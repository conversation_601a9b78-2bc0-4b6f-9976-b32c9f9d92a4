"""
认证管理模块：处理所有用户认证相关功能
"""

from pathlib import Path
from typing import List, Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal
from apis.auth_api import auth_api
from apis.perf_api import perf_api
from utils.common.file_utils import read_json, write_json
from utils.common.log_utils import get_logger
from core.settings_manager import settings_manager
import os

logger = get_logger(__name__)


class AuthManager(QObject):
    # 保留必要的信号
    logout_success = pyqtSignal()
    restart_requested = pyqtSignal()
    auth_success = pyqtSignal(dict)  # 改为发送字典对象

    # 添加一个标志位来防止重复恢复
    _session_restored = False

    def __init__(self):
        super().__init__()
        self._current_user: Optional[Dict] = None
        self._token: Optional[str] = None
        self.auth_api = auth_api
        
        # 从settings获取配置
        data_dir = Path(settings_manager.get('storage.paths.base.data', 'data'))  # 基础数据目录
        self._auth_file = data_dir / settings_manager.get('storage.paths.files.auth', 'auth.json')  # 认证文件名
        self._auth_data: Dict[str, Any] = {}
        self.auth_config = settings_manager.get('api.auth', {})
        
        # 确保目录存在
        self._auth_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化缓存
        self._business_list_cache = None  # 添加业务列表缓存属性
        self._token_cache = None  # token缓存
        self._user_info_cache = None  # 用户信息缓存
        self._client_id = None
        self._mac_address = None
        self._is_authenticated = False
        
        # 加载认证信息
        self._load_auth_info()

    def _load_auth_info(self) -> None:
        """加载认证信息"""
        try:
            # 如果认证文件不存在，创建默认结构
            if not self._auth_file.exists():
                default_auth = {
                    "current_user": None,
                    "token": None,
                    "client_id": None,
                    "mac_address": None
                }
                write_json(self._auth_file, default_auth)
                return

            # 读取认证文件
            auth_data = read_json(self._auth_file)
            if not auth_data:
                return

            # 设置认证信息
            current_user = auth_data.get('current_user')
            if current_user:
                # 验证必要字段
                required_fields = ['username', 'business_id', 'business_name']
                if all(field in current_user for field in required_fields):
                    self._current_user = current_user
                    self._token = auth_data.get('token')
                    self._client_id = auth_data.get('client_id')
                    self._mac_address = auth_data.get('mac_address')
                    self._is_authenticated = bool(self._current_user and self._token)
                    logger.info(f"已加载用户认证信息: {current_user.get('username')}")
                else:
                    logger.warning("用户信息缺少必要字段，重置认证状态")
                    self._clear_auth_data()
            else:
                logger.info("未找到当前用户信息")

        except Exception as e:
            logger.error(f"加载认证信息失败: {str(e)}")
            self._clear_auth_data()

    def get_current_user(self) -> Optional[Dict]:
        """获取当前用户信息"""
        return self._current_user

    def _try_restore_session(self) -> None:
        """尝试恢复登录会话"""
        try:
            auth_data = read_json(self._auth_file)
            if not auth_data:
                logger.warning("认证文件为空")
                return

            current_user = auth_data.get('current_user')
            token = auth_data.get('token')

            if current_user and token:
                self._current_user = current_user
                self._token = token
                # 同步 token 到 auth_api
                self.auth_api.token = token
                # 发送认证成功信号
                self.auth_success.emit(self._current_user)
                logger.info(f"成功恢复登录会话: {current_user.get('username')}")
            else:
                logger.warning("认证文件中缺少必要信息")

        except Exception as e:
            logger.error(f"恢复登录会话失败: {str(e)}")
            self._clear_auth_data()

    def _clear_auth_data(self) -> None:
        """清除认证数据"""
        try:
            self._current_user = None
            self._token = None
            # 保持文件中的原始结构，只清除值
            auth_data = {
                'current_user': None,
                'token': None,
                'client_id': None,
                'mac_address': None
            }
            write_json(self._auth_file, auth_data)
            logger.info("认证数据已清除")
        except Exception as e:
            logger.error(f"清除认证数据失败: {str(e)}")

    @property
    def is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        return bool(self._current_user and self._token)

    def set_current_user(self, user_info: Dict) -> bool:
        """设置当前户信息"""
        try:
            auth_data = read_json(self._auth_file) or {}
            auth_data['current_user'] = user_info
            write_json(self._auth_file, auth_data)
            logger.info(f"已设置当前用户: {user_info.get('username')}")
            return True
        except Exception as e:
            logger.error(f"设置当前用户信息失败: {str(e)}")
            return False

    def clear_current_user(self) -> bool:
        """清除当前用户信息"""
        try:
            auth_data = read_json(self._auth_file) or {}
            auth_data['current_user'] = None
            write_json(self._auth_file, auth_data)
            logger.info("已清除当前用户信息")
            return True
        except Exception as e:
            logger.error(f"清除当前用户信息失败: {str(e)}")
            return False

    def get_token(self) -> Optional[str]:
        """获取当前令牌"""
        try:
            # 从 auth.json 文件获取令牌
            if not self._auth_file.exists():
                logger.error(f"认证文件不存在: {self._auth_file}")
                return None
            
            auth_data = read_json(self._auth_file)
            token = auth_data.get('token')
            if not token:
                logger.error("认证文件中没有令牌信息")
                return None
            
            return token

        except Exception as e:
            logger.error(f"获取令牌失败: {str(e)}")
            return None

    def _save_auth(self, data: dict) -> bool:
        """保存认证信息到文件
        
        Args:
            data: 要保存的认证数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 读取现有数据
            auth_data = read_json(self._auth_file) or {}

            # 更新数据
            auth_data.update(data)

            # 如果有token，也保存它
            if self._token:
                auth_data['token'] = self._token

            # 写入文件
            write_json(self._auth_file, auth_data)
            logger.info("认证信息已保存")
            return True
            
        except Exception as e:
            logger.error(f"保存认证信息失败: {str(e)}")
            return False

    async def authenticate(self, username: str, business_name: str) -> bool:
        """用户认证
        
        Args:
            username: 用户名
            business_name: 业务名称
            
        Returns:
            bool: 是否认证成功
        """
        try:
            # 获取 JWT 令牌
            if not self._token:
                self._token = await self.auth_api.get_jwt_token()
                if not self._token:
                    logger.error("获取JWT令牌失败")
                    return False

            # 获取完整的用户信息
            user_info = await self.auth_api.get_user_info(username, self._token)
            if not user_info:
                logger.error("获取用户信息失败")
                return False

            # 获取业务ID
            business_id = None
            business_list = await self.get_business_list()
            if business_list:
                for business in business_list:
                    if business.get('business_name') == business_name:
                        business_id = business.get('id')
                        break

            if not business_id:
                logger.error(f"未找到业务 {business_name} 对应的ID")
                return False

            # 构建完整的用户信息
            complete_user_data = {
                "username": user_info.get('username', username),
                "business_id": business_id,  # 使用业务ID
                "business_name": business_name,  # 使用业务名称
                "email": user_info.get('email', f"{username}@bytedance.com"),
                "name": user_info.get('name', username),
                "department": user_info.get('department', {}).get('name', ''),
                "avatar_url": user_info.get('avatar_url', ''),
                "en_name": user_info.get('en_name', username)
            }

            # 设置当前用户
            self._current_user = complete_user_data

            # 保存认证信息
            if not self._save_auth({'current_user': complete_user_data}):
                logger.error("保存认证信息失败")
                return False

            # 发送认证成功信号
            self.auth_success.emit(self._current_user)

            logger.info(f"用户 {username} 认证成功")
            return True

        except Exception as e:
            logger.error(f"认证失败: {str(e)}")
            return False

    async def async_logout(self) -> bool:
        """异步执行登出"""
        try:
            # 清理API会话
            if hasattr(self, 'auth_api') and self.auth_api:
                await self.auth_api.close()

            # 清除缓存
            self.clear_cache()
            
            # 清除认证数据
            self._clear_auth_data()

            # 发送登出成功信号
            self.logout_success.emit()
            logger.info("用户登出成功")
            return True

        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
            return False

    async def get_business_list(self):
        """获取业务列表"""
        try:
            # 如果有缓存且未过期，直接返回缓存
            if self._business_list_cache is not None:
                return self._business_list_cache

            # 确保有token
            if not self._token:
                self._token = await self.auth_api.get_jwt_token()
                if not self._token:
                    logger.error("获取JWT令牌失败")
                    return []

            # 从perf_api获取业务列表,返回字典列表
            businesses = await perf_api.get_business_list()

            # 更新缓存
            self._business_list_cache = businesses

            return businesses
        except Exception as e:
            logger.error(f"获取业务列表失败: {str(e)}")
            return []

    def clear_business_list_cache(self) -> None:
        """清除业务列表缓存"""
        self._business_list_cache = None

    async def search_users(self, search_text: str) -> List[str]:
        """搜索用户"""
        try:
            if not search_text:
                return []

            # 使用 auth_api 获取用户列表
            users = await self.auth_api.get_user_list(search_text)
            return users

        except Exception as e:
            logger.error(f"搜索用户失败: {str(e)}")
            return []

    def clear_cache(self):
        """清除所有缓存"""
        self._business_list_cache = None
        self._token_cache = None
        self._user_info_cache = None


auth_manager = AuthManager()