"""工具提示样式定义"""
from ..base.colors import Colors

class TooltipStyle:
    @classmethod
    def get_tooltip_style(cls) -> str:
        """获取工具提示样式"""
        return f"""
            QToolTip {{
                background-color: {Colors.SURFACE};
                color: {Colors.TEXT_PRIMARY};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
            }}
        """ 