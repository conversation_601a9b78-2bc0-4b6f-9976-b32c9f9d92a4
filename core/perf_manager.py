"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-14 13:52:38
FilePath: /global_rtc_client/ui/core/perf_manager.py
Description:
"""
"""
性能管理模块：处理性能相关功能
"""
import json
import os
import platform
import subprocess
import time
import asyncio
from pathlib import Path
from typing import List, Optional, Dict, Any
from apis.auth_api import auth_api
from apis.perf_api import perf_api
from config.constants import (
    ServiceType, ServiceStatus, 
    ClientState, PlatformType
)
from core.auth_manager import auth_manager
from utils.common.file_utils import read_json, write_json
from utils.common.android_device import android_device
from utils.common.ios_device import ios_device
from utils.common.system_utils import SystemUtils
from utils.perf.app_control import app_control
from utils.perf.task_control import task_control
from utils.perf.serial_control import serial_control
from config.constants import TaskStatus
from core.settings_manager import settings_manager
from PyQt6.QtWidgets import QApplication

from utils.common.log_utils import get_logger
logger = get_logger(__name__)

class BaseService:
    """基础服务类，提供通用的服务管理功能"""
    
    def __init__(self, service_type: str, manager):
        self.service_type = service_type
        self.manager = manager
        self._status = ServiceStatus.STOPPED.value
        self._tasks = []
        self._start_time = 0
        self._stop_time = 0

    @property
    def status(self):
        return self._status

    @status.setter
    def status(self, value):
        self._status = value
        if hasattr(self.manager, '_services'):
            self.manager._services[self.service_type] = value

    async def start(self) -> bool:
        """启动服务"""
        try:
            self._start_time = time.perf_counter()
            logger.info(f"开始启动{self.service_type}服务...")
            
            # 验证用户认证状态
            if not auth_manager.get_current_user():
                logger.error("未获取到用户信息，请确保已登录")
                return False
                
            # 验证客户端ID（如果需要）
            if self.service_type in [ServiceType.DEVICE.value, ServiceType.TASK.value]:
                client_id = await self.manager._ensure_client_id()
                if not client_id:
                    logger.error("未获取到客户端ID，请先启动性能机房服务")
                    return False
            
            self.status = ServiceStatus.STARTING.value
            if await self._do_start():
                self.status = ServiceStatus.RUNNING.value
                elapsed_time = (time.perf_counter() - self._start_time) * 1000
                logger.info(f"{self.service_type}服务启动完成，耗时: {elapsed_time:.2f}ms")
                return True
                
            self.status = ServiceStatus.FAILED.value
            elapsed_time = (time.perf_counter() - self._start_time) * 1000
            logger.error(f"{self.service_type}服务启动失败，耗时: {elapsed_time:.2f}ms")
            return False
            
        except Exception as e:
            logger.error(f"启动{self.service_type}服务失败: {str(e)}")
            self.status = ServiceStatus.FAILED.value
            return False

    async def stop(self) -> bool:
        """停止服务"""
        try:
            self._stop_time = time.perf_counter()
            logger.info(f"开始停止{self.service_type}服务...")
            
            self.status = ServiceStatus.STOPPING.value
            if await self._do_stop():
                self.status = ServiceStatus.STOPPED.value
                elapsed_time = (time.perf_counter() - self._stop_time) * 1000
                logger.info(f"{self.service_type}服务停止完成，耗时: {elapsed_time:.2f}ms")
                return True
                
            self.status = ServiceStatus.ERROR.value
            elapsed_time = (time.perf_counter() - self._stop_time) * 1000
            logger.error(f"{self.service_type}服务停止失败，耗时: {elapsed_time:.2f}ms")
            return False
            
        except Exception as e:
            logger.error(f"停止{self.service_type}服务失败: {str(e)}")
            self.status = ServiceStatus.ERROR.value
            return False

    async def _do_start(self) -> bool:
        """具体的启动逻辑，由子类实现"""
        raise NotImplementedError

    async def _do_stop(self) -> bool:
        """具体的停止逻辑，由子类实现"""
        raise NotImplementedError

    def add_task(self, task):
        """添加异步任务"""
        self._tasks.append(task)

    async def cancel_tasks(self):
        """取消所有相关任务"""
        try:
            # 并行取消所有任务
            cancel_tasks = []
            for task in self._tasks:
                if task and not task.done():
                    try:
                        task.cancel()
                        cancel_tasks.append(task)
                    except Exception as e:
                        logger.error(f"取消任务失败: {str(e)}")
            
            if cancel_tasks:
                try:
                    # 等待所有任务取消完成
                    await asyncio.gather(*cancel_tasks, return_exceptions=True)
                except Exception as e:
                    logger.warning(f"等待任务取消时出错: {str(e)}")
            
            self._tasks.clear()
            
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            # 确保任务列表被清空
            self._tasks.clear()

class RoomService(BaseService):
    """机房服务类"""
    
    def __init__(self, manager):
        super().__init__(ServiceType.CLIENT.value, manager)
        self._init_task = None
        self._client_info_cache = None
        self._last_update_time = 0

    async def _do_start(self) -> bool:
        """启动服务"""
        try:
            # 1. 首先尝试从本地缓存获取客户端信息
            auth_data = read_json(self.manager.auth_file) or {}
                
            # 2. 如果没有缓存，获取MAC地址
            mac_address = SystemUtils.get_mac_address()
            if not mac_address:
                logger.error("获取MAC地址失败")
                return False

            # 3. 并行获取客户端详情和准备客户端信息
            client_detail_task = perf_api.get_client_detail(mac_address=mac_address)
            client_info_task = self.manager._get_client_info(ClientState.ONLINE)
            
            client_detail, client_info = await asyncio.gather(
                client_detail_task,
                client_info_task
            )

            if client_detail:
                # 4a. 如果客户端已存在，更新状态和地址信息
                logger.info("找到已存在的客户端信息，更新状态和地址信息")
                self.manager._client_id = client_detail.get('id')

                # 获取最新的地址信息
                if client_info:
                    # 异步更新状态、IPv4和IPv6地址
                    success, msg = await perf_api.update_client_info(
                        client_id=self.manager._client_id,
                        state=ClientState.ONLINE.value,
                        ipv4=client_info.get('ipv4', ''),
                        ipv6=client_info.get('ipv6', '')
                    )

                    if success:
                        self.manager._client_state = ClientState.ONLINE
                        logger.info("客户端状态和地址信息更新成功")
                    else:
                        logger.error(f"更新客户端信息失败: {msg}")
                else:
                    # 如果无法获取客户端信息，记录错误但不阻止流程继续
                    logger.error("无法获取客户端信息，跳过地址更新")
            else:
                # 4b. 如果客户端不存在，注册新客户端
                if not client_info:
                    logger.error("获取本地客户端信息失败")
                    return False

                success, msg = await perf_api.register_perf_client(client_info)
                if not success:
                    logger.error(f"注册客户端失败: {msg}")
                    return False

                # 获取新注册的客户端详情
                client_detail = await perf_api.get_client_detail(mac_address=mac_address)
                if not client_detail:
                    logger.error("获取新注册的客户端详情失败")
                    return False
                
                self.manager._client_id = client_detail.get('id')

            # 5. 保存客户端信息到本地
            if not self.manager._client_id:
                logger.error("获取客户端ID失败")
                return False

            auth_data.update({
                'client_id': self.manager._client_id,
                'mac_address': mac_address
            })
            write_json(self.manager.auth_file, auth_data)

            # 6. 更新状态和缓存
            self.manager._client_state = ClientState.ONLINE
            self.manager._last_client_info = client_detail
            
            logger.info("客户端信息初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"启动服务失败: {str(e)}")
            return False

    async def _do_stop(self) -> bool:
        """停止服务"""
        try:
            # 立即取消所有任务
            await self.cancel_tasks()
            
            # 更新客户端状态
            if self.manager._client_id:
                await perf_api.update_client_info(
                    client_id=self.manager._client_id,
                    state=ClientState.OFFLINE.value
                )
            
            # 清理客户端相关资源
            self.manager._client_state = ClientState.OFFLINE
            self.manager._last_client_info = None
            
            return True
        except Exception as e:
            logger.error(f"停止服务失败: {str(e)}")
            return False

class DeviceMonitorService(BaseService):
    """设备监控服务类"""
    
    def __init__(self, manager):
        super().__init__(ServiceType.DEVICE.value, manager)
        self._monitor_task = None
        self._upload_task = None
        self._upload_queue = asyncio.Queue()
        self._last_devices = None  # 存储上次的设备列表

    async def _do_start(self) -> bool:
        """启动设备监控服务"""
        try:
            # 清理iOS设备初始化记录
            self.manager.ios_device.clear_initialized_devices()
            
            # 创建并启动监控任务
            self._monitor_task = asyncio.create_task(self._device_monitor_loop(), name="device-monitor-loop")
            self._upload_task = asyncio.create_task(self._device_upload_loop(), name="device-upload-loop")
            self.add_task(self._monitor_task)
            self.add_task(self._upload_task)
            
            return True
        except Exception as e:
            logger.error(f"启动设备监控服务失败: {str(e)}")
            return False

    async def _do_stop(self) -> bool:
        """停止服务"""
        try:
            # 立即取消所有任务
            await self.cancel_tasks()
            
            # 异步更新设备状态
            await self._upload_empty_device_list()
            
            # 清理设备相关资源
            self._last_devices = None
            
            # 清理iOS设备初始化记录
            self.manager.ios_device.clear_initialized_devices()
            
            return True
        except Exception as e:
            logger.error(f"停止设备监控失败: {str(e)}")
            return False

    async def _upload_empty_device_list(self):
        """异步上传空设备列表"""
        try:
            await self._upload_device_info([])
        except Exception as e:
            logger.error(f"异步上传空设备列表失败: {str(e)}")

    async def _get_device_list(self) -> List[Dict]:
        """获取设备列表"""
        try:
            # 加载串口配置
            serial_config = {'serial_mappings': []}
            if os.path.exists(self.manager._serial_config_file):
                with open(self.manager._serial_config_file, 'r', encoding='utf-8') as f:
                    serial_config = json.load(f)
            
            # 创建 UDID 到串口的映射
            udid_port_map = {
                item.get('udid'): item.get('port')
                for item in serial_config.get('serial_mappings', [])
                if item.get('udid') and item.get('port')  # 确保 udid 和 port 都存在
            }

            # 并行获取 Android 和 iOS 设备
            android_devices, ios_devices = await asyncio.gather(
                android_device.get_android_devices(),  # 现在直接调用异步方法
                ios_device.get_ios_devices()  # 现在直接调用异步方法
            )

            devices = []
            if android_devices:
                devices.extend(android_devices)
            if ios_devices:
                devices.extend(ios_devices)

            # 为每个设备添加串口号
            for device in devices:
                # 直接访问对象属性而不是使用get方法
                udid = device.udid
                if udid and udid in udid_port_map:
                    device.serial_port = udid_port_map[udid]

            # 添加设备列表为空的日志
            if not devices:
                logger.warning("未检测到任何设备")

            return devices
        except Exception as e:
            logger.error(f"获取设备列表失败: {str(e)}")
            return []

    async def _upload_device_info(self, devices: List[Dict]) -> bool:
        """上传设备信息"""
        try:
            success = await perf_api.upload_device_info(
                client_id=self.manager._client_id,
                device_list=devices
            )
            
            if success:
                return True
            else:
                logger.error("上传设备信息失败")
                return False
        except Exception as e:
            logger.error(f"上传设备信息失败: {str(e)}")
            return False

    async def _device_monitor_loop(self):
        """设备监控循环"""
        try:
            while self.status == ServiceStatus.RUNNING.value:
                try:
                    # 获取当前设备列表
                    current_devices = await self._get_device_list()
                    
                    # 直接上传设备信息
                    if current_devices:
                        await self._upload_queue.put(current_devices)
                    
                    # 等待轮询间隔
                    await asyncio.sleep(self.manager._poll_intervals[self.service_type])
                    
                except asyncio.CancelledError:
                    raise
                except Exception as e:
                    logger.error(f"设备监控异常: {str(e)}")
                    await asyncio.sleep(5)
                    
        except asyncio.CancelledError:
            logger.info("设备监控循环被取消")
            raise

    async def _device_upload_loop(self):
        """设备信息上传循环"""
        try:
            while self.status == ServiceStatus.RUNNING.value:
                try:
                    devices = await self._upload_queue.get()
                    await self._upload_device_info(devices)
                    self._upload_queue.task_done()
                except asyncio.CancelledError:
                    raise
                except Exception as e:
                    logger.error(f"设备信息上传异常: {str(e)}")
                    await asyncio.sleep(1)
        except asyncio.CancelledError:
            logger.info("设备上传循环被取消")
            raise

class TaskPollingService(BaseService):
    """任务轮询服务类"""
    
    def __init__(self, manager):
        super().__init__(ServiceType.TASK.value, manager)

    async def _do_start(self) -> bool:
        """启动服务"""
        try:
            # 启动任务轮询
            poll_task = asyncio.create_task(self._task_poll_loop(), name="task-polling-loop")
            self.add_task(poll_task)
            return True
        except Exception as e:
            logger.error(f"启动任务轮询失败: {str(e)}")
            return False

    async def _do_stop(self) -> bool:
        """停止服务"""
        try:
            # 1. 立即取消所有任务
            await self.cancel_tasks()
            
            # 2. 终止正在运行的任务并等待完成
            await self.manager.task_control.terminate_sub_task()
            
            # 3. 释放资源并等待完成
            task_file = Path(
                settings_manager.get('storage.paths.base.data', 'data'),
                settings_manager.get('storage.paths.files.task', 'task.json')
            )
            
            task_detail = read_json(task_file)
            if task_detail:
                await self.manager.task_control.task_helper._release_resources()
            
            return True
        except Exception as e:
            logger.error(f"停止任务轮询失败: {str(e)}")
            return False

    async def _task_poll_loop(self):
        """任务轮询循环"""
        while self.status == ServiceStatus.RUNNING.value:
            try:
                sub_task_context = await perf_api.get_head_sub_task(self.manager._client_id)
                if sub_task_context:
                    await self._process_sub_task(sub_task_context)
                await asyncio.sleep(self.manager._poll_intervals[self.service_type])
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"任务轮询异常: {str(e)}")

    async def _process_sub_task(self, sub_task_context: Dict):
        """处理子任务"""
        try:
            self.manager.task_control.sub_task_context = sub_task_context
            task_status = sub_task_context.get('status')
            
            if task_status == TaskStatus.RETRY_PENDING.value:
                await self.manager.task_control.retry_sub_task()
            elif task_status in [TaskStatus.NOT_START.value, TaskStatus.PENDING.value]:
                await self.manager.task_control.start_sub_task()
            else:
                # 对于其他状态（如已取消、已完成等），从队列中移除该子任务
                logger.info(f"子任务状态为 {TaskStatus.get_name(task_status) if hasattr(TaskStatus, 'get_name') else task_status}，从队列中移除")
                await perf_api.remove_head_sub_task(self.manager._client_id)
                
        except Exception as e:
            logger.error(f"处理子任务失败: {str(e)}")

class RequestPollingService(BaseService):
    """性能请求接收服务类"""
    
    def __init__(self, manager):
        super().__init__(ServiceType.REQUEST.value, manager)
        self._poll_task = None

    async def _do_start(self) -> bool:
        """启动服务"""
        try:
            # 启动请求轮询
            self._poll_task = asyncio.create_task(self._request_poll_loop())
            self.add_task(self._poll_task)
            return True
        except Exception as e:
            logger.error(f"启动请求轮询失败: {str(e)}")
            return False

    async def _do_stop(self) -> bool:
        """停止服务"""
        try:
            start_time = time.perf_counter()
            
            await self.cancel_tasks()
            
            elapsed_time = (time.perf_counter() - start_time) * 1000
            logger.info(f"请求轮询服务停止完成，耗时: {elapsed_time:.2f}ms")
            return True
            
        except Exception as e:
            logger.error(f"停止请求轮询失败: {str(e)}")
            return False

    async def _handle_perf_request(self, request: Dict) -> None:
        """处理性能请求
        
        Args:
            request: 性能请求数据
        """
        try:
            event_key = request.get('event_key')
            request_body = request.get('request_body', {})
            
            # 处理取消任务请求
            if event_key == 'perf_request_cancel_task':
                task_id = request_body.get('task_id')
                if task_id is not None:
                    logger.info(f"收到取消任务请求: task_id={task_id}")
                    
                    # 判断是否是当前正在执行的任务
                    if getattr(self.manager.task_control, 'task_id', None) == task_id:
                        await self.manager.task_control.terminate_sub_task()
                    else:
                        logger.info(f"任务 ID 不匹配，当前执行的任务 ID: {getattr(self.manager.task_control, 'task_id', None)}")
                else:
                    logger.warning("取消任务请求中未找到 task_id")
            else:
                logger.info(f"未知的请求类型: {event_key}")
        except Exception as e:
            logger.error(f"处理请求异常: {str(e)}")

    async def _request_poll_loop(self):
        """请求轮询循环"""
        while self.status == ServiceStatus.RUNNING.value:
            try:
                # 获取所有性能请求
                requests = await perf_api.get_all_perf_requests(self.manager._client_id)
                
                # 判断请求列表是否为空
                if requests:
                    logger.info(f"收到 {len(requests)} 个性能请求")
                    
                    # 处理每个请求
                    for request in requests:
                        await self._handle_perf_request(request)
                    
                    # 清空请求队列
                    await perf_api.clear_perf_request_queue(self.manager._client_id)
                
                await asyncio.sleep(self.manager._poll_intervals[self.service_type])
                    
            except asyncio.CancelledError:
                logger.info("请求轮询任务被取消")
                raise
            except Exception as e:
                logger.error(f"请求轮询异常: {str(e)}")

class PerfManager:
    """性能管理器"""

    def __init__(self):
        """初始化性能管理器"""
        try:
            # 初始化服务状态
            self._services = {
                ServiceType.SCREEN_LOCK.value: ServiceStatus.STOPPED.value,
                ServiceType.CLIENT.value: ServiceStatus.STOPPED.value,
                ServiceType.DEVICE.value: ServiceStatus.STOPPED.value,
                ServiceType.TASK.value: ServiceStatus.STOPPED.value,
                ServiceType.REQUEST.value: ServiceStatus.STOPPED.value  # 添加新服务状态
            }
            
            # 初始化服务实例
            self._service_instances = {
                ServiceType.CLIENT.value: RoomService(self),
                ServiceType.DEVICE.value: DeviceMonitorService(self),
                ServiceType.TASK.value: TaskPollingService(self),
                ServiceType.REQUEST.value: RequestPollingService(self)  # 添加新服务实例
            }
            
            self._tasks = []
            self._client_id = None
            self._client_state = ClientState.OFFLINE
            self._last_client_info = None
            self._caffeinate_process = None
            
            # 使用配置中定义的路径
            self.auth_file = Path(
                settings_manager.get('storage.paths.base.data', 'data'),
                settings_manager.get('storage.paths.files.auth', 'auth.json')
            )
            
            self._serial_config_file = Path(
                settings_manager.get('storage.paths.base.data', 'data'),
                settings_manager.get('storage.paths.files.serial', 'serial.json')
            )
            
            self._poll_intervals = {
                ServiceType.DEVICE.value: settings_manager.get('poll_interval.device', 20),
                ServiceType.TASK.value: settings_manager.get('poll_interval.task', 5),
                ServiceType.REQUEST.value: settings_manager.get('poll_interval.request', 5),  # 添加新服务轮询间隔
            }

            # 初始化API和其他组件
            self.auth_api = auth_api
            self.serial_control = serial_control
            self.android_device = android_device
            self.ios_device = ios_device
            self.app_control = app_control
            self.task_control = task_control

        except Exception as e:
            logger.error(f"初始化性能管理器失败: {str(e)}")
            raise

    async def start_service(self, service_type: str) -> bool:
        """启动服务"""
        try:
            if service_type == ServiceType.SCREEN_LOCK.value:
                return await self._prevent_screen_lock()
                
            service = self._service_instances.get(service_type)
            if not service:
                logger.error(f"无效的服务类型: {service_type}")
                return False
                
            return await service.start()
        except Exception as e:
            logger.error(f"启动服务失败: {str(e)}")
            return False

    async def stop_service(self, service_type: str) -> bool:
        """停止服务"""
        try:
            if service_type == ServiceType.SCREEN_LOCK.value:
                return await self._allow_screen_lock()
                
            service = self._service_instances.get(service_type)
            if not service:
                logger.error(f"无效的服务类型: {service_type}")
                return False
            
            try:
                return await service.stop()
            except asyncio.InvalidStateError as e:
                logger.warning(f"停止{service_type}服务时出现事件循环错误: {str(e)}")
                # 强制更新服务状态
                service.status = ServiceStatus.STOPPED.value
                return True
                
        except Exception as e:
            logger.error(f"停止服务失败: {str(e)}")
            return False

    def get_service_status(self, service_type: str) -> ServiceStatus:
        """获取服务状态"""
        try:
            if service_type not in self._services:
                logger.error(f"无效的服务类型: {service_type}")
                return ServiceStatus.STOPPED

            status_value = self._services[service_type]
            return ServiceStatus(status_value)

        except Exception as e:
            logger.error(f"获取服务状态失败: {str(e)}")
            return ServiceStatus.STOPPED

    async def start_all_services(self) -> bool:
        """按顺序启动所有服务"""
        try:
            logger.info("开始启动所有服务...")
            
            # 验证用户认证状态
            if not auth_manager.get_current_user():
                logger.error("未获取到用户信息，请确保已登录")
                return False
            
            # 1. 启动屏幕锁定防护服务和性能机房服务
            screen_lock_task = self.start_service(ServiceType.SCREEN_LOCK.value)
            client_task = self.start_service(ServiceType.CLIENT.value)
            
            # 并行启动屏幕锁定和客户端服务
            results = await asyncio.gather(screen_lock_task, client_task)
            if not all(results):
                raise Exception("基础服务启动失败")
                        
            # 等待客户端服务就绪
            await self._wait_service_ready(ServiceType.CLIENT.value, timeout=10)
            
            # 2. 并行启动其他服务
            device_task = self.start_service(ServiceType.DEVICE.value)
            task_polling_task = self.start_service(ServiceType.TASK.value)
            request_task = self.start_service(ServiceType.REQUEST.value)
            
            results = await asyncio.gather(device_task, task_polling_task, request_task)
            if not all(results):
                raise Exception("监控、任务和请求服务启动失败")
            
            # 不再等待设备监控服务就绪，让它自然运行
            logger.info("所有服务启动完成")
            return True

        except Exception as e:
            logger.error(f"启动服务失败: {str(e)}")
            # 如果启动失败,停止已启动的服务
            await self.stop_all_services()
            return False

    async def stop_all_services(self) -> bool:
        """停止所有服务"""
        try:
            logger.info("开始停止所有服务...")
            
            # 按特定顺序停止服务，确保依赖关系
            services_to_stop = [
                ServiceType.TASK.value,
                ServiceType.REQUEST.value,
                ServiceType.DEVICE.value,
                ServiceType.CLIENT.value,
                ServiceType.SCREEN_LOCK.value
            ]
            
            all_success = True
            for service_type in services_to_stop:
                if self._services[service_type] != ServiceStatus.STOPPED.value:
                    try:
                        success = await self.stop_service(service_type)
                        if not success:
                            logger.error(f"停止{service_type}服务失败")
                            all_success = False
                    except Exception as e:
                        logger.error(f"停止{service_type}服务时发生异常: {str(e)}")
                        all_success = False
            
            logger.info("所有服务已停止")
            return all_success
            
        except Exception as e:
            logger.error(f"停止服务失败: {str(e)}")
            return False

    async def cleanup_all_resources(self) -> None:
        """清理所有资源，用于应用退出、重启等场景"""
        try:
            logger.info("开始清理所有资源...")
            
            # 1. 确保所有任务被取消
            for service in self._service_instances.values():
                await service.cancel_tasks()
            
            # 2. 停止所有服务并等待完成
            await self.stop_all_services()
            
            # 3. 等待所有服务完全停止
            for service_type in list(self._services.keys()):
                await self._wait_service_stopped(service_type, timeout=10)
            
            # 4. 清除缓存
            self._client_id = None
            self._client_state = ClientState.OFFLINE
            self._last_client_info = None
            
            logger.info("所有资源清理完成")
            
        except Exception as e:
            logger.error(f"清理资源时发生错误: {str(e)}")
            logger.exception(e)

    async def _wait_service_ready(self, service_type: str, timeout: int = 30) -> bool:
        """等待服务就绪"""
        try:
            start_time = time.time()
            check_interval = 0.5  # 减少检查间隔
            
            while time.time() - start_time < timeout:
                if service_type == ServiceType.CLIENT.value:
                    if (self._services[service_type] == ServiceStatus.RUNNING.value and 
                        self._client_id):
                        return True
                        
                elif service_type == ServiceType.DEVICE.value:
                    if (self._services[service_type] == ServiceStatus.RUNNING.value and
                        await self._check_device_monitor_ready()):
                        return True
                        
                await asyncio.sleep(check_interval)
                
            raise TimeoutError(f"{service_type}服务未在{timeout}秒内就绪")
            
        except Exception as e:
            logger.error(f"等待服务就绪失败: {str(e)}")
            raise

    async def _check_device_monitor_ready(self) -> bool:
        """检查设备监控服务是否就绪"""
        try:
            service = self._service_instances.get(ServiceType.DEVICE.value)
            if not service:
                return False
                
            # 获取MAC地址
            mac_address = SystemUtils.get_mac_address()
            if not mac_address:
                return False
            
            # 不再获取设备列表，只检查服务状态
            return True
            
        except Exception as e:
            logger.error(f"检查设备监控服务就绪状态失败: {str(e)}")
            return False
        
    async def _prevent_screen_lock(self) -> bool:
        """阻止屏幕锁定"""
        try:
            if self._caffeinate_process:
                return True

            caffeinate_path = "/usr/bin/caffeinate"
            if not os.path.exists(caffeinate_path):
                logger.error("caffeinate命令不存在")
                return False

            self._caffeinate_process = subprocess.Popen(
                [caffeinate_path, "-d", "-i", "-m"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False
            )

            if self._caffeinate_process.poll() is None:
                logger.info("已启动屏幕锁定防护")
                self._services[ServiceType.SCREEN_LOCK.value] = ServiceStatus.RUNNING.value
                return True
            else:
                logger.error("启动屏幕锁定防护失败")
                return False

        except Exception as e:
            logger.error(f"启动屏幕锁定防护失败: {str(e)}")
            return False

    async def _allow_screen_lock(self) -> bool:
        """允许屏幕锁定"""
        try:
            if self._caffeinate_process:
                self._caffeinate_process.terminate()
                try:
                    self._caffeinate_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self._caffeinate_process.kill()
                self._caffeinate_process = None

                logger.info("已关闭屏幕锁定防护")
                self._services[ServiceType.SCREEN_LOCK.value] = ServiceStatus.STOPPED.value
                return True

            return True

        except Exception as e:
            logger.error(f"关闭屏幕锁定防护失败: {str(e)}")
            return False

    async def _ensure_client_id(self) -> Optional[int]:
        """确保获取到客户端ID"""
        if self._client_id is not None:
            return self._client_id

        auth_data = read_json(self.auth_file)
        client_id = auth_data.get('client_id')
        
        if not client_id:
            logger.error("未找到客户端ID，请先启动性能机房服务")
            return None

        self._client_id = client_id
        logger.info(f"成功获取客户端ID: {client_id}")
        return client_id

    async def _get_client_info(self, state: ClientState = ClientState.ONLINE) -> Optional[Dict[str, Any]]:
        """获取注册客户端所需的基本信息"""
        try:
            # 1. 获取当前用户信息
            current_user = auth_manager.get_current_user()
            if not current_user:
                logger.error("未获取到用户信息")
                return None
            
            # 2. 获取MAC地址
            mac_address = SystemUtils.get_mac_address()
            if not mac_address:
                logger.error("获取MAC地址失败")
                return None

            # 3. 获取IP地址
            ipv4, ipv6 = SystemUtils.get_ip_addresses()

            # 4. 获取系统类型
            system_type = platform.system()
            sys_type_map = {
                'Windows': PlatformType.WINDOWS.value,
                'Darwin': PlatformType.MACOS.value,
                'Linux': PlatformType.LINUX.value
            }
            sys_type = sys_type_map.get(system_type, PlatformType.MACOS.value)

            # 5. 构建客户端信息
            client_info = {
                'business_id': current_user.get('business_id'),
                'owner': current_user.get('username'),
                'sys_type': sys_type,
                'mac_address': mac_address,
                'ipv4': ipv4 or "",
                'ipv6': ipv6 or "",
                'port': settings_manager.get('client.port', 8080),
                'state': state.value
            }

            return client_info
        except Exception as e:
            logger.error(f"获取客户端信息失败: {str(e)}")
            return None

    async def save_serial_config(self, config: dict) -> bool:
        """保存串口配置"""
        try:
            os.makedirs(os.path.dirname(self._serial_config_file), exist_ok=True)
            with open(self._serial_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存串口配置失败: {str(e)}")
            return False

    def get_screen_list(self) -> List[Dict]:
        """获取当前系统连接的所有屏幕"""
        try:
            screens = []
            for screen in QApplication.screens():
                geometry = screen.geometry()
                screen_info = {
                    'name': screen.name(),
                    'geometry': {
                        'x': geometry.x(),
                        'y': geometry.y(),
                        'width': geometry.width(),
                        'height': geometry.height()
                    },
                    'display_text': f"{screen.name()} ({geometry.width()}x{geometry.height()})"
                }
                screens.append(screen_info)
            return screens
        except Exception as e:
            logger.error(f"获取屏幕列表失败: {str(e)}")
            return []

    def set_selected_screen(self, screen_info: Dict) -> None:
        """设置选中的屏幕"""
        try:
            self._selected_screen = screen_info
            logger.info(f"已选择屏幕: {screen_info['name']}")
        except Exception as e:
            logger.error(f"设置选中屏幕失败: {str(e)}")

    async def _wait_service_stopped(self, service_type: str, timeout: int = 10) -> bool:
        """等待服务完全停止"""
        try:
            start_time = time.time()
            check_interval = 0.5
            
            while time.time() - start_time < timeout:
                if self._services[service_type] == ServiceStatus.STOPPED.value:
                    return True
                await asyncio.sleep(check_interval)
            
            logger.warning(f"{service_type}服务未在{timeout}秒内完全停止")
            return False
            
        except Exception as e:
            logger.error(f"等待服务停止失败: {str(e)}")
            return False


perf_manager = PerfManager()