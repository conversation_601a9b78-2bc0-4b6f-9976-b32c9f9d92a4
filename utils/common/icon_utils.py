"""
图标工具类
提供应用图标的加载和管理功能
"""

import os
from pathlib import Path
from typing import Optional

from PyQt6.QtCore import QSize
from PyQt6.QtGui import QIcon, QPixmap
from utils.common.log_utils import get_logger

logger = get_logger(__name__)


class IconManager:
    """图标管理器"""
    
    def __init__(self):
        """初始化图标管理器"""
        self.base_path = Path(__file__).parent.parent.parent
        self.icons_dir = self.base_path / "resources" / "icons"
        self._app_icon = None
        
        # 确保图标目录存在
        self.icons_dir.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"图标目录: {self.icons_dir}")
    
    def get_app_icon(self) -> QIcon:
        """
        获取应用程序图标
        
        Returns:
            QIcon: 应用程序图标
        """
        if self._app_icon is not None:
            return self._app_icon
        
        # 尝试加载不同格式的图标文件
        icon_files = [
            "app_icon.icns",  # macOS 原生格式
            "app_icon.png",   # PNG 格式
            "app_icon.ico",   # Windows 格式
            "icon.icns",
            "icon.png",
            "icon.ico"
        ]
        
        for icon_file in icon_files:
            icon_path = self.icons_dir / icon_file
            if icon_path.exists():
                try:
                    self._app_icon = QIcon(str(icon_path))
                    if not self._app_icon.isNull():
                        logger.info(f"成功加载应用图标: {icon_path}")
                        return self._app_icon
                except Exception as e:
                    logger.warning(f"加载图标文件失败 {icon_path}: {str(e)}")
        
        # 如果没有找到图标文件，创建默认图标
        self._app_icon = self._create_default_icon()
        logger.info("使用默认应用图标")
        return self._app_icon
    
    def _create_default_icon(self) -> QIcon:
        """
        创建默认图标
        
        Returns:
            QIcon: 默认图标
        """
        try:
            # 创建一个简单的默认图标
            icon = QIcon()
            
            # 尝试使用系统图标
            if hasattr(QIcon, 'fromTheme'):
                system_icon = QIcon.fromTheme("application-x-executable")
                if not system_icon.isNull():
                    return system_icon
            
            # 创建一个空图标作为最后的备选
            return icon
            
        except Exception as e:
            logger.error(f"创建默认图标失败: {str(e)}")
            return QIcon()
    
    def get_icon(self, icon_name: str, size: Optional[QSize] = None) -> QIcon:
        """
        获取指定名称的图标
        
        Args:
            icon_name: 图标名称（不包含扩展名）
            size: 图标尺寸
            
        Returns:
            QIcon: 图标对象
        """
        try:
            # 支持的图标格式
            extensions = ['.png', '.svg', '.ico', '.icns']
            
            for ext in extensions:
                icon_path = self.icons_dir / f"{icon_name}{ext}"
                if icon_path.exists():
                    icon = QIcon(str(icon_path))
                    if not icon.isNull():
                        logger.debug(f"加载图标: {icon_path}")
                        return icon
            
            logger.warning(f"未找到图标: {icon_name}")
            return QIcon()
            
        except Exception as e:
            logger.error(f"加载图标失败 {icon_name}: {str(e)}")
            return QIcon()
    
    def create_app_icon_from_text(self, text: str = "GB", size: int = 512) -> QIcon:
        """
        从文本创建应用图标（用于开发测试）
        
        Args:
            text: 图标文本
            size: 图标尺寸
            
        Returns:
            QIcon: 创建的图标
        """
        try:
            from PyQt6.QtGui import QPainter, QFont, QColor, QBrush
            from PyQt6.QtCore import Qt
            
            # 创建像素图
            pixmap = QPixmap(size, size)
            pixmap.fill(QColor(70, 130, 180))  # 钢蓝色背景
            
            # 创建画笔
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 设置字体
            font = QFont("Arial", size // 4, QFont.Weight.Bold)
            painter.setFont(font)
            
            # 设置文字颜色
            painter.setPen(QColor(255, 255, 255))  # 白色文字
            
            # 绘制文字
            painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, text)
            painter.end()
            
            # 保存图标到文件
            icon_path = self.icons_dir / "app_icon.png"
            pixmap.save(str(icon_path))
            logger.info(f"创建默认图标: {icon_path}")
            
            return QIcon(pixmap)
            
        except Exception as e:
            logger.error(f"创建文本图标失败: {str(e)}")
            return QIcon()
    
    def get_icon_info(self) -> dict:
        """
        获取图标信息
        
        Returns:
            dict: 图标信息
        """
        info = {
            "icons_dir": str(self.icons_dir),
            "icons_dir_exists": self.icons_dir.exists(),
            "available_icons": [],
            "app_icon_loaded": self._app_icon is not None
        }
        
        if self.icons_dir.exists():
            # 扫描可用的图标文件
            for icon_file in self.icons_dir.glob("*"):
                if icon_file.is_file() and icon_file.suffix.lower() in ['.png', '.ico', '.icns', '.svg']:
                    info["available_icons"].append(icon_file.name)
        
        return info


# 全局图标管理器实例
icon_manager = IconManager()
