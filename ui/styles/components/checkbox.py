"""复选框样式定义"""
from ..base.colors import Colors

class CheckboxStyle:
    @classmethod
    def get_perf_window_checkbox_style(cls) -> str:
        """获取性能窗口复选框样式"""
        return f"""
            QCheckBox::indicator {{
                width: 40px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid {Colors.BORDER};
                background: transparent;
            }}
            QCheckBox::indicator:checked {{
                background: {Colors.SUCCESS};
                border: none;
            }}
        """ 