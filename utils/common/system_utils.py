"""系统工具模块"""
import platform
import re
import subprocess
import uuid
from typing import Tuple

from utils.common.log_utils import get_logger

logger = get_logger(__name__)

# 常量定义
EXCLUDED_IPV4_PREFIXES = ('127.', '169.254.', '0.', '255.')
EXCLUDED_IPV6_PREFIXES = ('::1', 'fe80:', 'ff00:', 'ff02:')
MACOS_INTERFACE_PRIORITY = ['en0', 'en1', 'en2', 'en3', 'en4', 'en5', 'en6', 'en7', 'en8', 'en9', 'anpi0', 'anpi1']


class SystemUtils:
    """系统工具类，提供macOS系统信息获取功能"""

    @staticmethod
    def get_platform() -> str:
        """获取平台类型

        Returns:
            str: 平台类型，固定返回 'Darwin'
        """
        platform_type = platform.system()
        logger.debug(f"当前系统平台: {platform_type}")
        return platform_type

    @staticmethod
    def get_ip_addresses() -> Tuple[str, str]:
        """获取macOS系统的IP地址"""
        # 方法1: 获取主网络接口的IP地址
        for interface in MACOS_INTERFACE_PRIORITY:
            try:
                result = subprocess.run(
                    ['ifconfig', interface],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    check=False
                )

                if result.returncode == 0:
                    ipv4, ipv6 = SystemUtils._parse_ifconfig_output(result.stdout)
                    if ipv4 or ipv6:
                        logger.debug(f"从接口 {interface} 获取到IP: IPv4={ipv4}, IPv6={ipv6}")
                        return ipv4, ipv6

            except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                logger.debug(f"查询接口 {interface} 失败: {str(e)}")
                continue

        # 方法2: 如果优先级接口都没有IP，扫描所有接口
        try:
            result = subprocess.run(
                ['ifconfig'],
                capture_output=True,
                text=True,
                timeout=15,
                check=False
            )

            if result.returncode == 0:
                ipv4, ipv6 = SystemUtils._parse_ifconfig_output(result.stdout)
                if ipv4 or ipv6:
                    logger.debug(f"从所有接口扫描获取到IP: IPv4={ipv4}, IPv6={ipv6}")
                    return ipv4, ipv6

        except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
            logger.debug(f"扫描所有接口失败: {str(e)}")

        logger.warning("未找到有效的IP地址")
        return "", ""

    @staticmethod
    def _parse_ifconfig_output(output: str) -> Tuple[str, str]:
        """解析ifconfig命令输出"""
        ipv4 = ""
        ipv6 = ""

        for line in output.splitlines():
            line = line.strip()

            # 解析IPv4地址
            if "inet " in line:
                match = re.search(r'inet (\d+\.\d+\.\d+\.\d+)', line)
                if match:
                    ip = match.group(1)
                    if not any(ip.startswith(prefix) for prefix in EXCLUDED_IPV4_PREFIXES):
                        ipv4 = ip

            # 解析IPv6地址
            elif "inet6 " in line:
                match = re.search(r'inet6 ([0-9a-fA-F:]+)', line)
                if match:
                    ip = match.group(1)
                    if not any(ip.startswith(prefix) for prefix in EXCLUDED_IPV6_PREFIXES):
                        ipv6 = ip

        return ipv4, ipv6

    @staticmethod
    def get_mac_address() -> str:
        """获取设备唯一标识符

        优先获取硬件级UUID，如果失败则获取主网络接口的MAC地址。
        专为macOS系统设计。

        Returns:
            str: 设备唯一标识符，获取失败时返回空字符串
        """
        try:
            return SystemUtils._get_mac_address_macos()
        except Exception as e:
            logger.error(f"获取设备标识符时发生异常: {str(e)}")
            return SystemUtils._get_fallback_mac_address()

    @staticmethod
    def _get_mac_address_macos() -> str:
        """获取macOS系统的设备标识符"""
        # 方法1: 获取IOPlatformUUID（推荐）
        try:
            result = subprocess.run(
                ['/usr/sbin/ioreg', '-d2', '-c', 'IOPlatformExpertDevice'],
                capture_output=True,
                text=True,
                timeout=10,
                check=False
            )

            if result.returncode == 0:
                for line in result.stdout.splitlines():
                    if 'IOPlatformUUID' in line:
                        # 提取UUID，格式: "IOPlatformUUID" = "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
                        match = re.search(r'"([0-9A-F-]{36})"', line)
                        if match:
                            uuid_str = match.group(1)
                            logger.debug(f"获取到IOPlatformUUID: {uuid_str}")
                            return uuid_str
        except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
            logger.debug(f"获取IOPlatformUUID失败: {str(e)}")

        # 方法2: 获取Hardware UUID
        try:
            result = subprocess.run(
                ['system_profiler', 'SPHardwareDataType'],
                capture_output=True,
                text=True,
                timeout=15,
                check=False
            )

            if result.returncode == 0:
                for line in result.stdout.splitlines():
                    if 'Hardware UUID' in line:
                        parts = line.split(':')
                        if len(parts) > 1:
                            hardware_uuid = parts[1].strip()
                            logger.debug(f"获取到Hardware UUID: {hardware_uuid}")
                            return hardware_uuid
        except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
            logger.debug(f"获取Hardware UUID失败: {str(e)}")

        # 方法3: 获取主网络接口MAC地址
        return SystemUtils._get_interface_mac_address_macos()

    @staticmethod
    def _get_interface_mac_address_macos() -> str:
        """获取macOS主网络接口MAC地址"""
        for interface in MACOS_INTERFACE_PRIORITY:
            try:
                result = subprocess.run(
                    ['ifconfig', interface],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    check=False
                )

                if result.returncode == 0:
                    match = re.search(r'ether ([0-9a-fA-F:]{17})', result.stdout)
                    if match:
                        mac = match.group(1).upper()
                        logger.debug(f"获取到接口 {interface} MAC地址: {mac}")
                        return mac
            except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                logger.debug(f"获取接口 {interface} MAC地址失败: {str(e)}")
                continue

        return ""

    @staticmethod
    def _get_fallback_mac_address() -> str:
        """获取备用MAC地址（使用Python uuid模块）"""
        try:
            # 使用uuid.getnode()获取MAC地址
            mac_int = uuid.getnode()
            # 转换为MAC地址格式
            mac_hex = f"{mac_int:012x}"
            mac_address = ':'.join(mac_hex[i:i+2] for i in range(0, 12, 2)).upper()
            logger.debug(f"使用备用方法获取MAC地址: {mac_address}")
            return mac_address
        except Exception as e:
            logger.error(f"备用MAC地址获取失败: {str(e)}")
            return ""

if __name__ == "__main__":
    print(SystemUtils.get_ip_addresses())