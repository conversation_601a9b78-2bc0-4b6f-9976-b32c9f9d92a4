'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-11 17:04:23
FilePath: /global_rtc_client/config/constants.py
Description: 
'''
"""后端常量定义"""
from enum import IntEnum, Enum, auto
from typing import Optional


class PlatformType(IntEnum):
    """平台类型"""
    ANDROID = 1
    IOS = 2
    WINDOWS = 3
    MACOS = 4
    LINUX = 5

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取平台名称"""
        return {
            cls.ANDROID: "Android",
            cls.IOS: "iOS",
            cls.WINDOWS: "Windows",
            cls.MACOS: "macOS",
            cls.LINUX: "Linux"
        }.get(value, "Unknown")


class TaskStatus(IntEnum):
    """任务状态"""
    NOT_START = 0  # 未开始
    PENDING = 1  # 等待中
    RUNNING = 2  # 执行中
    SUCCESS = 3  # 执行成功
    FAILED = 4  # 执行失败
    RETRY_PENDING = 5  # 重试等待中
    RETRY_RUNNING = 6  # 重试执行中
    CANCELED = 7  # 已取消
    TIMEOUT = 8  # 已超时

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取状态名称"""
        return {
            cls.NOT_START: "未开始",
            cls.PENDING: "等待中",
            cls.RUNNING: "执行中",
            cls.SUCCESS: "执行成功",
            cls.FAILED: "执行失败",
            cls.RETRY_PENDING: "重试等待中",
            cls.RETRY_RUNNING: "重试执行中",
            cls.CANCELED: "已取消",
            cls.TIMEOUT: "已超时"
        }.get(value, "Unknown")


class DeviceLevel(IntEnum):
    """设备等级"""
    LOW = 1  # 低端机
    MIDDLE = 2  # 中端机
    HIGH = 3  # 高端机

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取等级名称"""
        return {
            cls.LOW: "低端机",
            cls.MIDDLE: "中端机",
            cls.HIGH: "高端机"
        }.get(value, "Unknown")
        
class PerfCollectMode(IntEnum):
    """性能采集方式"""
    WIRED = 1  # 有线连接采集
    WIRELESS = 2  # 无线连接采集

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取采集方式名称"""
        return {
            cls.WIRED: "有线连接采集",
            cls.WIRELESS: "无线连接采集"
        }.get(value, "Unknown")


class AppInstallMode(IntEnum):
    """应用安装模式"""
    NOT_INSTALL = 0  # 不安装
    UNINSTALL_INSTALL = 1  # 卸载安装
    OVERRIDE_INSTALL = 2  # 覆盖安装

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取模式名称"""
        return {
            cls.NOT_INSTALL: "不安装",
            cls.UNINSTALL_INSTALL: "卸载安装",
            cls.OVERRIDE_INSTALL: "覆盖安装"
        }.get(value, "Unknown")


class CaseStatus(IntEnum):
    """用例状态"""
    PENDING = 0  # 等待执行
    RUNNING = 1  # 执行中
    RETRYING = 2  # 重试中
    SUCCESS = 3  # 执行成功
    FAILED = 4  # 执行失败
    CANCELED = 5  # 已取消
    TIMEOUT = 6  # 已超时

    @classmethod
    def get_name(cls, value: str) -> str:
        """获取状态名称"""
        status_names = {
            cls.PENDING.value: "等待执行",
            cls.RUNNING.value: "执行中",
            cls.RETRYING.value: "重试中",
            cls.SUCCESS.value: "执行成功",
            cls.FAILED.value: "执行失败",
            cls.CANCELED.value: "已取消",
            cls.TIMEOUT.value: "已超时"
        }
        return status_names.get(value, "未知状态")


class PerfCollectType(IntEnum):
    """性能采集类型"""
    FPS = 1
    CPU = 2
    GPU = 3
    MEM = 4
    DISK = 5
    NET = 6
    BATTERY = 7
    FD = 8
    THREAD = 9
    TEMPERATURE = 10
    ENERGY = 11
    HITCHES = 12
    TIME_PROFILER = 13
    SIGNPOST = 14
    THREAD_STATE = 15
    PRO_INFO = 16
    ALL_SYS_MON = 17

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取采集类型名称"""
        return {
            cls.FPS: "FPS",
            cls.CPU: "CPU",
            cls.GPU: "GPU",
            cls.MEM: "MEM",
            cls.DISK: "DISK",
            cls.NET: "NET",
            cls.BATTERY: "BATTERY",
            cls.FD: "FD",
            cls.THREAD: "THREAD",
            cls.TEMPERATURE: "TEMPERATURE",
            cls.ENERGY: "ENERGY",
            cls.HITCHES: "HITCHES",
            cls.TIME_PROFILER: "TIME_PROFILER",
            cls.SIGNPOST: "SIGNPOST",
            cls.THREAD_STATE: "THREAD_STATE",
            cls.PRO_INFO: "PRO_INFO",
            cls.ALL_SYS_MON: "ALL_SYS_MON"
        }.get(value, "Unknown")


class PerfToolType(IntEnum):
    """性能采集工具类型"""
    DS = 1  # DS性能采集工具
    GAMEPERF = 2  # GamePerf性能采集工具

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取工具类型名称"""
        return {
            cls.DS: "DS性能采集工具",
            cls.GAMEPERF: "GamePerf性能采集工具"
        }.get(value, "Unknown")


class AppType(IntEnum):
    """应用包类型"""
    PERF = 1  # 性能包
    BASE = 2  # 基准包
    ASSIST = 3  # 辅助包

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取包类型名称"""
        return {
            cls.PERF: "性能包",
            cls.BASE: "基准包",
            cls.ASSIST: "辅助包"
        }.get(value, "Unknown")

    @classmethod
    def get_prefix(cls, value: int) -> str:
        """获取包类型对应的文件前缀"""
        return {
            cls.PERF: "perf_",
            cls.BASE: "base_",
            cls.ASSIST: "assist_"
        }.get(value, "")

# Trace命名空间
LIVE_TRACE_NAMESPACE = "live-trace"
RTC_TRACE_NAMESPACE = "abase_rtc_common_monitor_event_aggregation_dim"


class ClientState(IntEnum):
    """客户端状态"""
    OFFLINE = 0  # 离线
    ONLINE = 1  # 在线

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取状态名称"""
        return {
            cls.OFFLINE: "离线",
            cls.ONLINE: "在线",
        }.get(value, "Unknown")


class DeviceState(IntEnum):
    """设备状态"""
    OFFLINE = 0  # 离线
    ONLINE = 1  # 在线

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取状态名称"""
        return {
            cls.OFFLINE: "离线",
            cls.ONLINE: "在线",
        }.get(value, "Unknown")


class DeviceConnectType(IntEnum):
    """设备连接方式"""
    USB = 1  # USB连接
    WIFI = 2  # 无线连接

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取连接方式名称"""
        return {
            cls.USB: "USB",
            cls.WIFI: "WiFi"
        }.get(value, "Unknown")


class ServiceType(Enum):
    """服务类型枚举"""
    SCREEN_LOCK = "screen_lock"  # 屏幕锁定服务
    CLIENT = "client"  # 客户端信息上传
    DEVICE = "device"  # 设备信息上传
    TASK = "task"  # 性能子任务执行
    REQUEST = "request"  # 性能请求接收

    @classmethod
    def from_str(cls, value: str) -> Optional['ServiceType']:
        """从字符串获取枚举值"""
        try:
            return cls[value.upper()]
        except KeyError:
            return None

    def __str__(self) -> str:
        """返回枚举值的字符串表示"""
        return self.value


class ServiceStatus(Enum):
    """服务状态"""
    UNKNOWN = auto()
    ERROR = auto()
    STOPPED = auto()
    STARTING = auto()
    RUNNING = auto()
    STOPPING = auto()
    FAILED = auto()

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取状态名称"""
        return {
            cls.UNKNOWN: "未知",
            cls.ERROR: "错误",
            cls.STOPPED: "已停止",
            cls.STARTING: "启动中",
            cls.RUNNING: "运行中",
            cls.STOPPING: "停止中",
            cls.FAILED: "失败"
        }.get(value, "未知")

class ErrorCode(Enum):
    """错误码定义"""
    SUCCESS = 0
    UNKNOWN_ERROR = 1
    
    # 任务相关错误 (1000-1999)
    TASK_NOT_FOUND = 1000
    TASK_ALREADY_EXISTS = 1001
    TASK_STATUS_UPDATE_FAILED = 1002
    TASK_TERMINATE_FAILED = 1003
    TASK_TERMINATE_TIMEOUT = 1004
    TASK_CLEANUP_FAILED = 1005
    TASK_DIR_CREATE_FAILED = 1006
    TASK_CONFIG_COPY_FAILED = 1007
    
    # 资源相关错误 (2000-2999)
    RESOURCE_NOT_FOUND = 2000
    RESOURCE_ALREADY_EXISTS = 2001
    RESOURCE_OCCUPY_FAILED = 2002
    RESOURCE_RELEASE_FAILED = 2003
    RESOURCE_CLEANUP_FAILED = 2004
    
    # 应用相关错误 (3000-3999)
    APP_NOT_FOUND = 3000
    APP_ALREADY_EXISTS = 3001
    APP_OPERATION_FAILED = 3002
    APP_INSTALL_FAILED = 3003
    APP_UNINSTALL_FAILED = 3004
    APP_PROCESS_TERMINATE_FAILED = 3005
    APP_CLEANUP_FAILED = 3006
    APP_PERF_HANDLE_FAILED = 3007
    APP_ASSIST_HANDLE_FAILED = 3008
    APP_GROUP_BUILD_FAILED = 3009
    
    # 用例相关错误 (4000-4999)
    CASE_NOT_FOUND = 4000
    CASE_ALREADY_EXISTS = 4001
    CASE_EXECUTION_FAILED = 4002
    CASE_PROCESS_TERMINATE_FAILED = 4003
    CASE_CLEANUP_FAILED = 4004
    CASE_GROUP_BUILD_FAILED = 4005
    NO_EXECUTABLE_CASES = 4006

    
    # 设备相关错误 (5000-5999)
    DEVICE_NOT_FOUND = 5000
    DEVICE_ALREADY_EXISTS = 5001
    DEVICE_INIT_FAILED = 5002
    DEVICE_BATTERY_LOW = 5003
    DEVICE_GROUP_BUILD_FAILED = 5004
    
    # 仓库相关错误 (6000-6999)
    REPO_NOT_FOUND = 6000
    REPO_ALREADY_EXISTS = 6001
    REPO_INIT_FAILED = 6002
    
    # 配置相关错误 (7000-7999)
    CONFIG_NOT_FOUND = 7000
    CONFIG_ALREADY_EXISTS = 7001
    CONFIG_UPDATE_FAILED = 7002
    CONFIG_CLEANUP_FAILED = 7003
    CONFIG_GROUP_BUILD_FAILED = 7004
    EXPERIMENT_CONFIGS_BUILD_FAILED = 7010
    
    # 下载相关错误 (8000-8999)
    DOWNLOAD_FAILED = 8000
    DOWNLOAD_TIMEOUT = 8001
    DOWNLOAD_CANCEL_FAILED = 8002
    
    # 视频相关错误 (9000-9999)
    VIDEO_NOT_FOUND = 9000
    VIDEO_PLAY_FAILED = 9001
    VIDEO_STOP_FAILED = 9002
    VIDEO_DOWNLOAD_FAILED = 9003
    
    # 账号相关错误 (10000-10999)
    ACCOUNT_NOT_FOUND = 10000
    ACCOUNT_ALREADY_EXISTS = 10001
    ACCOUNT_GROUP_BUILD_FAILED = 10002
    
    @classmethod
    def get_message(cls, code: int) -> str:
        """获取错误码对应的消息
        
        Args:
            code: 错误码
            
        Returns:
            str: 错误消息
        """
        error_messages = {
            cls.SUCCESS.value: "成功",
            cls.UNKNOWN_ERROR.value: "未知错误",
            
            # 任务相关错误
            cls.TASK_NOT_FOUND.value: "任务未找到",
            cls.TASK_ALREADY_EXISTS.value: "任务已存在",
            cls.TASK_STATUS_UPDATE_FAILED.value: "任务状态更新失败",
            cls.TASK_TERMINATE_FAILED.value: "任务终止失败",
            cls.TASK_TERMINATE_TIMEOUT.value: "任务终止超时",
            cls.TASK_CLEANUP_FAILED.value: "任务清理失败",
            cls.TASK_DIR_CREATE_FAILED.value: "任务目录创建失败",
            cls.TASK_CONFIG_COPY_FAILED.value: "任务配置复制失败",
            
            # 用例相关错误
            cls.CASE_EXECUTION_FAILED.value: "用例执行失败",
            cls.CASE_PROCESS_TERMINATE_FAILED.value: "用例进程终止失败",
            cls.NO_EXECUTABLE_CASES.value: "没有可执行的用例",

            
            # 应用相关错误
            cls.APP_INSTALL_FAILED.value: "应用安装失败",
            cls.APP_UNINSTALL_FAILED.value: "应用卸载失败",
            
            # 设备相关错误
            cls.DEVICE_NOT_FOUND.value: "设备未找到",
            cls.DEVICE_BATTERY_LOW.value: "设备电量过低",
            
            # 下载相关错误
            cls.DOWNLOAD_FAILED.value: "下载失败",
            cls.DOWNLOAD_TIMEOUT.value: "下载超时",
        }
        
        # 如果有特定的错误消息，返回它
        if code in error_messages:
            return error_messages[code]
            
        # 否则尝试通过枚举名称生成消息
        try:
            error = cls(code)
            return error.name.replace('_', ' ').title()
        except ValueError:
            return "未知错误"

class TaskType(IntEnum):
    """任务类型"""
    VERSION_REGRESSION = 1  # 版本回归
    LIBRA_EXPERIMENT = 2  # Libra实验

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取任务类型名称"""
        return {
            cls.VERSION_REGRESSION: "版本回归",
            cls.LIBRA_EXPERIMENT: "Libra实验"
        }.get(value, "Unknown")

class LibraGroupType(IntEnum):
    """Libra实验组类型"""
    EXPERIMENT = 1  # 实验组
    CONTROL = 2     # 对照组
    
    @classmethod
    def get_name(cls, value: int) -> str:
        """获取实验组类型名称"""
        try:
            return {
                cls.EXPERIMENT: "实验组",
                cls.CONTROL: "对照组",
            }[cls(value)]
        except ValueError:
            return "Unknown"