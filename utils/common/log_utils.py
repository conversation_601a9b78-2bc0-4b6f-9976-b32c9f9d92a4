'''
日志工具模块，提供日志配置和管理功能
'''
import sys
import time
import threading
import logging
import asyncio
from enum import Enum
from typing import Optional, Dict, Any, List
from datetime import datetime
from pathlib import Path
from loguru import logger
from typing import Set

class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogConfig:
    """日志配置类"""
    # 路径配置
    ROOT_DIR = Path(__file__).parent.parent.parent
    LOG_DIR = ROOT_DIR / 'logs'
    LOG_DIR.mkdir(parents=True, exist_ok=True)

    # 日志状态跟踪
    CURRENT_LOG_FILE: Optional[Path] = None
    LAST_LOG_DATE: Optional[datetime] = None
    LOGGER_ID: Optional[int] = None
    _file_lock = threading.Lock()  # 文件操作锁

    # 日志格式配置
    CONSOLE_FORMAT = (
        "<white>[</white><green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green><white>]</white> "
        "<white>[</white>{level:<4}<white>]</white> "
        "<white>[</white><cyan>{name}:{function}:{line}</cyan><white>]</white> "
        "<white>➜</white> <level>{message}</level>"
    )
    DEFAULT_FORMAT = (
        "<white>[</white><green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green><white>]</white> "
        "<white>[</white>{level:<4}<white>]</white> "
        "<white>[</white><cyan>{name}:{function}:{line}</cyan><white>]</white> "
        "<white>➜</white> <level>{message}</level>"
    )

    # 日志过滤配置
    FILTER_PATTERNS: List[str] = [
        "metrics address is:",
        "Converted retries value:",
        "Using selector:",
        "Using proactor:",
        "Populating font family aliases",
    ]

    FILTERED_PACKAGES: List[str] = [
        "PyQt6", "PySide2", "qt.qpa",
        "bytedance.metrics", "urllib3", "asyncio"
    ]

    LEVEL_FILTERS: Dict[str, str] = {
        "bytedance.metrics": "INFO",
        "qt.qpa": "WARNING",
    }

    # 日志保留配置
    RETENTION_DAYS = "30 days"
    CHECK_INTERVAL = 60  # 日志日期检查间隔（秒）

    # 默认配置
    DEFAULT_CONFIG = {
        "console": {
            "sink": sys.stderr,
            "format": CONSOLE_FORMAT,
            "level": "INFO",
            "colorize": True,
            "backtrace": True,
            "diagnose": True,
            "enqueue": True,
            "catch": True,
            "serialize": False,
        },
        "file": {
            "format": DEFAULT_FORMAT,
            "retention": RETENTION_DAYS,
            "compression": "zip",
            "encoding": "utf-8",
            "enqueue": True,
            "catch": True,
            "delay": False,
            "serialize": False,
        }
    }

    @classmethod
    def get_log_file(cls) -> Path:
        """获取当前日期的日志文件路径"""
        with cls._file_lock:
            current_time = datetime.now()
            current_date = current_time.date()

            # 检查是否需要切换到新的日志文件
            if cls.LAST_LOG_DATE != current_date or cls.CURRENT_LOG_FILE is None:
                date_str = current_time.strftime('%Y-%m-%d')
                new_log_file = cls.LOG_DIR / f"{date_str}.log"

                # 确保日志文件存在且可写
                if cls._ensure_log_file_exists(new_log_file):
                    # 更新状态
                    cls.CURRENT_LOG_FILE = new_log_file
                    cls.LAST_LOG_DATE = current_date

                    # 重新配置日志记录器
                    if cls.LOGGER_ID is not None:
                        cls._reconfigure_file_logger()
                else:
                    print(f"无法创建或访问日志文件: {new_log_file}", file=sys.stderr)
                    # 如果无法创建新文件，继续使用当前文件（如果存在）
                    if cls.CURRENT_LOG_FILE is None:
                        # 兜底：使用临时文件名
                        cls.CURRENT_LOG_FILE = cls.LOG_DIR / f"fallback-{date_str}.log"

            return cls.CURRENT_LOG_FILE

    @classmethod
    def _reconfigure_file_logger(cls) -> None:
        """重新配置文件日志记录器"""
        try:
            logger.remove(cls.LOGGER_ID)
            file_config = cls.DEFAULT_CONFIG["file"].copy()
            file_config.update({
                "filter": filter_logs,
                "sink": str(cls.CURRENT_LOG_FILE),
                "compression": cls._get_compression_path,
            })
            cls.LOGGER_ID = logger.add(**file_config)
        except Exception as e:
            print(f"重新配置日志记录器失败: {str(e)}", file=sys.stderr)

    @classmethod
    def _get_compression_path(cls, log_file_path: str) -> str:
        """获取压缩文件路径"""
        try:
            log_path = Path(log_file_path)
            # 对于 YYYY-MM-DD.log 格式，压缩为 YYYY-MM-DD.zip
            if log_path.suffix == '.log':
                return str(log_path.with_suffix('.zip'))
            else:
                # 兜底逻辑：移除最后一个扩展名并添加 .zip
                return f"{log_file_path.rsplit('.', 1)[0]}.zip"
        except Exception as e:
            print(f"生成压缩文件路径失败: {str(e)}", file=sys.stderr)
            # 兜底逻辑
            return f"{log_file_path.split('.')[0]}.zip"

    @classmethod
    def _ensure_log_file_exists(cls, log_file: Path) -> bool:
        """确保日志文件存在且可写"""
        try:
            # 确保父目录存在
            log_file.parent.mkdir(parents=True, exist_ok=True)

            # 如果文件不存在，创建它
            if not log_file.exists():
                log_file.touch(mode=0o644, exist_ok=True)

            # 检查文件是否可写
            return log_file.is_file() and log_file.stat().st_mode & 0o200

        except Exception as e:
            print(f"确保日志文件存在失败: {str(e)}", file=sys.stderr)
            return False

def filter_logs(record: Dict[str, Any]) -> bool:
    """日志过滤器"""
    try:
        # 获取日志相关信息
        logger_name = record["name"]
        level_name = record["level"].name if hasattr(record["level"], "name") else record["level"]
        message = str(record["message"])

        # 1. 检查日志级别过滤
        level_map = {name: idx * 10 for idx, name in enumerate(LogLevel)}
        current_level = level_map.get(level_name, 0)
        
        for pkg, min_level in LogConfig.LEVEL_FILTERS.items():
            if logger_name.startswith(pkg):
                return current_level >= level_map.get(min_level, 0)

        # 2. 检查包名过滤
        if any(logger_name.startswith(pkg) for pkg in LogConfig.FILTERED_PACKAGES):
            return False

        # 3. 检查日志内容
        if not message.strip():
            return False

        if any(pattern in message for pattern in LogConfig.FILTER_PATTERNS):
            return False

        return True

    except Exception as e:
        print(f"日志过滤异常: {str(e)}", file=sys.stderr)
        return True

class LogInterceptHandler(logging.Handler):
    """标准日志拦截处理器"""
    def emit(self, record: logging.LogRecord) -> None:
        try:
            # 跳过被过滤的日志
            if not filter_logs({
                "name": record.name,
                "message": record.getMessage(),
                "level": record.levelname
            }):
                return

            # 转发到loguru
            level = logger.level(record.levelname).name
            frame = logging.currentframe()
            depth = 2

            while frame and frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())

        except Exception as e:
            print(f"日志转发异常: {str(e)}", file=sys.stderr)

def init_logger() -> None:
    """初始化日志系统"""
    try:
        # 1. 移除默认handler
        logger.remove()

        # 2. 配置标准日志系统
        logging.basicConfig(handlers=[LogInterceptHandler()], level=0, force=True)

        # 3. 配置loguru handlers
        console_config = LogConfig.DEFAULT_CONFIG["console"].copy()
        file_config = LogConfig.DEFAULT_CONFIG["file"].copy()

        # 更新配置
        for config in (console_config, file_config):
            config["filter"] = filter_logs

        # 设置日志文件
        log_file = LogConfig.get_log_file()
        file_config.update({
            "sink": str(log_file),
            "compression": LogConfig._get_compression_path,
        })

        # 添加handlers
        logger.add(**console_config)
        LogConfig.LOGGER_ID = logger.add(**file_config)

    except Exception as e:
        print(f"初始化日志配置失败: {str(e)}", file=sys.stderr)
        raise

def start_log_date_checker() -> None:
    """启动日志日期检查器"""
    def _check_loop() -> None:
        while True:
            try:
                time.sleep(LogConfig.CHECK_INTERVAL)
                LogConfig.get_log_file()
            except Exception as e:
                print(f"日志日期检查异常: {str(e)}", file=sys.stderr)

    checker = threading.Thread(
        target=_check_loop,
        name="LogDateChecker",
        daemon=True
    )
    checker.start()

def get_logger(
    name: Optional[str] = None,
    **kwargs: Any
):
    """获取日志记录器"""
    try:
        context = {
            "name": name or "root",
            "time": datetime.now().isoformat(),
        }
        context.update(kwargs)
        return logger.bind(**context)

    except Exception as e:
        print(f"获取日志记录器失败: {str(e)}", file=sys.stderr)
        return logger

# 实时日志流处理工具
async def stream_realtime_logs(stream, stream_name: str, sub_task_id: str = None) -> None:
    """实时流式处理日志输出

    逐行读取并实时处理进程输出，避免缓冲区阻塞问题

    Args:
        stream: 异步流对象 (stdout 或 stderr)
        stream_name: 流名称，用于日志标识
        sub_task_id: 子任务ID，用于日志标识
    """
    if not stream:
        logger.debug(f"[子任务 {sub_task_id}] {stream_name} 流为空，跳过处理")
        return

    # 定义需要过滤的日志内容
    FILTERED_MESSAGES = {
        "can not add bytest handler twice",
        "use adb to click",
        "try press back to close it",
        "failed: [Errno 17] File exists:",
    }

    line_count = 0
    last_heartbeat = time.time()
    heartbeat_interval = 30  # 30秒心跳间隔

    try:
        logger.debug(f"[子任务 {sub_task_id}] 开始实时处理 {stream_name} 流")

        while True:
            try:
                # 逐行异步读取
                line_bytes = await stream.readline()

                # 检查是否到达流末尾
                if not line_bytes:
                    logger.debug(f"[子任务 {sub_task_id}] {stream_name} 流已结束")
                    break

                # 解码并处理行内容
                try:
                    line = line_bytes.decode('utf-8', errors='replace').rstrip('\n\r')
                except UnicodeDecodeError as decode_err:
                    logger.warning(f"[子任务 {sub_task_id}] {stream_name} 解码失败: {decode_err}")
                    continue

                # 实时处理日志行
                if line and should_log_line(line, FILTERED_MESSAGES):
                    # 根据流类型选择日志级别
                    if stream_name == "STDERR":
                        logger.warning(f"[{stream_name}] {line}")
                    else:
                        logger.info(f"[{stream_name}] {line}")

                line_count += 1

                # 定期输出心跳日志，避免长时间无输出时的静默
                current_time = time.time()
                if current_time - last_heartbeat > heartbeat_interval:
                    logger.debug(f"[子任务 {sub_task_id}] {stream_name} 处理进度: 已处理 {line_count} 行日志")
                    last_heartbeat = current_time

            except asyncio.CancelledError:
                logger.debug(f"[子任务 {sub_task_id}] {stream_name} 流处理被取消")
                break
            except Exception as line_err:
                logger.error(f"[子任务 {sub_task_id}] {stream_name} 处理单行日志异常: {line_err}")
                continue  # 继续处理下一行

    except Exception as e:
        logger.error(f"[子任务 {sub_task_id}] {stream_name} 流处理异常: {str(e)}")
    finally:
        logger.debug(f"[子任务 {sub_task_id}] {stream_name} 流处理完成，共处理 {line_count} 行日志")

def should_log_line(line: str, filtered_messages: Set[str]) -> bool:
    """判断是否应该记录该行日志

    Args:
        line: 日志行内容
        filtered_messages: 需要过滤的消息集合

    Returns:
        bool: 是否应该记录该行日志
    """
    if not line.strip():
        return False

    # 检查是否包含需要过滤的内容
    return not any(msg in line for msg in filtered_messages)

async def monitor_process_status(process, sub_task_id: str = None) -> None:
    """监控进程状态

    定期检查进程状态，提供进程运行的监控信息

    Args:
        process: 子进程对象
        sub_task_id: 子任务ID，用于日志标识
    """
    try:
        start_time = time.time()
        check_interval = 60  # 60秒检查间隔

        logger.debug(f"[子任务 {sub_task_id}] 开始监控进程状态 (PID: {process.pid})")

        while process.returncode is None:
            try:
                # 等待指定间隔或进程结束
                await asyncio.sleep(check_interval)

                # 检查进程是否仍在运行
                if process.returncode is None:
                    elapsed_time = time.time() - start_time
                    logger.debug(f"[子任务 {sub_task_id}] 进程运行中... (PID: {process.pid}, 运行时间: {elapsed_time:.1f}秒)")
                else:
                    break

            except asyncio.CancelledError:
                logger.debug(f"[子任务 {sub_task_id}] 进程监控被取消")
                break
            except Exception as e:
                logger.error(f"[子任务 {sub_task_id}] 进程监控异常: {str(e)}")
                break

    except Exception as e:
        logger.error(f"[子任务 {sub_task_id}] 进程状态监控失败: {str(e)}")
    finally:
        total_time = time.time() - start_time
        logger.debug(f"[子任务 {sub_task_id}] 进程监控结束，总运行时间: {total_time:.1f}秒")

# 初始化日志系统
init_logger()
start_log_date_checker()
