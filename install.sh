#!/bin/bash

# Global Business Client - 一键安装脚本
# 支持 macOS 平台的自动安装和配置

set -e  # 遇到错误立即退出

# 常量定义
readonly PYTHON_VERSION="3.9"
readonly PYTHON_PATH="/opt/homebrew/opt/python@3.9/libexec/bin"
readonly CACHE_DIR="$HOME/.gb_cache"
readonly CACHE_FILE="$CACHE_DIR/project_path"
readonly GLOBAL_BIN_DIR="/usr/local/bin"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# 检测用户使用的 shell 类型并返回配置文件路径
detect_shell_config() {
    local shell_config=""

    if [ -n "$ZSH_VERSION" ] || [ "$SHELL" = "/bin/zsh" ] || [ "$SHELL" = "/usr/bin/zsh" ]; then
        shell_config="$HOME/.zshrc"
        log_info "检测到 zsh shell，配置文件: ~/.zshrc"
    elif [ -n "$BASH_VERSION" ] || [ "$SHELL" = "/bin/bash" ] || [ "$SHELL" = "/usr/bin/bash" ]; then
        shell_config="$HOME/.bashrc"
        log_info "检测到 bash shell，配置文件: ~/.bashrc"
    else
        # 默认检查文件存在性
        if [ -f "$HOME/.zshrc" ]; then
            shell_config="$HOME/.zshrc"
            log_info "使用默认 zsh 配置文件: ~/.zshrc"
        elif [ -f "$HOME/.bashrc" ]; then
            shell_config="$HOME/.bashrc"
            log_info "使用默认 bash 配置文件: ~/.bashrc"
        else
            # 创建 .zshrc 作为默认配置（macOS 默认使用 zsh）
            shell_config="$HOME/.zshrc"
            log_info "创建默认 zsh 配置文件: ~/.zshrc"
        fi
    fi

    echo "$shell_config"
}

# 重新加载 shell 配置文件
reload_shell_config() {
    local shell_config="$1"

    log_info "重新加载 shell 配置文件..."
    if [[ "$shell_config" == *".zshrc" ]]; then
        if command -v zsh &> /dev/null; then
            zsh -c "source $shell_config" 2>/dev/null || true
        fi
    elif [[ "$shell_config" == *".bashrc" ]]; then
        if command -v bash &> /dev/null; then
            bash -c "source $shell_config" 2>/dev/null || true
        fi
    fi
}

# 检查配置是否已存在
config_exists() {
    local config_file="$1"
    local search_pattern="$2"

    [ -f "$config_file" ] && grep -q "$search_pattern" "$config_file"
}

# 添加配置到文件
add_config_to_file() {
    local config_file="$1"
    local comment="$2"
    local config_line="$3"

    # 确保配置文件存在
    if [ ! -f "$config_file" ]; then
        touch "$config_file"
        log_info "创建配置文件: $config_file"
    fi

    echo "" >> "$config_file"
    echo "# $comment" >> "$config_file"
    echo "$config_line" >> "$config_file"
}

# 检查操作系统
check_os() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "此安装脚本仅支持 macOS 系统"
        exit 1
    fi
    log_info "检测到 macOS 系统，继续安装..."
}

# 获取项目根目录
get_project_root() {
    PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    log_info "项目根目录: $PROJECT_ROOT"
}

# 检查权限
check_permissions() {
    log_info "检查安装权限..."

    # 检查是否可以写入全局二进制目录
    if [ ! -w "$GLOBAL_BIN_DIR" ]; then
        log_warning "需要管理员权限来创建全局命令"
        if ! sudo -n true 2>/dev/null; then
            log_info "请输入管理员密码以继续安装..."
            sudo -v
        fi
    fi
}

# 安装 Homebrew
install_homebrew() {
    if ! command -v brew &> /dev/null; then
        log_info "正在安装 Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        log_success "Homebrew 安装完成"
    else
        log_info "Homebrew 已安装，正在更新..."
        brew update
        log_success "Homebrew 更新完成"
    fi
    
    # 禁止 Homebrew 自动更新
    export HOMEBREW_NO_AUTO_UPDATE=1
}

# 安装 Python
install_python() {
    local python_cmd="python${PYTHON_VERSION}"

    if ! command -v "$python_cmd" &> /dev/null; then
        log_info "正在安装 Python ${PYTHON_VERSION}..."
        brew install "python@${PYTHON_VERSION}"
        log_success "Python ${PYTHON_VERSION} 安装完成"
    else
        log_success "Python ${PYTHON_VERSION} 已安装"
    fi
}

# 配置 Python 环境变量
configure_python_env() {
    log_info "正在配置 Python ${PYTHON_VERSION} 环境变量..."

    local env_line="export PATH=\"$PYTHON_PATH:\$PATH\""
    local shell_config
    shell_config=$(detect_shell_config)

    # 检查环境变量是否已经存在
    if config_exists "$shell_config" "python@${PYTHON_VERSION}/libexec/bin"; then
        log_success "Python ${PYTHON_VERSION} 环境变量已配置"
    else
        # 添加环境变量到配置文件
        add_config_to_file "$shell_config" "Python ${PYTHON_VERSION} 环境变量配置 (由 GB 安装脚本添加)" "$env_line"
        log_success "Python ${PYTHON_VERSION} 环境变量已添加到 $shell_config"
    fi

    # 重新加载配置文件
    reload_shell_config "$shell_config"

    # 验证配置
    verify_python_config "$shell_config"
}

# 验证 Python 配置
verify_python_config() {
    local shell_config="$1"

    log_info "验证 Python ${PYTHON_VERSION} 配置..."

    # 在新的 shell 环境中验证
    local python_version=""
    local python_path_check=""

    if [[ "$shell_config" == *".zshrc" ]]; then
        python_version=$(zsh -c "source $shell_config && python3 --version 2>&1" 2>/dev/null || echo "未找到")
        python_path_check=$(zsh -c "source $shell_config && which python3 2>&1" 2>/dev/null || echo "未找到")
    elif [[ "$shell_config" == *".bashrc" ]]; then
        python_version=$(bash -c "source $shell_config && python3 --version 2>&1" 2>/dev/null || echo "未找到")
        python_path_check=$(bash -c "source $shell_config && which python3 2>&1" 2>/dev/null || echo "未找到")
    fi

    if [[ "$python_version" == *"Python ${PYTHON_VERSION}"* ]]; then
        log_success "Python ${PYTHON_VERSION} 配置验证成功: $python_version"
        log_info "Python 路径: $python_path_check"
    else
        log_warning "Python ${PYTHON_VERSION} 配置可能需要手动重新加载 shell"
        log_info "请执行以下命令之一："
        if [[ "$shell_config" == *".zshrc" ]]; then
            log_info "  source ~/.zshrc"
        else
            log_info "  source ~/.bashrc"
        fi
        log_info "然后验证: python3 --version"
    fi
}

# 创建虚拟环境
create_virtual_env() {
    log_info "正在创建虚拟环境..."

    local venv_path="$PROJECT_ROOT/.venv"
    local python_cmd="python${PYTHON_VERSION}"

    if [ -d "$venv_path" ]; then
        log_success "虚拟环境已存在"
    else
        "$python_cmd" -m venv "$venv_path"
        log_success "虚拟环境创建完成"
    fi
}

# 安装 Python 依赖
install_python_dependencies() {
    log_info "正在安装 Python 依赖包..."

    local venv_path="$PROJECT_ROOT/.venv"
    local python_bin="$venv_path/bin/python${PYTHON_VERSION}"
    local pip_bin="$venv_path/bin/pip${PYTHON_VERSION}"
    local requirements_file="$PROJECT_ROOT/requirements.txt"

    # 检查必要文件是否存在
    if [ ! -f "$requirements_file" ]; then
        log_error "requirements.txt 文件不存在"
        return 1
    fi

    # 激活虚拟环境
    source "$venv_path/bin/activate"

    # 更新 pip
    "$python_bin" -m pip install --upgrade pip

    # 安装依赖包
    "$pip_bin" install -r "$requirements_file" \
        -i https://bytedpypi.byted.org/simple/ \
        -i https://shoots-pypi.bytedance.net/simple/ \
        -U --progress-bar on --no-cache-dir 2>&1 | grep -v "Requirement already satisfied" || true

    log_success "Python 依赖包安装完成"
}

# 安装单个工具的通用函数
install_tool() {
    local tool_name="$1"
    local brew_package="$2"
    local install_args="${3:-}"

    if ! command -v "$tool_name" &> /dev/null; then
        log_info "正在安装 $tool_name..."
        brew install "$brew_package" $install_args
        log_success "$tool_name 安装完成"
    else
        log_success "$tool_name 已安装"
    fi
}

# 安装系统工具
install_system_tools() {
    log_info "正在安装系统工具..."

    # 定义工具列表：工具名称 brew包名 安装参数
    local tools=(
        "adb:android-platform-tools:"
        "java:oracle-jdk:"
        "ideviceinstaller:ideviceinstaller:"
        "ffplay:ffmpeg:--quiet"
    )

    # 批量安装工具
    for tool_info in "${tools[@]}"; do
        IFS=':' read -r tool_name brew_package install_args <<< "$tool_info"
        install_tool "$tool_name" "$brew_package" "$install_args"
    done

    log_success "系统工具安装完成"
}

# 安装 cosign
install_cosign() {
    if ! command -v cosign &> /dev/null; then
        log_info "正在安装 cosign..."
        /bin/bash -c "$(curl -fsSL http://voffline.byted.org/download/tos/schedule/hopter/cosign/scripts/install.sh)"
        log_success "cosign 安装完成"
    else
        log_success "cosign 已安装"
    fi
}

# 创建项目路径缓存
create_project_cache() {
    log_info "正在创建项目路径缓存..."

    # 创建缓存目录
    if [ ! -d "$CACHE_DIR" ]; then
        mkdir -p "$CACHE_DIR"
        log_info "创建缓存目录: $CACHE_DIR"
    fi

    # 写入项目路径到缓存文件
    echo "$PROJECT_ROOT" > "$CACHE_FILE"
    log_success "项目路径已缓存: $PROJECT_ROOT"
    log_info "缓存文件: $CACHE_FILE"
}

# 设置 gb 别名
set_gb_alias() {
    log_info "正在设置 gb 别名..."

    local alias_line="alias gb='gb'"
    local shell_config
    shell_config=$(detect_shell_config)

    # 检查别名是否已经存在
    if config_exists "$shell_config" "alias gb="; then
        log_success "gb 别名已存在于 $shell_config"
    else
        # 添加别名到配置文件
        add_config_to_file "$shell_config" "GB 命令别名配置 (由 GB 安装脚本添加)" "$alias_line"
        log_success "gb 别名已添加到 $shell_config"
    fi

    # 在当前会话中设置别名
    alias gb='gb' 2>/dev/null || true

    # 重新加载配置文件
    reload_shell_config "$shell_config"

    log_success "gb 别名设置完成"
}

# 安装 gb 脚本到全局路径
install_gb_script() {
    local gb_script="$PROJECT_ROOT/gb"
    local global_gb="$GLOBAL_BIN_DIR/gb"

    log_info "安装项目中的 gb 脚本..."

    # 复制 gb 脚本到全局二进制目录
    if [ -w "$GLOBAL_BIN_DIR" ]; then
        cp "$gb_script" "$global_gb"
        chmod +x "$global_gb"
    else
        sudo cp "$gb_script" "$global_gb"
        sudo chmod +x "$global_gb"
    fi

    log_success "gb 脚本已安装到全局路径"
}

# 创建全局 gb 命令
create_global_command() {
    log_info "正在创建全局 gb 命令..."

    # 设置 gb 别名
    set_gb_alias

    # 检查项目中的 gb 脚本是否存在
    local gb_script="$PROJECT_ROOT/gb"
    if [ ! -f "$gb_script" ]; then
        log_error "项目中的 gb 脚本不存在"
        log_error "请确保 gb 脚本文件存在于项目根目录"
        exit 1
    fi

    # 安装 gb 脚本
    install_gb_script

    # 创建项目路径缓存
    create_project_cache
}

# 验证单个组件
verify_component() {
    local component_name="$1"
    local check_command="$2"
    local error_message="$3"

    if eval "$check_command"; then
        log_success "$component_name 验证成功"
        return 0
    else
        log_error "$error_message"
        return 1
    fi
}

# 验证安装
verify_installation() {
    log_info "正在验证安装..."

    local verification_failed=false

    # 定义验证项目
    local verifications=(
        "gb 命令:command -v gb &> /dev/null:gb 命令安装失败"
        "虚拟环境:[ -f \"$PROJECT_ROOT/.venv/bin/python${PYTHON_VERSION}\" ]:虚拟环境创建失败"
        "应用文件:[ -f \"$PROJECT_ROOT/app.py\" ]:应用文件不存在"
    )

    # 批量验证
    for verification in "${verifications[@]}"; do
        IFS=':' read -r component_name check_command error_message <<< "$verification"
        if ! verify_component "$component_name" "$check_command" "$error_message"; then
            verification_failed=true
        fi
    done

    if [ "$verification_failed" = true ]; then
        log_error "安装验证失败"
        return 1
    fi

    log_success "安装验证完成"
}

# 显示安装完成信息
show_completion_message() {
    echo ""
    echo "=========================================="
    log_success "Global Business Client 安装完成！"
    echo "=========================================="
    echo ""
    echo "用法: gb [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h         显示此帮助信息"
    echo "  --version, -v      显示版本信息"
    echo "  --reinstall        重新安装应用程序（更新代码并执行完整安装）"
    echo ""
    echo "功能特性:"
    echo "  - 启动前自动拉取最新代码"
    echo "  - 优雅退出支持 (Cmd+C/Ctrl+C)"
    echo ""
    echo "示例:"
    echo "  gb                 启动应用程序（自动更新代码）"
    echo "  gb --reinstall     重新安装应用程序（更新代码并完整安装）"
    echo ""
}

# 主安装流程
main() {
    echo "=========================================="
    echo "Global Business Client"
    echo "一键安装脚本"
    echo "=========================================="
    echo ""
    
    check_os
    get_project_root
    check_permissions

    log_info "开始安装..."

    install_homebrew
    install_python
    configure_python_env
    create_virtual_env
    install_python_dependencies
    install_system_tools
    install_cosign
    create_global_command
    verify_installation
    
    show_completion_message
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查日志并重试"; exit 1' ERR

# 运行主函数
main "$@"
