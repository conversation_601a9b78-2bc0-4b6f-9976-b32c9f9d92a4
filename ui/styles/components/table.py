"""表格样式定义"""
from ..base.colors import Colors

class TableStyle:
    @classmethod
    def get_table_base_style(cls) -> str:
        """获取表格基础样式"""
        return f"""
            QTableWidget {{
                border: none;
                background: {Colors.SURFACE};
                gridline-color: {Colors.BORDER};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {Colors.BORDER};
            }}
            QHeaderView::section {{
                background: {Colors.SURFACE};
                padding: 8px;
                border: none;
                border-bottom: 2px solid {Colors.PRIMARY};
                font-weight: bold;
                color: {Colors.TEXT_PRIMARY};
            }}
        """

    @classmethod
    def get_serial_dialog_table_style(cls) -> str:
        """获取串口配置表格样式"""
        return f"""
            QTableWidget {{
                background: {Colors.SURFACE};
                border: none;
                gridline-color: {Colors.BORDER};
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {Colors.BORDER};
            }}
            QTableWidget::item:selected {{
                background: {Colors.SURFACE_LIGHT};
                color: {Colors.PRIMARY};
            }}
            QHeaderView::section {{
                background: {Colors.SURFACE};
                padding: 12px;
                border: none;
                border-bottom: 2px solid {Colors.PRIMARY};
                font-weight: 600;
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                text-align: left;
            }}
        """ 