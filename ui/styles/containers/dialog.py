'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-11 13:16:17
FilePath: /global_rtc_client/ui/styles/containers/dialog.py
Description: 对话框样式定义
'''
from ..base.colors import Colors

class DialogStyle:
    @classmethod
    def get_dialog_style(cls) -> str:
        """获取对话框基础样式"""
        return f"""
            QDialog {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
            }}
        """

    @classmethod
    def get_dialog_content_style(cls) -> str:
        """获取对话框内容区域样式"""
        return f"""
            QWidget {{
                background: transparent;
            }}
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                line-height: 1.6;
            }}
            QPushButton {{
                min-width: 80px;
                padding: 8px 16px;
            }}
        """

    @classmethod
    def get_dialog_base_style(cls) -> str:
        """获取对话框基础样式"""
        return f"""
            QDialog {{
                background: {Colors.SURFACE};
                border-radius: 16px;
                border: 1px solid {Colors.BORDER};
                min-width: 400px;
            }}
            
            /* 标签样式 */
            QDialog QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
            }}
            QDialog QLabel#titleLabel {{
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 16px;
            }}
            QDialog QLabel#messageLabel {{
                color: {Colors.TEXT_SECONDARY};
                line-height: 1.5;
                padding: 0 16px;
                background: transparent;
            }}
            
            /* 按钮样式 */
            QDialog QPushButton {{
                min-width: 88px;
                min-height: 18px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                padding: 8px 16px;
            }}
            QDialog QPushButton#okButton {{
                background: {Colors.PRIMARY};
                color: white;
                border: none;
            }}
            QDialog QPushButton#okButton:hover {{
                background: {Colors.PRIMARY_LIGHT};
            }}
            QDialog QPushButton#okButton:pressed {{
                background: {Colors.PRIMARY_DARK};
            }}
            QDialog QPushButton#cancelButton {{
                background: transparent;
                color: {Colors.TEXT_PRIMARY};
                border: 1px solid {Colors.BORDER};
            }}
            QDialog QPushButton#cancelButton:hover {{
                background: {Colors.SURFACE_LIGHT};
                border-color: {Colors.PRIMARY};
                color: {Colors.PRIMARY};
            }}
            
            /* 输入框样式 */
            QDialog QLineEdit, QDialog QTextEdit {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QDialog QLineEdit:focus, QDialog QTextEdit:focus {{
                border-color: {Colors.PRIMARY};
            }}
            
            /* 下拉框样式 */
            QDialog QComboBox {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QDialog QComboBox:focus {{
                border-color: {Colors.PRIMARY};
            }}
            QDialog QComboBox::drop-down {{
                border: none;
                padding-right: 8px;
            }}
            QDialog QComboBox::down-arrow {{
                image: url(ui/icons/status/chevron-down.svg);
                width: 16px;
                height: 16px;
            }}
            
            /* 表格样式 */
            QDialog QTableWidget {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                gridline-color: {Colors.BORDER};
            }}
            QDialog QTableWidget::item {{
                padding: 8px;
                border: none;
            }}
            QDialog QTableWidget::item:selected {{
                background: {Colors.PRIMARY_LIGHT}40;
                color: {Colors.PRIMARY};
            }}
            QDialog QHeaderView::section {{
                background: {Colors.SURFACE};
                padding: 8px;
                border: none;
                border-bottom: 1px solid {Colors.BORDER};
                font-weight: 500;
            }}
        """

    @classmethod
    def get_message_box_style(cls, type: str = "info") -> str:
        """获取消息框样式"""
        styles = {
            "info": {
                "color": Colors.PRIMARY,
                "icon": "ui/icons/status/info.svg"
            },
            "success": {
                "color": Colors.SUCCESS,
                "icon": "ui/icons/status/success.svg"
            },
            "warning": {
                "color": Colors.WARNING,
                "icon": "ui/icons/status/warning.svg"
            },
            "error": {
                "color": Colors.ERROR,
                "icon": "ui/icons/status/error.svg"
            }
        }
        
        style = styles.get(type, styles["info"])
        
        return f"""
            QDialog {{
                background: transparent;
            }}
            QFrame#container {{
                background: {Colors.SURFACE};
                border-radius: 12px;
                border: 1px solid {Colors.BORDER};
                margin: 0px;
            }}
            QLabel#titleLabel {{
                font-size: 18px;
                font-weight: 600;
                color: {style['color']};
                background-image: url({style['icon']});
                background-position: left center;
                background-repeat: no-repeat;
                padding-left: 32px;
                margin: 0px;
            }}
            QLabel#messageLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                line-height: 1.5;
                margin: 0px;
            }}
            QPushButton#okButton {{
                background: {style['color']};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 0px 16px;
                font-weight: 500;
                min-width: 96px;
                min-height: 36px;
                margin: 0px;
            }}
            QPushButton#okButton:hover {{
                background: {style['color']}D0;
            }}
            QPushButton#okButton:pressed {{
                background: {style['color']};
            }}
            QPushButton#cancelButton {{
                background: transparent;
                color: {Colors.TEXT_PRIMARY};
                border: 1px solid {Colors.BORDER};
                border-radius: 6px;
                padding: 0px 16px;
                font-weight: 500;
                min-width: 96px;
                min-height: 36px;
                margin: 0px;
            }}
            QPushButton#cancelButton:hover {{
                background: {Colors.SURFACE_LIGHT};
                border-color: {style['color']};
                color: {style['color']};
            }}
            QPushButton#cancelButton:pressed {{
                background: {Colors.SURFACE_LIGHT};
                border-color: {style['color']};
                color: {style['color']};
            }}
        """

    @classmethod
    def get_auth_dialog_style(cls) -> str:
        """获取认证对话框样式"""
        return f"""
            QDialog {{
                background: {Colors.SURFACE};
                border-radius: 16px;
                border: 1px solid {Colors.BORDER};
            }}
            
            /* 标签样式 */
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
            }}
            QLabel#titleLabel {{
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 24px;
            }}
            
            /* 输入框和下拉框样式 */
            QLineEdit, QComboBox {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: {Colors.TEXT_PRIMARY};
                min-width: 280px;
                min-height: 40px;
            }}
            QLineEdit:focus, QComboBox:focus {{
                border-color: {Colors.PRIMARY};
            }}
            QComboBox::drop-down {{
                border: none;
                padding-right: 8px;
            }}
            QComboBox::down-arrow {{
                image: url(ui/icons/chevron-down.svg);
                width: 16px;
                height: 16px;
            }}
            QComboBox QAbstractItemView {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                selection-background-color: {Colors.PRIMARY_LIGHT}40;
                selection-color: {Colors.PRIMARY};
            }}
            
            /* 按钮样式 */
            QPushButton {{
                min-width: 280px;
                min-height: 40px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                padding: 8px 16px;
            }}
            QPushButton#authButton {{
                background: {Colors.PRIMARY};
                color: white;
                border: none;
            }}
            QPushButton#authButton:hover {{
                background: {Colors.PRIMARY_LIGHT};
            }}
            QPushButton#authButton:pressed {{
                background: {Colors.PRIMARY_DARK};
            }}
            QPushButton#authButton:disabled {{
                background: {Colors.SURFACE_LIGHT};
                color: {Colors.TEXT_DISABLED};
            }}
            
            /* 容器样式 */
            QWidget#mainContainer {{
                background: {Colors.SURFACE};
                border-radius: 16px;
                border: 1px solid {Colors.BORDER};
            }}
            QWidget#contentContainer {{
                background: transparent;
            }}
            
            /* 窗口控制按钮样式 */
            QPushButton#minimizeButton, QPushButton#maximizeButton, QPushButton#closeButton {{
                min-width: 40px;
                min-height: 40px;
                border-radius: 0px;
                background: transparent;
            }}
            QPushButton#minimizeButton:hover, QPushButton#maximizeButton:hover {{
                background: {Colors.SURFACE_LIGHT};
            }}
            QPushButton#closeButton:hover {{
                background: {Colors.ERROR};
            }}
        """

    @classmethod
    def get_doc_link_button_style(cls) -> str:
        """获取文档链接按钮样式"""
        return f"""
            QPushButton {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                text-align: left;
                padding: 0px;
            }}
            QPushButton:hover {{
                background: {Colors.SURFACE_LIGHT};
                border-color: {Colors.PRIMARY};
            }}
        """