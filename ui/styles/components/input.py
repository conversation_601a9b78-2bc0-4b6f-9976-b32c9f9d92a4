"""输入控件样式定义"""
from ..base.colors import Colors
from ..base.fonts import Fonts

class InputStyle:
    @classmethod
    def get_line_edit_style(cls) -> str:
        """获取单行输入框样式"""
        return f"""
            QLineEdit {{
                background-color: {Colors.BACKGROUND};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 4px 8px;
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                min-height: 32px;
                max-height: 32px;
                selection-background-color: {Colors.SELECTED};
            }}
            QLineEdit:hover {{
                border-color: {Colors.BORDER_DARK};
                background-color: {Colors.BACKGROUND_LIGHT};
            }}
            QLineEdit:focus {{
                border-color: {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QLineEdit:disabled {{
                background-color: {Colors.DISABLED};
                color: {Colors.TEXT_DISABLED};
            }}
            QLineEdit[readOnly="true"] {{
                background-color: {Colors.BACKGROUND_LIGHT};
                color: {Colors.TEXT_SECONDARY};
            }}
        """

    @classmethod
    def get_combo_box_style(cls) -> str:
        """获取下拉框样式"""
        return f"""
            QComboBox {{
                background-color: {Colors.BACKGROUND};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 4px 8px;
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                min-height: 32px;
                max-height: 32px;
            }}
            QComboBox:hover {{
                border-color: {Colors.BORDER_DARK};
                background-color: {Colors.BACKGROUND_LIGHT};
            }}
            QComboBox:focus {{
                border-color: {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QComboBox::drop-down {{
                width: 20px;
                border: none;
                background: transparent;
            }}
            QComboBox::down-arrow {{
                image: url(ui/icons/actions/arrow_down.svg);
                width: 12px;
                height: 12px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {Colors.BACKGROUND};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                selection-background-color: {Colors.SELECTED};
                selection-color: {Colors.TEXT_PRIMARY};
                padding: 4px;
            }}
            QComboBox QAbstractItemView::item {{
                padding: 8px;
                min-height: 24px;
            }}
            QComboBox QAbstractItemView::item:hover {{
                background-color: {Colors.HOVER};
                color: {Colors.PRIMARY};
            }}
            QComboBox QAbstractItemView::item:selected {{
                background-color: {Colors.SELECTED};
                color: {Colors.PRIMARY};
            }}
        """

    @classmethod
    def get_text_edit_style(cls) -> str:
        """获取文本编辑器样式"""
        return f"""
            QTextEdit {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 8px;
                color: {Colors.TEXT_PRIMARY};
                font-family: {Fonts.get_monospace_font_family()};
                font-size: 14px;
            }}
            QTextEdit:focus {{
                border-color: {Colors.PRIMARY};
                background-color: {Colors.SURFACE_LIGHT};
            }}
        """

    @classmethod
    def get_spin_box_style(cls) -> str:
        """获取数字输入框样式"""
        return f"""
            QSpinBox {{
                background-color: {Colors.BACKGROUND};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 4px 8px;
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                min-height: 32px;
                max-height: 32px;
            }}
            QSpinBox:hover {{
                border-color: {Colors.BORDER_DARK};
                background-color: {Colors.BACKGROUND_LIGHT};
            }}
            QSpinBox:focus {{
                border-color: {Colors.PRIMARY};
                background-color: {Colors.BACKGROUND};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                width: 20px;
                background-color: transparent;
                border: none;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {Colors.HOVER};
            }}
            QSpinBox::up-arrow {{
                image: url(ui/icons/actions/arrow_up.svg);
                width: 12px;
                height: 12px;
            }}
            QSpinBox::down-arrow {{
                image: url(ui/icons/actions/arrow_down.svg);
                width: 12px;
                height: 12px;
            }}
        """

    @classmethod
    def get_checkbox_style(cls) -> str:
        """获取复选框样式"""
        return f"""
            QCheckBox {{
                spacing: 8px;
                min-height: 32px;
                max-height: 32px;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 1px solid {Colors.BORDER};
                border-radius: 2px;
                background-color: {Colors.BACKGROUND};
            }}
            QCheckBox::indicator:hover {{
                border-color: {Colors.BORDER_DARK};
            }}
            QCheckBox::indicator:checked {{
                background-color: {Colors.PRIMARY};
                border-color: {Colors.PRIMARY};
                image: url(ui/icons/actions/check.svg);
            }}
            QCheckBox::indicator:checked:hover {{
                background-color: {Colors.PRIMARY_HOVER};
                border-color: {Colors.PRIMARY_HOVER};
            }}
        """

    @classmethod
    def get_cert_upload_combo_style(cls) -> str:
        """获取证书上传下拉框样式"""
        return f"""
            QComboBox {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                min-width: 320px;
                height: 42px;
            }}
            QComboBox:hover {{
                border-color: {Colors.PRIMARY_LIGHT};
            }}
            QComboBox:focus {{
                border-color: {Colors.PRIMARY};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 36px;
            }}
            QComboBox::down-arrow {{
                image: url(ui/icons/controls/dropdown.svg);
                width: 16px;
                height: 16px;
            }}
        """

    @classmethod
    def get_cert_upload_input_style(cls) -> str:
        """获取证书上传输入框样式"""
        return f"""
            QLineEdit {{
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 12px 16px;
                background: #F8FAFC;
                font-size: 14px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: {Colors.PRIMARY};
                background: white;
            }}
            QLineEdit::placeholder {{
                color: {Colors.TEXT_SECONDARY};
            }}
        """

    @classmethod
    def get_auth_combobox_style(cls) -> str:
        """获取认证窗口下拉框样式"""
        return f"""
            QComboBox {{
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 8px 16px;
                background: white;
                min-width: 320px;
                height: 40px;
                font-size: 14px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QComboBox:hover {{
                border-color: {Colors.PRIMARY_LIGHT};
                background: {Colors.SURFACE_LIGHT};
            }}
            QComboBox:focus {{
                border-color: {Colors.PRIMARY};
                background: white;
            }}
            QComboBox::drop-down {{
                border: none;
                width: 40px;
                padding-right: 16px;
            }}
            QComboBox::down-arrow {{
                image: url(ui/icons/controls/chevron-down.svg);
                width: 12px;
                height: 12px;
                margin-right: 16px;
            }}
            QComboBox QAbstractItemView {{
                outline: none;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                background: white;
                selection-background-color: transparent;
            }}
            QComboBox QAbstractItemView::item {{
                height: 36px;
                padding: 8px 12px;
                margin: 2px 4px;
                border-radius: 4px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QComboBox QAbstractItemView::item:hover {{
                background: {Colors.HOVER};
                color: {Colors.PRIMARY};
            }}
            QComboBox QAbstractItemView::item:selected {{
                background: {Colors.PRIMARY_LIGHT};
                color: {Colors.PRIMARY};
            }}
            QComboBox QScrollBar:vertical {{
                width: 8px;
                background: transparent;
                margin: 4px 0px;
            }}
            QComboBox QScrollBar::handle:vertical {{
                background: {Colors.BORDER};
                border-radius: 4px;
                min-height: 32px;
            }}
            QComboBox QScrollBar::handle:vertical:hover {{
                background: {Colors.BORDER_DARK};
            }}
            QComboBox QScrollBar::add-line:vertical,
            QComboBox QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QComboBox QScrollBar::add-page:vertical,
            QComboBox QScrollBar::sub-page:vertical {{
                background: none;
            }}
            QComboBox QLineEdit {{
                color: {Colors.TEXT_PRIMARY};
                background: transparent;
                border: none;
                padding: 0;
            }}
            QComboBox QLineEdit::placeholder {{
                color: {Colors.TEXT_SECONDARY};
            }}
        """

    @classmethod
    def get_config_text_edit_style(cls) -> str:
        """获取配置文本编辑器样式"""
        return f"""
            QTextEdit {{
                background-color: {Colors.SURFACE_DARK};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 8px;
                color: {Colors.TEXT_PRIMARY};
                font-family: 'Courier New', monospace;
            }}
        """