"""菜单样式定义"""
from ..base.colors import Colors

class MenuStyle:
    @classmethod
    def get_menu_style(cls) -> str:
        """获取菜单基础样式"""
        return f"""
            QMenu {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 4px;
                padding: 4px 0px;
            }}
            QMenu::item {{
                padding: 8px 24px;
                font-size: 16px;
            }}
            QMenu::item:selected {{
                background-color: {Colors.SURFACE_LIGHT};
                color: {Colors.PRIMARY};
            }}
            QMenu::separator {{
                height: 1px;
                background-color: {Colors.BORDER};
                margin: 4px 0px;
            }}
        """

    @classmethod
    def get_menu_bar_style(cls) -> str:
        """获取菜单栏样式"""
        return f"""
            QMenuBar {{
                background-color: {Colors.SURFACE};
                border-bottom: 1px solid {Colors.BORDER};
                min-height: 30px;
            }}
            QMenuBar::item {{
                padding: 8px 12px;
                margin: 0;
                color: {Colors.TEXT_PRIMARY};
                font-size: 16px;
            }}
            QMenuBar::item:selected {{
                background-color: {Colors.SURFACE_LIGHT};
                color: {Colors.PRIMARY};
            }}
        """

    @classmethod
    def get_menu_widget_style(cls) -> str:
        """获取左侧菜单区域样式"""
        return f"""
            QWidget {{
                background: {Colors.SURFACE};
                border-right: 1px solid {Colors.BORDER};
                min-width: 80px;
                padding: 12px 0;
            }}
            QPushButton {{
                text-align: left;
                padding: 12px 16px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                color: {Colors.PRIMARY};
                margin: 4px 8px;
                min-height: 36px;
            }}
            QPushButton:hover {{
                background: {Colors.SURFACE_LIGHT};
            }}
            QPushButton:checked {{
                background: {Colors.PRIMARY};
                color: white;
                font-weight: 600;
            }}
            QPushButton::icon {{
                margin-right: 8px;
                margin-left: 2px;
                padding: 0;
                width: 18px;
                height: 18px;
            }}
        """

    @classmethod
    def get_stacked_widget_style(cls) -> str:
        """获取堆叠窗口样式"""
        return f"""
            QStackedWidget {{
                background: {Colors.BACKGROUND};
            }}
        """

    @classmethod
    def get_user_menu_style(cls) -> str:
        """获取用户菜单样式"""
        return f"""
            QMenu {{
                background-color: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 4px 0px;
            }}
            QMenu::item {{
                padding: 8px 24px;
                font-size: 16px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QMenu::item:selected {{
                background-color: {Colors.SURFACE_LIGHT};
                color: {Colors.PRIMARY};
            }}
            QMenu::separator {{
                height: 1px;
                background-color: {Colors.BORDER};
                margin: 4px 0px;
            }}
        """ 