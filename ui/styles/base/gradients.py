"""渐变样式定义"""
from .colors import Colors

class Gradients:
    # 主色调渐变
    PRIMARY = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {Colors.PRIMARY}, stop:1 {Colors.PRIMARY_DARK})"
    PRIMARY_HOVER = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {Colors.PRIMARY_LIGHT}, stop:1 {Colors.PRIMARY})"
    PRIMARY_PRESSED = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {Colors.PRIMARY_DARK}, stop:1 {Colors.PRIMARY})"

    # 状态渐变
    SUCCESS = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {Colors.SUCCESS}, stop:1 {Colors.SUCCESS})"
    ERROR = f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {Colors.ERROR}, stop:1 {Colors.ERROR})" 