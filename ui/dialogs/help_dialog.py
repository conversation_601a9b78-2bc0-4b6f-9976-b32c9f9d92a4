"""帮助对话框"""
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, 
    QPushButton, QFrame, QHBoxLayout,
    QWidget
)
from PyQt6.QtGui import QDesktopServices, QIcon, QPixmap, QCursor, QMouseEvent
from ..styles import Colors, ButtonStyle, DialogStyle, LabelStyle

class ClickableLabel(QLabel):
    """可点击的标签"""
    def __init__(self, text: str, link: str, parent=None):
        super().__init__(text, parent)
        self.link = link
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
    def mousePressEvent(self, event: QMouseEvent) -> None:
        if event.button() == Qt.MouseButton.LeftButton:
            QDesktopServices.openUrl(QUrl(self.link))
        super().mousePressEvent(event)

class HelpDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        # 设置窗口属性
        self.setWindowTitle("帮助")
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        self.setFixedSize(600, 400)
        
        # 设置布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(32, 32, 32, 32)
        
        # 标题区域
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        title = QLabel("联系我们")
        title.setStyleSheet(f"""
            font-size: 24px;
            font-weight: 600;
            color: {Colors.TEXT_PRIMARY};
        """)
        title_layout.addWidget(title)
        title_layout.addStretch()
        
        # 添加标题区域
        layout.addWidget(title_widget)
        
        # 说明文本
        description = QLabel("如果您在使用过程中遇到任何问题，请联系：")
        description.setWordWrap(True)
        description.setStyleSheet(f"""
            font-size: 14px;
            color: {Colors.TEXT_SECONDARY};
            margin-bottom: 8px;
        """)
        layout.addWidget(description)
        
        # 联系信息卡片
        contact_card = self._create_contact_card()
        layout.addWidget(contact_card)
        
        layout.addStretch()
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(f"""
            background: {Colors.BORDER};
            margin: 8px 0;
        """)
        layout.addWidget(separator)
        
        # 底部按钮
        button = QPushButton("确定")
        button.setFixedSize(120, 36)
        button.clicked.connect(self.accept)
        button.setStyleSheet(f"""
            QPushButton {{
                background: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background: {Colors.PRIMARY_HOVER};
            }}
            QPushButton:pressed {{
                background: {Colors.PRIMARY_PRESSED};
            }}
        """)
        layout.addWidget(button, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 应用对话框样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {Colors.BACKGROUND};
                border-radius: 12px;
            }}
        """)
        
    def _create_contact_card(self) -> QWidget:
        """创建联系信息卡片"""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background: {Colors.BACKGROUND_LIGHT};
                border-radius: 8px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(16)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # 技术支持
        tech_support = self._create_contact_item(
            "技术支持",
            "hejiabei.oxep",
            "ui/icons/settings/support.svg",
            "lark://messenger/chat/hejiabei.oxep"  # 使用正确的飞书客户端聊天协议
        )
        layout.addWidget(tech_support)
        
        # 邮箱
        email = self._create_contact_item(
            "邮箱",
            "<EMAIL>",
            "ui/icons/settings/email.svg",
            "mailto:<EMAIL>"
        )
        layout.addWidget(email)
        
        return card
        
    def _create_contact_item(self, title: str, value: str, icon_path: str, link: str = None) -> QWidget:
        """创建联系信息项"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)
        
        # 图标
        icon_label = QLabel()
        icon_label.setFixedSize(20, 20)
        icon_label.setStyleSheet(f"""
            border-image: url({icon_path});
        """)
        layout.addWidget(icon_label)
        
        # 信息区域
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {Colors.TEXT_SECONDARY};
            font-size: 12px;
        """)
        info_layout.addWidget(title_label)
        
        # 值
        if link:
            value_label = ClickableLabel(value, link)
            value_label.setStyleSheet(f"""
                color: {Colors.LINK};
                font-size: 14px;
                font-weight: 500;
                text-decoration: none;
                padding: 2px 0;
            """)
            
            # 添加悬停效果
            def enter_event(event):
                value_label.setStyleSheet(f"""
                    color: {Colors.LINK_HOVER};
                    font-size: 14px;
                    font-weight: 500;
                    text-decoration: underline;
                    padding: 2px 0;
                """)
            
            def leave_event(event):
                value_label.setStyleSheet(f"""
                    color: {Colors.LINK};
                    font-size: 14px;
                    font-weight: 500;
                    text-decoration: none;
                    padding: 2px 0;
                """)
            
            value_label.enterEvent = enter_event
            value_label.leaveEvent = leave_event
        else:
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                font-weight: 500;
                padding: 2px 0;
            """)
        info_layout.addWidget(value_label)
        
        layout.addWidget(info_widget)
        layout.addStretch()
        
        return widget 