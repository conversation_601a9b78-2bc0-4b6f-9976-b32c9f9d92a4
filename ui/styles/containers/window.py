'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-11 13:17:49
FilePath: /global_rtc_client/ui/styles/containers/window.py
Description: 
'''
"""窗口样式定义"""
from ..base.colors import Colors
from ..base.fonts import Fonts

class WindowStyle:
    @classmethod
    def get_window_base_style(cls) -> str:
        """获取窗口基础样式"""
        return f"""
            QMainWindow {{
                background: {Colors.BACKGROUND};
            }}
            QWidget {{
                background: {Colors.BACKGROUND};
                color: {Colors.TEXT_PRIMARY};
                font-family: {Fonts.get_system_font_family()};
            }}
        """

    @classmethod
    def get_main_window_base_style(cls) -> str:
        """获取主窗口基础样式"""
        return f"""
            QMainWindow {{
                background: {Colors.BACKGROUND};
                border: none;
            }}
            QWidget {{
                background: {Colors.BACKGROUND};
                color: {Colors.TEXT_PRIMARY};
                font-family: {Fonts.get_system_font_family()};
            }}
            
            /* 标题栏按钮样式 */
            QMainWindow::title {{
                background: transparent;
            }}
            
            /* 保持系统按钮可见 */
            #qt_titlebar_minimizebutton,
            #qt_titlebar_maximizebutton,
            #qt_titlebar_closebutton {{
                background: transparent;
                border: none;
            }}
            
            #qt_titlebar_minimizebutton:hover,
            #qt_titlebar_maximizebutton:hover {{
                background: rgba(0, 0, 0, 0.1);
            }}
            
            #qt_titlebar_closebutton:hover {{
                background: #ff4d4d;
            }}
        """

    @classmethod
    def get_toolbar_style(cls) -> str:
        """获取工具栏样式"""
        return f"""
            QWidget {{
                background: white;
            }}
        """

    @classmethod
    def get_auth_window_style(cls) -> str:
        """获取认证窗口样式"""
        return f"""
            QMainWindow {{
                background: {Colors.BACKGROUND};
                border: 1px solid {Colors.BORDER};
                border-radius: 12px;
            }}
            QWidget {{
                background: {Colors.BACKGROUND};
                color: {Colors.TEXT_PRIMARY};
                font-family: {Fonts.get_system_font_family()};
            }}
            QComboBox {{
                background: white;
            }}
            QPushButton {{
                background: {Colors.PRIMARY};
            }}
        """

    @classmethod
    def get_content_container_style(cls) -> str:
        """获取内容容器样式"""
        return f"""
            QWidget#contentContainer {{
                background: {Colors.SURFACE};
            }}
        """

    @classmethod
    def get_toolbar_separator_style(cls) -> str:
        """获取工具栏分隔线样式"""
        return f"""
            QFrame {{
                background: {Colors.BORDER};
                width: 1px;
                margin: 12px 0;
            }}
        """

    @classmethod
    def get_menu_widget_style(cls) -> str:
        """获取左侧菜单区域样式"""
        return f"""
            QWidget {{
                background: {Colors.BACKGROUND};
                min-width: 200px;
            }}
            QPushButton {{
                text-align: left;
                padding: 12px 24px;
                border: none;
                border-radius: 0;
                font-size: 14px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QPushButton:hover {{
                background: {Colors.SURFACE_LIGHT};
                color: {Colors.PRIMARY};
            }}
            QPushButton:checked {{
                background: {Colors.PRIMARY_LIGHT};
                color: {Colors.PRIMARY};
                font-weight: 500;
            }}
        """

    @classmethod
    def get_stacked_widget_style(cls) -> str:
        """获取堆叠窗口样式"""
        return f"""
            QStackedWidget {{
                background: {Colors.BACKGROUND};
            }}
        """

    @classmethod
    def get_settings_menu_style(cls) -> str:
        """获取设置菜单样式"""
        return f"""
            QMenu {{
                background-color: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 8px 0;
            }}
            QMenu::item {{
                padding: 8px 24px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QMenu::item:selected {{
                background-color: {Colors.SURFACE_LIGHT};
            }}
            QMenu::separator {{
                height: 1px;
                background: {Colors.BORDER};
                margin: 4px 0;
            }}
        """

    @classmethod
    def get_tab_widget_style(cls) -> str:
        """获取选项卡样式"""
        return f"""
            QTabWidget {{
                background: transparent;
            }}
            QTabWidget::pane {{
                border: none;
                background: transparent;
            }}
            QTabBar::tab {{
                padding: 8px 16px;
                color: {Colors.TEXT_SECONDARY};
                border: none;
                background: transparent;
                min-width: 100px;
                font-size: 14px;
            }}
            QTabBar::tab:selected {{
                color: {Colors.PRIMARY};
                border-bottom: 2px solid {Colors.PRIMARY};
                font-weight: 500;
            }}
            QTabBar::tab:hover {{
                color: {Colors.PRIMARY};
            }}
        """

    @classmethod
    def get_scroll_area_style(cls) -> str:
        """获取滚动区域样式"""
        return f"""
            QScrollArea {{
                border: none;
                background: transparent;
            }}
            QScrollBar:vertical {{
                border: none;
                background: {Colors.SURFACE_LIGHT};
                width: 8px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background: {Colors.BORDER};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::add-line:vertical {{
                height: 0px;
            }}
            QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar:horizontal {{
                border: none;
                background: {Colors.SURFACE_LIGHT};
                height: 8px;
                margin: 0;
            }}
            QScrollBar::handle:horizontal {{
                background: {Colors.BORDER};
                border-radius: 4px;
                min-width: 20px;
            }}
            QScrollBar::add-line:horizontal {{
                width: 0px;
            }}
            QScrollBar::sub-line:horizontal {{
                width: 0px;
            }}
        """ 