#!/bin/bash

# Global Business Client 启动脚本
# 这是一个独立的 gb 脚本模板，用于手动安装或测试

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Global Business Client"
    echo ""
    echo "用法: gb [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h         显示此帮助信息"
    echo "  --version, -v      显示版本信息"
    echo "  --reinstall        重新安装应用程序（更新代码并执行完整安装）"
    echo ""
    echo "功能特性:"
    echo "  - 启动前自动拉取最新代码"
    echo "  - 优雅退出支持 (Cmd+C/Ctrl+C)"
    echo ""
    echo "示例:"
    echo "  gb                 启动应用程序（自动更新代码）"
    echo "  gb --reinstall     重新安装应用程序（更新代码并完整安装）"
    echo ""
}

# 显示版本信息
show_version() {
    echo "Global Business Client v1.0.0"
    echo "Copyright (c) 2025 Global Business Team"
}

# 缓存配置
CACHE_DIR="$HOME/.gb_cache"
CACHE_FILE="$CACHE_DIR/project_path"

# 创建缓存目录
create_cache_dir() {
    if [ ! -d "$CACHE_DIR" ]; then
        mkdir -p "$CACHE_DIR"
    fi
}

# 获取项目根目录
get_project_root() {
    if [ -f "$CACHE_FILE" ]; then
        PROJECT_ROOT=$(cat "$CACHE_FILE" 2>/dev/null)
        if [ -n "$PROJECT_ROOT" ]; then
            log_info "使用缓存的项目目录: $PROJECT_ROOT"
            return 0
        fi
    fi

    log_error "无法找到项目目录缓存"
    log_error "请重新运行安装脚本: sh install.sh"
    exit 1
}

# 检查虚拟环境
check_virtual_env() {
    VENV_PYTHON="$PROJECT_ROOT/.venv/bin/python3.9"
    
    if [ ! -f "$VENV_PYTHON" ]; then
        log_error "虚拟环境不存在: $VENV_PYTHON"
        log_error "请运行安装脚本重新创建虚拟环境"
        exit 1
    fi
    
    log_info "虚拟环境检查通过"
}

# 检查应用文件
check_app_files() {
    if [ ! -f "$PROJECT_ROOT/app.py" ]; then
        log_error "应用主文件不存在: $PROJECT_ROOT/app.py"
        exit 1
    fi
    
    log_info "应用文件检查通过"
}

# 设置环境变量
setup_environment() {
    export PYTHONPATH="$PROJECT_ROOT"
    export QT_AUTO_SCREEN_SCALE_FACTOR="1"
    export QT_ENABLE_HIGHDPI_SCALING="1"
    export QT_LOGGING_RULES="*.debug=false;qt.qpa.*=false"

    # macOS 特定设置
    if [[ "$OSTYPE" == "darwin"* ]]; then
        export QT_MAC_WANTS_LAYER="1"
        export QT_QPA_PLATFORM="cocoa"
        export QT_MAC_WANTS_METAL="1"
    fi

    log_info "环境变量设置完成"
}

# 更新代码仓库
update_code_repository() {
    log_info "检查代码更新..."

    # 切换到项目目录
    cd "$PROJECT_ROOT"

    # 检查是否是 Git 仓库
    if [ ! -d ".git" ]; then
        log_info "当前目录不是 Git 仓库，跳过代码更新"
        return 0
    fi

    # 检查 Git 命令是否可用
    if ! command -v git &> /dev/null; then
        log_warning "Git 命令不可用，跳过代码更新"
        return 0
    fi

    # 获取当前分支名称
    local current_branch
    current_branch=$(git symbolic-ref --short HEAD 2>/dev/null || echo "HEAD")

    if [ "$current_branch" = "HEAD" ]; then
        log_warning "当前处于分离头指针状态，跳过代码更新"
        return 0
    fi

    log_info "当前分支: $current_branch"

    # 检查是否有未提交的修改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        log_warning "检测到本地有未提交的修改"
        echo ""
        echo "选择操作:"
        echo "1. 强制拉取并覆盖本地修改"
        echo "2. 暂存修改后拉取最新代码"
        echo "3. 跳过更新（使用当前版本）"
        echo ""
        read -p "请输入选择 (1/2/3): " git_choice

        case $git_choice in
            1)
                log_info "强制拉取最新代码..."
                if git fetch && git reset --hard origin/$current_branch; then
                    log_success "代码强制更新成功！本地修改已被丢弃"
                else
                    log_warning "强制拉取最新代码失败，继续使用当前代码..."
                fi
                ;;
            2)
                log_info "暂存本地修改..."
                if git stash save "自动暂存 - $(date)"; then
                    log_info "拉取最新代码..."
                    if git fetch && git pull origin $current_branch; then
                        log_success "代码更新成功"
                        log_info "恢复本地修改..."
                        if git stash pop; then
                            # 检查是否有冲突
                            if git diff --name-only --diff-filter=U | grep -q .; then
                                log_warning "合并冲突，请手动解决..."
                            else
                                log_success "本地修改已恢复"
                            fi
                        else
                            log_warning "恢复本地修改失败，请手动处理"
                        fi
                    else
                        log_error "拉取最新代码失败"
                        log_info "恢复本地修改..."
                        git stash pop
                    fi
                else
                    log_warning "暂存本地修改失败，跳过更新步骤..."
                fi
                ;;
            3)
                log_info "跳过更新步骤，保留本地修改..."
                ;;
            *)
                log_warning "无效的选择，跳过更新步骤..."
                ;;
        esac
    else
        # 没有本地修改，直接拉取
        log_info "拉取最新代码..."
        if git fetch && git pull origin $current_branch; then
            log_success "代码更新成功！"
        else
            log_warning "拉取最新代码失败，继续使用当前代码..."
        fi
    fi

    echo ""
}

# 配置 iOS 设备（仅在需要时）
setup_ios_devices() {
    # 检查 idevice_id 命令是否可用
    if ! command -v idevice_id &> /dev/null; then
        log_info "idevice_id 不可用，跳过 iOS 设备配置"
        return 0
    fi

    # 检查 cosign 命令是否可用
    if ! command -v cosign &> /dev/null; then
        log_warning "cosign 不可用，无法配置 iOS 设备"
        log_warning "请运行安装脚本安装 cosign: ./install.sh"
        return 0
    fi

    # 创建 iOS 配置目录
    mkdir -p ~/.shoots/ios/

    # 检查是否有连接的 iOS 设备
    local connected_devices
    connected_devices=$(idevice_id -l 2>/dev/null)

    if [ -z "$connected_devices" ]; then
        log_info "没有检测到连接的 iOS 设备"
        return 0
    fi

    # 检查是否需要配置
    local need_config=false
    local unconfigured_devices=()

    while IFS= read -r device_id; do
        if [ -n "$device_id" ]; then
            local xcconfig_file="$HOME/.shoots/ios/${device_id}.xcconfig"
            if [ ! -f "$xcconfig_file" ] || [ ! -s "$xcconfig_file" ]; then
                need_config=true
                unconfigured_devices+=("$device_id")
            fi
        fi
    done <<< "$connected_devices"

    if [ "$need_config" = false ]; then
        log_info "所有连接的 iOS 设备已配置"
        return 0
    fi

    # 需要配置设备
    log_info "检测到 ${#unconfigured_devices[@]} 个未配置的 iOS 设备"

    # 提示用户选择 scope-name
    echo ""
    echo "请选择 scope-name:"
    echo "1. TikTok - 每人 iPhone 注册数 3"
    echo "2. 抖音 - 每人 iPhone 注册数 4"
    echo "3. 头条 - 每人 iPhone 注册数 3"
    echo "4. 直播 - 每人 iPhone 注册数 10"
    echo "5. 西瓜视频 - 每人 iPhone 注册数 3"
    echo "6. 剪映 - 每人 iPhone 注册数 10"
    echo "7. 幸福里 - 每人 iPhone 注册数 5"
    echo ""
    read -p "请输入选择 (1-7): " choice

    local scope_name
    case $choice in
        1) scope_name="TikTok" ;;
        2) scope_name="DouYin" ;;
        3) scope_name="TouTiao" ;;
        4) scope_name="Live" ;;
        5) scope_name="XiguaVideo" ;;
        6) scope_name="JianYing" ;;
        7) scope_name="XingFuLi" ;;
        *)
            log_warning "无效的选择，使用默认值 TikTok"
            scope_name="TikTok"
            ;;
    esac

    log_info "使用 scope-name: $scope_name"

    # 为每个未配置的设备创建 xcconfig
    for device_id in "${unconfigured_devices[@]}"; do
        local xcconfig_file="$HOME/.shoots/ios/${device_id}.xcconfig"

        log_info "正在为设备 $device_id 创建配置..."

        # 创建空的 xcconfig 文件
        touch "$xcconfig_file"

        # 使用 cosign 配置设备
        if cosign --scope-name "$scope_name" \
                  --device-udid "$device_id" \
                  --xcconfig-path "$xcconfig_file" \
                  --export-path "$HOME/.shoots/ios" \
                  --ignore-mac 2>/dev/null; then
            log_success "设备 $device_id 配置成功"
        else
            log_error "设备 $device_id 配置失败"
            # 删除失败的配置文件
            rm -f "$xcconfig_file"
        fi
    done

    log_success "iOS 设备配置完成"
}

# 启动应用程序
start_app() {
    log_info "正在启动应用程序..."

    # 切换到项目目录
    cd "$PROJECT_ROOT"

    "$VENV_PYTHON" app.py
}

# 重新安装应用程序
reinstall_app() {
    log_info "正在重新安装应用程序..."

    # 首先更新代码到最新版本
    update_code_repository

    # 检查 install.sh 文件是否存在
    local install_script="$PROJECT_ROOT/install.sh"
    if [ ! -f "$install_script" ]; then
        log_error "安装脚本不存在: $install_script"
        log_error "请确保项目目录完整，或手动下载安装脚本"
        exit 1
    fi

    # 检查安装脚本是否可执行
    if [ ! -x "$install_script" ]; then
        log_info "设置安装脚本执行权限..."
        chmod +x "$install_script"
    fi

    log_info "执行安装脚本..."
    echo ""
    echo "=========================================="
    echo "开始执行完整安装流程"
    echo "=========================================="
    echo ""

    # 切换到项目目录并执行安装脚本
    cd "$PROJECT_ROOT"

    # 执行安装脚本
    if bash "$install_script"; then
        echo ""
        echo "=========================================="
        log_success "重新安装完成！"
        echo "=========================================="
        echo ""
        log_info "现在可以使用 gb 命令启动应用程序"
    else
        echo ""
        echo "=========================================="
        log_error "重新安装失败！"
        echo "=========================================="
        echo ""
        log_error "请检查安装日志并手动解决问题"
        exit 1
    fi
}

# 主函数
main() {
    # 解析命令行参数
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --version|-v)
            show_version
            exit 0
            ;;
        --reinstall)
            get_project_root
            reinstall_app
            ;;
        "")
            # 默认启动
            get_project_root
            update_code_repository
            check_virtual_env
            check_app_files
            setup_environment
            setup_ios_devices
            start_app "$@"
            ;;
        *)
            log_error "未知选项: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'log_error "启动过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
