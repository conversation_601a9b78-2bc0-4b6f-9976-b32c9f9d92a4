'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-27 14:41:05
FilePath: /global_rtc_client/utils/perf/profile_analyzer.py
Description: CPU Profile 分析器 - 负责添加和分析
'''
from bytedance.profile_toolkit.tool.analyzer.cloud_analyzer import CloudAnalyzer
from typing import Dict, Any
from utils.common.log_utils import get_logger

logger = get_logger(__name__)

class ProfileAnalyzer:
    """CPU Profile 分析器"""
    
    def __init__(self, rule_tags, common_info: Dict[str, str]):
        """初始化分析器
        Args:
            common_info: 通用信息，包含 app、app_url、device_level、creator、device_id、device_name 等
        """
        self.trigger = CloudAnalyzer(
            rule_tags=rule_tags,
            common_info=common_info
        )
        logger.info("[CPU Profile] 分析器初始化完成")
    
    def add_trace(self, trace_path: str, profile_info: Dict[str, Any] = None) -> None:
        """添加trace
        Args:
            trace_path: trace文件路径
            profile_info: profile信息，包含 profile_id、profile_duration、case_name、case_desc 等
        """
        if not profile_info:
            profile_info = {}
            
        logger.debug(f"添加CPU Profile trace: {trace_path}，信息: {profile_info}")
        self.trigger.add_trace(
            trace_path=trace_path,
            trace_info=profile_info
        )
        logger.info(f"[CPU Profile] 添加trace完成: {trace_path}")
    
    def trigger_analysis(self) -> str:
        """触发分析
        Returns:
            任务URL
        """
        logger.debug("触发CPU Profile分析任务")
        job_url = self.trigger.start_job()
        logger.info(f"[CPU Profile] 分析任务已提交，URL: {job_url}")
        return job_url 
    
if __name__ == "__main__":
    profile_analyzer = ProfileAnalyzer(rule_tags=[], common_info={})
    profile_analyzer.trigger_analysis()