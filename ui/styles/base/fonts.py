'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-11 14:10:49
FilePath: /global_rtc_client/ui/styles/base/fonts.py
Description: 字体定义
'''
"""字体定义"""

class Fonts:
    # 字体大小定义
    SIZE_SMALL = "12px"
    SIZE_NORMAL = "14px"
    SIZE_MEDIUM = "16px"
    SIZE_LARGE = "18px"
    SIZE_XLARGE = "20px"
    SIZE_TITLE = "28px"
    SIZE_HEADER = "36px"

    # 字重定义
    WEIGHT_NORMAL = "400"
    WEIGHT_MEDIUM = "500"
    WEIGHT_SEMIBOLD = "600"
    WEIGHT_BOLD = "700"

    # 系统字体定义
    SYSTEM_FONTS = (
        '".AppleSystemUIFont"',  # macOS
        '"PingFang SC"',         # macOS 中文
        '"Microsoft YaHei"',     # Windows 中文
        '"Segoe UI"',           # Windows
        '"Helvetica Neue"',     # macOS/Linux
        'Arial',                # 通用
        'sans-serif'            # 后备字体
    )

    # 等宽字体定义
    MONOSPACE_FONTS = (
        '"SF Mono"',            # macOS
        '"Monaco"',             # macOS 备选
        '"Source Code Pro"',    # 跨平台
        '"Ubuntu Mono"',        # Linux
        '"Courier New"',        # 通用
        'monospace'             # 后备字体
    )

    @classmethod
    def get_system_font_family(cls) -> str:
        """获取系统字体族"""
        return ", ".join(cls.SYSTEM_FONTS)

    @classmethod
    def get_monospace_font_family(cls) -> str:
        """获取等宽字体族"""
        return ", ".join(cls.MONOSPACE_FONTS)

    @classmethod
    def get_font_style(cls, size: str = SIZE_NORMAL, weight: str = WEIGHT_NORMAL) -> str:
        """获取字体样式
        
        Args:
            size: 字体大小
            weight: 字体粗细
            
        Returns:
            str: 字体样式字符串
        """
        return f"""
            font-family: {cls.get_system_font_family()};
            font-size: {size};
            font-weight: {weight};
        """

    @classmethod
    def get_monospace_style(cls, size: str = SIZE_NORMAL) -> str:
        """获取等宽字体样式
        
        Args:
            size: 字体大小
            
        Returns:
            str: 等宽字体样式字符串
        """
        return f"""
            font-family: {cls.get_monospace_font_family()};
            font-size: {size};
        """ 