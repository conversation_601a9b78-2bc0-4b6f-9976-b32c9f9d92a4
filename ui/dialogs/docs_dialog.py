"""文档对话框"""
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, 
    QPushButton, QFrame, QHBoxLayout,
    QWidget
)
from PyQt6.QtGui import QDesktopServices, QIcon, QPixmap, QCursor, QMouseEvent
from ..styles import Colors, ButtonStyle, DialogStyle, LabelStyle

class ClickableLabel(QLabel):
    """可点击的标签"""
    def __init__(self, text: str, link: str, parent=None):
        super().__init__(text, parent)
        self.link = link
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
    def mousePressEvent(self, event: QMouseEvent) -> None:
        if event.button() == Qt.MouseButton.LeftButton:
            QDesktopServices.openUrl(QUrl(self.link))
        super().mousePressEvent(event)

class DocsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        # 设置窗口属性
        self.setWindowTitle("文档")
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint)
        self.setFixedSize(600, 520)
        
        # 设置布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(32, 32, 32, 32)
        
        # 标题区域
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        title = QLabel("帮助文档")
        title.setStyleSheet(f"""
            font-size: 24px;
            font-weight: 600;
            color: {Colors.TEXT_PRIMARY};
        """)
        title_layout.addWidget(title)
        title_layout.addStretch()
        
        # 添加标题区域
        layout.addWidget(title_widget)
        
        # 说明文本
        description = QLabel("欢迎使用流媒体国际客户端！以下是一些帮助您快速上手的文档链接：")
        description.setWordWrap(True)
        description.setStyleSheet(f"""
            font-size: 14px;
            color: {Colors.TEXT_SECONDARY};
            margin-bottom: 8px;
        """)
        layout.addWidget(description)
        
        # 文档链接卡片
        docs_card = self._create_docs_card()
        layout.addWidget(docs_card)
        
        layout.addStretch()
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(f"""
            background: {Colors.BORDER};
            margin: 8px 0;
        """)
        layout.addWidget(separator)
        
        # 底部按钮
        button = QPushButton("关闭")
        button.setFixedSize(120, 36)
        button.clicked.connect(self.accept)
        button.setStyleSheet(f"""
            QPushButton {{
                background: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background: {Colors.PRIMARY_HOVER};
            }}
            QPushButton:pressed {{
                background: {Colors.PRIMARY_PRESSED};
            }}
        """)
        layout.addWidget(button, 0, Qt.AlignmentFlag.AlignCenter)
        
        # 应用对话框样式
        self.setStyleSheet(f"""
            QDialog {{
                background: {Colors.BACKGROUND};
                border-radius: 12px;
            }}
        """)
        
    def _create_docs_card(self) -> QWidget:
        """创建文档链接卡片"""
        card = QWidget()
        card.setStyleSheet(f"""
            QWidget {{
                background: {Colors.BACKGROUND_LIGHT};
                border-radius: 8px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(16)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # 文档链接列表
        docs_list = [
            {
                "title": "快速入门指南",
                "url": "https://bytedance.larkoffice.com/docx/getting_started",
                "icon": "ui/icons/navigation/docs.svg"
            },
            {
                "title": "性能自动化使用文档",
                "url": "https://bytedance.larkoffice.com/docx/PRmcdNw3toEobnxLey8clsRVn0d",
                "icon": "ui/icons/navigation/docs.svg"
            },
            {
                "title": "常见问题解答(FAQ)",
                "url": "https://bytedance.larkoffice.com/docx/faq",
                "icon": "ui/icons/navigation/docs.svg"
            },
            {
                "title": "API参考文档",
                "url": "https://bytedance.larkoffice.com/docx/api_reference",
                "icon": "ui/icons/navigation/docs.svg"
            },
            {
                "title": "最佳实践指南",
                "url": "https://bytedance.larkoffice.com/docx/best_practices",
                "icon": "ui/icons/navigation/docs.svg"
            }
        ]
        
        # 创建文档链接
        for doc in docs_list:
            doc_item = self._create_doc_item(doc["title"], doc["url"], doc["icon"])
            layout.addWidget(doc_item)
        
        return card
        
    def _create_doc_item(self, title: str, url: str, icon_path: str) -> QWidget:
        """创建文档链接项"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)
        
        # 图标
        icon_label = QLabel()
        icon_label.setFixedSize(20, 20)
        icon_label.setStyleSheet(f"""
            border-image: url({icon_path});
        """)
        layout.addWidget(icon_label)
        
        # 标题（可点击）
        title_label = ClickableLabel(title, url)
        title_label.setStyleSheet(f"""
            color: {Colors.LINK};
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            padding: 2px 0;
        """)
        
        # 添加悬停效果
        def enter_event(event):
            title_label.setStyleSheet(f"""
                color: {Colors.LINK_HOVER};
                font-size: 14px;
                font-weight: 500;
                text-decoration: underline;
                padding: 2px 0;
            """)
        
        def leave_event(event):
            title_label.setStyleSheet(f"""
                color: {Colors.LINK};
                font-size: 14px;
                font-weight: 500;
                text-decoration: none;
                padding: 2px 0;
            """)
        
        title_label.enterEvent = enter_event
        title_label.leaveEvent = leave_event
        
        layout.addWidget(title_label)
        layout.addStretch()
        
        # 添加箭头图标
        arrow_label = QLabel()
        arrow_label.setFixedSize(16, 16)
        arrow_label.setStyleSheet("""
            border-image: url(ui/icons/actions/arrow_right.svg);
        """)
        layout.addWidget(arrow_label)
        
        return widget 