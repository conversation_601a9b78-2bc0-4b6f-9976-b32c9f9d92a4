import os
import json
import asyncio
import shutil
from pathlib import Path
from typing import Dict, Optional, List
from utils.common.log_utils import get_logger
from apis.perf_api import perf_api
from core.settings_manager import settings_manager
from utils.common.network_utils import network_utils
from utils.common.android_device import android_device
from utils.common.ios_device import ios_device
from utils.perf.serial_control import serial_control
from config.constants import *
from utils.common.file_utils import write_json
from datetime import datetime, timedelta

logger = get_logger(__name__)

class TaskHelper:
    """任务辅助工具"""

    def __init__(self):
        """初始化任务辅助工具"""
        # 基本属性
        self.task_id = None
        self.sub_task_id = None
        self.sub_task_detail = None
        self.sub_task_context = None
        self.client_id = None
        self.business_id = None
        self.case_group = []
        self.app_group = {}
        self.device_group = {}
        self.account_group = {}
        self.config_group = {}
        
        # 资源控制相关属性
        self.occupied_resources = {'devices': [], 'accounts': []}
        self.error_code = None
        self.error_detail = None
        
        # 视频播放相关
        self._current_video_process = None
        self.video_task = None

        # 基础路径配置
        self.base_data_dir = settings_manager.get('storage.paths.base.data', 'data')
        self.base_repo_dir = settings_manager.get('storage.paths.base.repo', 'repos')
        self.base_logs_dir = settings_manager.get('storage.paths.base.log', 'logs')
        
        # 任务相关路径配置
        self.tasks_base_dir = settings_manager.get('storage.paths.base.tasks', '.tasks')
        self.task_resources_dir = settings_manager.get('storage.paths.task.resources', 'resources')
        self.task_results_dir = settings_manager.get('storage.paths.task.results', 'results')

        # 文件相关配置
        self.task_file_name = settings_manager.get('storage.paths.files.task', 'task.json')
        self.serial_file_name = settings_manager.get('storage.paths.files.serial', 'serial.json')
        
        # 构建完整路径
        self.task_file_path = Path(os.path.join(self.base_data_dir, self.task_file_name))
        self.serial_file_path = Path(os.path.join(self.base_data_dir, self.serial_file_name))

        # 仓库相关配置
        self.perf_case_repo_name = 'global_business_perf'
        self.perf_case_repo_url = settings_manager.get(
            'repo.perf_case_url',
            "******************:bytertc_i18n/global_business_perf.git"
        )
        self.perf_case_repo_path = Path(os.path.join(self.base_repo_dir, self.perf_case_repo_name))
    
    def _calculate_device_priority_score(self, device: Dict) -> float:
        """计算设备优先级得分
        
        优先级评分规则：
        1. 基础分值为100分
        2. 电量得分（权重：40%）
           - 电量 >= 80%: +40分
           - 50% <= 电量 < 80%: +20分
           - 电量 < 50%: +0分
        3. 品牌得分（权重：25%）
           - 非华为设备: +25分
           - 华为设备: +0分
        4. 串口得分（权重：20%）
           - 无串口: +20分
           - 有串口: +0分
        5. 系统版本得分（权重：15%）
           - 根据系统版本号计算，最高+15分
        
        Args:
            device: 设备信息字典
            
        Returns:
            float: 优先级得分，分值范围0-100
        """
        score = 0.0
        
        # 1. 电量得分 (40分)
        battery_level = device.get('battery_level', 0)
        if battery_level >= 80:
            score += 40
        elif battery_level >= 50:
            score += 20
            
        # 2. 品牌得分 (25分)
        if not device.get('brand', '').lower() == 'huawei':
            score += 25
            
        # 3. 串口得分 (20分)
        if not device.get('serial_port'):
            score += 20
            
        # 4. 系统版本得分 (15分)
        try:
            sys_version = float(device.get('sys_version', 0)) or 0
            # 假设系统版本在4.0到14.0之间，将其映射到0-15分
            version_score = min(15, max(0, (sys_version - 4.0) * 1.5))
            score += version_score
        except (ValueError, TypeError):
            pass
            
        return score

    async def _select_auxil_devices(
        self,
        device_list: List[Dict],
        perf_device_id: str,
        required_count: int,
        serial_config: Dict
    ) -> Optional[List[Dict]]:
        """选择辅助设备

        Args:
            device_list: 可用设备列表
            perf_device_id: 性能测试设备ID
            required_count: 需要的辅助设备数量
            serial_config: 串口配置信息

        Returns:
            Optional[List[Dict]]: 选择的辅助设备列表，失败返回None
        """
        # 获取性能测试设备的类型信息
        perf_device_type = None
        for device in device_list:
            if device.get('id') == perf_device_id:
                perf_device_type = device.get('sys_type')
                break

        logger.info(f"[子任务 {self.sub_task_id}] 性能测试设备类型: {PlatformType.get_name(perf_device_type) if perf_device_type else 'Unknown'}")

        # 先过滤出所有符合基本条件的设备
        available_devices = []
        for device in device_list:
            if (device.get('id') != perf_device_id
                and device.get('sys_type') in [PlatformType.ANDROID.value, PlatformType.IOS.value]
                and not device.get('is_occupied')
                and device.get('state') == DeviceState.ONLINE.value):

                # 根据设备类型检查连接状态
                device_connected = False
                if device.get('sys_type') == PlatformType.ANDROID.value:
                    device_connected = await android_device._check_device_connection(device.get('udid'))
                elif device.get('sys_type') == PlatformType.IOS.value:
                    device_connected = await ios_device._check_device_connection(device.get('udid'))

                if device_connected:
                    # 获取设备电量
                    battery_level = await self._get_device_battery_level(
                        device.get('udid'),
                        device.get('sys_type')
                    )
                    device['battery_level'] = battery_level
                    # 计算优先级得分
                    device['priority_score'] = self._calculate_device_priority_score(device)
                    available_devices.append(device)

        # 实现基于设备类型的优先级调度策略
        if perf_device_type and perf_device_type in [PlatformType.ANDROID.value, PlatformType.IOS.value]:
            # 分离同类型和异类型设备
            same_type_devices = [d for d in available_devices if d.get('sys_type') == perf_device_type]
            different_type_devices = [d for d in available_devices if d.get('sys_type') != perf_device_type]

            # 分别对同类型和异类型设备按优先级得分排序
            same_type_devices.sort(
                key=lambda x: (
                    -x.get('priority_score', 0),  # 优先级得分，负号表示降序
                    x.get('id', 0)  # 如果得分相同，按ID排序保证稳定性
                )
            )
            different_type_devices.sort(
                key=lambda x: (
                    -x.get('priority_score', 0),  # 优先级得分，负号表示降序
                    x.get('id', 0)  # 如果得分相同，按ID排序保证稳定性
                )
            )

            # 优先选择同类型设备，不足时补充异类型设备
            selected_devices = same_type_devices[:required_count]
            if len(selected_devices) < required_count:
                remaining_count = required_count - len(selected_devices)
                selected_devices.extend(different_type_devices[:remaining_count])

            logger.info(f"[子任务 {self.sub_task_id}] 基于设备类型优先级调度: 同类型设备 {len(same_type_devices)} 个，异类型设备 {len(different_type_devices)} 个")
        else:
            # 如果性能测试设备类型未知或不支持，使用原有的调度逻辑
            available_devices.sort(
                key=lambda x: (
                    -x.get('priority_score', 0),  # 优先级得分，负号表示降序
                    x.get('id', 0)  # 如果得分相同，按ID排序保证稳定性
                )
            )
            selected_devices = available_devices[:required_count]

        if len(selected_devices) < required_count:
            logger.error(f"[子任务 {self.sub_task_id}] 找不到足够的可用已连接的辅助设备")
            return None

        # 更新辅助设备的串口信息
        for device in selected_devices:
            device_udid = device.get('udid')
            if device_udid in serial_config:
                device['serial_port'] = serial_config[device_udid]

        logger.info(f"[子任务 {self.sub_task_id}] 已选择 {len(selected_devices)} 个辅助设备:")
        for device in selected_devices:
            platform = "Android" if device.get('sys_type') == PlatformType.ANDROID.value else "iOS"
            battery = device.get('battery_level', 'unknown')
            score = device.get('priority_score', 0)
            is_same_type = device.get('sys_type') == perf_device_type
            type_priority = "同类型优先" if is_same_type else "异类型补充"
            logger.info(f"[子任务 {self.sub_task_id}] {platform} 设备: {device.get('name')} "
                       f"(ID: {device.get('id')}, 电量: {battery}%, 优先级得分: {score:.1f}, {type_priority})")

        return selected_devices

    async def get_device_group(self) -> Optional[Dict]:
        """获取设备组信息"""
        logger.debug(f"[子任务 {self.sub_task_id}] 初始化设备组")
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始构建设备组信息")
            
            # 加载串口配置
            serial_config = {}
            serial_config_file = os.path.join(
                settings_manager.get('storage.paths.base.data', 'data'),
                settings_manager.get('storage.paths.files.serial', 'serial.json')
            )
            
            if os.path.exists(serial_config_file):
                with open(serial_config_file, 'r') as f:
                    config = json.load(f)
                    # 适配新的配置格式
                    if isinstance(config, dict) and 'serial_mappings' in config:
                        serial_config = {
                            item['udid']: item['port']
                            for item in config['serial_mappings']
                        }

            # 获取性能测试设备ID
            perf_device_id = self.sub_task_context.get('perf_device_id')
            if not perf_device_id:
                logger.error(f"[子任务 {self.sub_task_id}] 未找到性能测试设备ID")
                return None

            device_list = await perf_api.get_device_list(
                is_occupied=0,
                client_id=self.client_id
            )
            if not device_list:
                logger.error(f"[子任务 {self.sub_task_id}] 获取设备列表失败")
                return None

            # 查找性能测试设备
            perf_device = next(
                (device for device in device_list 
                 if device.get('id') == perf_device_id
                 and device.get('state') == DeviceState.ONLINE.value),
                None
            )

            if not perf_device:
                logger.error(f"[子任务 {self.sub_task_id}] 未找到性能测试设备: {perf_device_id}")
                return None

            # 更新性能测试设备的串口信息
            if perf_device:
                device_udid = perf_device.get('udid')
                if device_udid in serial_config:
                    perf_device['serial_port'] = serial_config[device_udid]

            # 获取用例组中需要的最大设备数量
            max_device_count = max((case.get('device_count', 1) for case in self.case_group), default=1)
            auxil_device_count = max_device_count - 1  # 减去性能测试设备

            # 如果不需要辅助设备，直接返回
            if auxil_device_count <= 0:
                logger.info(f"[子任务 {self.sub_task_id}] 不需要辅助设备")
                return {
                    'perf_device': perf_device,
                    'auxil_devices': []
                }

            # 获取辅助设备
            auxil_devices = await self._select_auxil_devices(
                device_list, 
                perf_device_id, 
                auxil_device_count,
                serial_config
            )
            
            if not auxil_devices:
                return None

            return {
                'perf_device': perf_device,
                'auxil_devices': auxil_devices
            }

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 构建设备组信息失败: {str(e)}")
            return None

    async def get_account_group(self, device_group: Dict) -> Optional[Dict]:
        """获取账号组信息"""
        logger.debug(f"[子任务 {self.sub_task_id}] 准备账号组")
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始构建账号组信息")

            # 获取性能测试账号
            perf_account_id = self.sub_task_context.get('perf_account_id')
            if perf_account_id:
                perf_account = await perf_api.get_account_detail(perf_account_id)
            else:
                perf_accounts = await perf_api.get_account_list(
                    business_id=self.business_id,
                    is_occupied=0,
                    account_type=1
                )
                perf_account = perf_accounts[0]
            if not perf_account:
                logger.error(f"[子任务 {self.sub_task_id}] 没有可用的性能测试账号")
                return None

            account_group = {
                'perf_account': perf_account
            }

            # 获取辅助账号
            auxil_device_count = len(device_group.get('auxil_devices', [])) if device_group else 0
            if auxil_device_count > 0:
                auxil_accounts = await perf_api.get_account_list(
                    business_id=self.business_id,
                    is_occupied=0,
                    account_type=2
                )

                if len(auxil_accounts) < auxil_device_count:
                    logger.error(f"[子任务 {self.sub_task_id}] 可用辅助账号数量不足: 需要{auxil_device_count}个，实际{len(auxil_accounts)}个")
                    return None
                account_group['auxil_accounts'] = auxil_accounts[:auxil_device_count]

            logger.info(f"[子任务 {self.sub_task_id}] 构建账号组信息成功")
            return account_group

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 构建账号组信息失败: {str(e)}")
            return None

    async def get_app_group(self) -> Optional[Dict]:
        logger.debug(f"[子任务 {self.sub_task_id}] 获取应用组信息")
        try:
            # 步骤4: 获取应用组详情
            app_group_id = self.sub_task_context.get('app_group_id')
            app_group = await perf_api.get_app_group_detail(app_group_id)
            if not app_group:
                logger.error(f"[子任务 {self.sub_task_id}] 获取应用组 {app_group_id} 详情失败")
                return None
            return app_group

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 获取应用组失败: {str(e)}")
            return None

    async def get_case_group(self) -> Optional[List[Dict]]:
        logger.debug(f"[子任务 {self.sub_task_id}] 获取用例组信息")
        try:
            # 初始化用例组列表
            self.case_group = []
            
            # 遍历用例ID列表获取用例详情
            for case_id in self.sub_task_context.get('case_id_list', []):
                case_detail = await perf_api.get_case_detail(case_id)
                if not case_detail:
                    logger.error(f"[子任务 {self.sub_task_id}] 获取用例 {case_id} 详情失败")
                    return None
                self.case_group.append(case_detail)
                
            return self.case_group

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 获取用例组失败: {str(e)}")
            return None

    async def _init_case_repo(self, repo_url: str, repo_path: str) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 拉取用例仓库: {repo_url}, 路径: {repo_path}")
        try:
            # 判断分支
            env = settings_manager.get('api.current_env', 'local')
            branch = 'boe' if env in ('local', 'boe') else 'master'
            # 如果仓库目录存在则删除
            if Path(repo_path).exists():
                logger.info(f"[子任务 {self.sub_task_id}] 用例仓库已存在，删除后重新拉取...")
                shutil.rmtree(repo_path)
            # 直接克隆
            if not await self._clone_repo(repo_url, repo_path, branch):
                logger.error(f"[子任务 {self.sub_task_id}] 克隆用例仓库失败")
                return False
            # 克隆后自动安装依赖
            requirements_path = os.path.join(repo_path, 'requirements.txt')
            if os.path.exists(requirements_path):
                logger.info(f"[子任务 {self.sub_task_id}] 开始安装用例仓库依赖 requirements.txt ...")
                # 获取项目根目录（假设本文件在 utils/perf/ 下，向上两级）
                project_root = Path(__file__).resolve().parent.parent.parent
                pip_path = project_root / ".venv/bin/pip3.9"
                if not pip_path.exists():
                    logger.error(f"[子任务 {self.sub_task_id}] 未找到虚拟环境 pip: {pip_path}")
                    return False
                pip_cmd = [
                    str(pip_path), 'install', '-r', requirements_path,
                    '-i', 'https://bytedpypi.byted.org/simple/',
                    '-i', 'https://shoots-pypi.bytedance.net/simple/',
                    '-U', '--progress-bar', 'on', '--no-cache-dir'
                ]
                process = await asyncio.create_subprocess_exec(
                    *pip_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.STDOUT
                )
                while True:
                    line = await process.stdout.readline()
                    if not line:
                        break
                    line_str = line.decode(errors='ignore')
                    if 'Requirement already satisfied' not in line_str:
                        logger.info(f"[pip] {line_str.strip()}")
                await process.wait()
                logger.info(f"[子任务 {self.sub_task_id}] requirements.txt 依赖安装完成")
            else:
                logger.warning(f"[子任务 {self.sub_task_id}] 用例仓库 requirements.txt 不存在，跳过依赖安装")
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 初始化用例仓库失败: {str(e)}")
            return False
        
    async def _clone_repo(self, repo_url: str, repo_path: str, branch: str = 'master') -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 克隆用例仓库, 分支: {branch}")
        try:
            logger.info(f"[子任务 {self.sub_task_id}]开始克隆用例仓库，分支: {branch}")

            # 创建父目录
            os.makedirs(os.path.dirname(repo_path), exist_ok=True)

            # 构建克隆命令
            cmd = [
                "git", "clone",
                "--depth", "1",  # 浅克隆，只获取最新版本
                "--single-branch",  # 只克隆单个分支
                "--branch", branch,  # 指定分支
                repo_url,
                repo_path
            ]

            # 使用异步子进程执行克隆命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"[子任务 {self.sub_task_id}] 用例仓库克隆成功")
                return True
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 克隆失败: {stderr.decode()}")
                return False

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 克隆用例仓库失败: {str(e)}")
            return False

    async def _update_repo(self, repo_path: str, branch: str = 'master') -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 更新用例仓库: {repo_path}, 分支: {branch}")
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始更新用例仓库...，分支: {branch}")

            # 保存当前目录
            current_dir = os.getcwd()
            os.chdir(repo_path)

            try:
                # 重置所有本地修改
                logger.info(f"[子任务 {self.sub_task_id}] 开始重置所有本地修改")
                reset_process = await asyncio.create_subprocess_exec(
                    "git", "reset", "--hard", "HEAD",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await reset_process.communicate()

                # 清理未跟踪的文件
                logger.info(f"[子任务 {self.sub_task_id}] 开始清理未跟踪的文件")
                clean_process = await asyncio.create_subprocess_exec(
                    "git", "clean", "-fd",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await clean_process.communicate()

                # 拉取最新代码
                logger.info(f"[子任务 {self.sub_task_id}] 开始拉取最新代码")
                pull_process = await asyncio.create_subprocess_exec(
                    "git", "pull", "origin", branch,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await pull_process.communicate()

                if pull_process.returncode == 0:
                    logger.info(f"[子任务 {self.sub_task_id}] 用例仓库更新成功")
                    return True
                else:
                    logger.error(f"[子任务 {self.sub_task_id}] 更新仓库失败: {stderr.decode()}")
                    return False

            finally:
                # 恢复原始目录
                os.chdir(current_dir)

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 更新用例仓库失败: {str(e)}")
            return False
        
    async def download_sub_task_videos(self, folders: Dict[str, str], video_url: str) -> str:
        logger.debug(f"[子任务 {self.sub_task_id}] 下载子任务视频，URL: {video_url}")
        try:
            # 如果没有视频URL，直接返回None，这不是错误情况
            if not video_url:
                logger.info(f"[子任务 {self.sub_task_id}] 未配置视频URL，跳过视频下载")
                return None

            resources_folder = folders.get('resources')
            logger.info(f"[子任务 {self.sub_task_id}] 开始下载视频文件")
            video_path = os.path.join(resources_folder, 'test.mp4')

            # 使用重试机制下载视频，最多尝试3次
            for retry in range(3):
                try:
                    saved_path = await network_utils.download_file(
                        url=video_url,
                        save_path=video_path
                    )

                    if saved_path:
                        logger.info(f"[子任务 {self.sub_task_id}] 视频文件下载成功")
                        return saved_path

                    if retry < 2:
                        logger.warning(f"[子任务 {self.sub_task_id}] 视频下载失败，准备重试")
                        await asyncio.sleep(1)
                        continue

                except Exception as e:
                    logger.error(f"[子任务 {self.sub_task_id}] 视频下载异常: {str(e)}")

            logger.error(f"[子任务 {self.sub_task_id}] 视频文件下载失败，重试次数已用完")
            return None

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 下载视频文件失败: {str(e)}")
            return None
        
    async def play_test_video(self, video_path: str):
        logger.debug(f"[子任务 {self.sub_task_id}] 播放测试视频，路径: {video_path}")
        try:
            # 检查视频文件
            if not os.path.exists(video_path):
                logger.error(f"[子任务 {self.sub_task_id}] 视频文件不存在: {video_path}")
                return False
            
            if not os.access(video_path, os.R_OK):
                logger.error(f"[子任务 {self.sub_task_id}] 视频文件无法读取: {video_path}")
                return False

            # 基础命令
            cmd = [
                "ffplay",
                "-hide_banner",     # 隐藏横幅
                "-loop", "0",       # 循环播放
                "-loglevel", "error",  # 只显示错误日志
                "-volume", "0",     # 静音
                "-window_title", f"Performance Test Video - {self.sub_task_id}",    # 窗口标题
                "-fs" # 全屏播放
            ]

            # 添加视频文件路径
            cmd.append(video_path)

            # 创建异步播放任务
            async def play_video():
                try:
                    # 启动ffplay
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    logger.info(f"[子任务 {self.sub_task_id}] 视频播放进程已启动，PID: {process.pid}")
                    
                    # 等待进程结束并获取输出
                    stdout, stderr = await process.communicate()
                    
                    if process.returncode != 0:
                        logger.error(f"[子任务 {self.sub_task_id}] 视频播放异常退出，返回码: {process.returncode}")
                        if stderr:
                            logger.error(f"错误输出: {stderr.decode().strip()}")
                        return False
                    
                    return True
                        
                except asyncio.CancelledError:
                    logger.info(f"[子任务 {self.sub_task_id}] 视频播放任务被取消")
                    if process and process.returncode is None:
                        process.terminate()
                    raise
                except Exception as e:
                    logger.error(f"[子任务 {self.sub_task_id}] 视频播放异常: {str(e)}")
                    return False

            # 创建并存储任务
            self._current_video_process = asyncio.create_task(
                play_video(),
                name=f"video-player-{os.path.basename(video_path)}"
            )
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 启动视频播放失败: {str(e)}")
            return False

    async def stop_play_test_video(self) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 停止测试视频播放")
        try:
            # 1. 终止当前视频播放任务
            if self._current_video_process and not self._current_video_process.done():
                self._current_video_process.cancel()
                try:
                    await asyncio.wait_for(self._current_video_process, timeout=5)
                except (asyncio.TimeoutError, asyncio.CancelledError):
                    pass
                finally:
                    self._current_video_process = None

            # 2. 清理所有ffplay进程
            logger.info(f"[子任务 {self.sub_task_id}] 开始清理ffplay进程")
            
            # 2.1 使用pkill直接终止所有ffplay进程
            kill_cmd = "pkill -f ffplay"
            process = await asyncio.create_subprocess_shell(
                kill_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # 2.2 等待短暂时间确保进程被终止
            await asyncio.sleep(1)
            
            # 2.3 检查是否还有残留进程
            check_cmd = "pgrep ffplay"
            process = await asyncio.create_subprocess_shell(
                check_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            
            # 如果还有残留进程，使用强制终止
            if stdout and stdout.decode().strip():
                logger.warning(f"[子任务 {self.sub_task_id}] 发现残留进程，使用强制终止")
                force_kill_cmd = "pkill -9 -f ffplay"
                process = await asyncio.create_subprocess_shell(
                    force_kill_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                
                # 最后验证
                process = await asyncio.create_subprocess_shell(
                    check_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, _ = await process.communicate()
                if stdout and stdout.decode().strip():
                    logger.error(f"[子任务 {self.sub_task_id}] 无法完全终止ffplay进程")
                    return False
            
            logger.info(f"[子任务 {self.sub_task_id}] 视频播放进程清理完成")
            return True
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 终止视频播放失败: {str(e)}")
            return False

    async def create_sub_task_directories(self, sub_task_folder: str) -> Dict[str, str]:
        logger.debug(f"[子任务 {self.sub_task_id}] 创建子任务相关目录: {sub_task_folder}")
        try:
            if not sub_task_folder:
                raise ValueError("子任务目录路径为空")

            # 创建主目录
            os.makedirs(sub_task_folder, exist_ok=True)

            # 使用已初始化的路径配置
            folders = {}

            # 创建资源目录
            resources_path = os.path.abspath(os.path.join(sub_task_folder, self.task_resources_dir))
            os.makedirs(resources_path, exist_ok=True)
            folders['resources'] = resources_path

            # 创建结果目录
            results_path = os.path.abspath(os.path.join(sub_task_folder, self.task_results_dir))
            os.makedirs(results_path, exist_ok=True)
            folders['results'] = results_path



            logger.info(f"[子任务 {self.sub_task_id}] 子任务目录创建完成")
            return folders

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 创建子任务目录失败: {str(e)}")
            return None
            
    async def copy_sub_task_configuration(self, sub_task_folder: str) -> bool:
        """复制子任务配置到子任务目录"""
        logger.debug(f"[子任务 {self.sub_task_id}] 复制子任务配置到: {sub_task_folder}")
        try:
            # 确保数据目录存在
            os.makedirs(self.base_data_dir, exist_ok=True)

            # 保存子任务配置
            write_json(self.task_file_path, self.sub_task_detail)
            logger.info(f"[子任务 {self.sub_task_id}] 子任务配置已保存")

            # 复制到用例仓库目录
            if not self.perf_case_repo_path.exists():
                os.makedirs(self.perf_case_repo_path, exist_ok=True)
            dst_file = self.perf_case_repo_path / self.task_file_name
            shutil.copy2(self.task_file_path, dst_file)
            logger.info(f"[子任务 {self.sub_task_id}] 子任务配置已复制到用例仓库")

            # 复制到子任务目录
            dst_file = os.path.join(sub_task_folder, self.task_file_name)
            shutil.copy2(self.task_file_path, dst_file)
            logger.info(f"[子任务 {self.sub_task_id}] 子任务配置已复制到子任务目录")

            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 复制子任务配置失败: {str(e)}")
            return False

    def create_perf_data_directory(self, tool_type: int) -> str:
        """在用例仓库下创建性能数据目录

        Args:
            tool_type: 性能工具类型

        Returns:
            str: 创建的数据目录路径
        """
        try:
            # 确保用例仓库目录存在
            if not self.perf_case_repo_path.exists():
                os.makedirs(self.perf_case_repo_path, exist_ok=True)
                logger.info(f"[子任务 {self.sub_task_id}] 创建用例仓库目录: {self.perf_case_repo_path}")

            # 创建data目录
            data_dir = self.perf_case_repo_path / "data"
            os.makedirs(data_dir, exist_ok=True)

            return str(data_dir)

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 创建性能数据目录失败: {str(e)}")
            return str(self.perf_case_repo_path)

    async def _cleanup_old_task_folders(self) -> bool:
        """清理历史任务文件夹"""
        logger.debug("清理历史任务文件夹")
        try:
            tasks_base_path = Path(self.tasks_base_dir)
            if not tasks_base_path.exists():
                logger.info("任务目录不存在，无需清理")
                return True
                
            current_time = datetime.now()
            fifteen_days_ago = current_time - timedelta(days=15)
            
            for task_folder in tasks_base_path.iterdir():
                if not task_folder.is_dir():
                    continue
                    
                try:
                    folder_stat = task_folder.stat()
                    folder_time = datetime.fromtimestamp(folder_stat.st_mtime)
                    
                    if folder_time < fifteen_days_ago:
                        shutil.rmtree(task_folder)
                        logger.info(f"已删除15天前的任务文件夹: {task_folder}")
                except Exception as e:
                    logger.error(f"删除任务文件夹 {task_folder} 时出错: {str(e)}")
                    continue

            return True
            
        except Exception as e:
            logger.error(f"清理旧任务文件夹失败: {str(e)}")
            return False
        
    async def _cleanup_successed_task_folders(self) -> bool:
        """清理已完成任务文件夹"""
        logger.debug("清理已完成任务文件夹")
        try:
            # 1. 获取任务目录下的所有文件夹
            tasks_base_path = Path(self.tasks_base_dir)
            if not tasks_base_path.exists():
                logger.info("任务目录不存在，无需清理")
                return True
                
            # 2. 获取所有任务文件夹
            task_folders = [f for f in tasks_base_path.iterdir() if f.is_dir()]
            
            # 3. 遍历每个任务文件夹
            for task_folder in task_folders:
                try:
                    # 获取任务ID
                    task_id = task_folder.name
                    # 查询任务状态
                    task_detail = await perf_api.get_task_detail_by_id(task_id)
                    if not task_detail:
                        logger.warning(f"获取任务 {task_id} 详情失败，视为已删除，直接清理")
                        if task_folder.exists():  # 添加检查确保文件夹存在
                            shutil.rmtree(task_folder)
                            logger.info(f"已删除任务文件夹: {task_folder}")
                        continue
                    # 检查任务状态是否为成功
                    task_status = task_detail.get('status')
                    if task_status == TaskStatus.SUCCESS.value:
                        # 删除成功任务文件夹
                        if task_folder.exists():  # 添加检查确保文件夹存在
                            shutil.rmtree(task_folder)
                            logger.info(f"已删除成功任务文件夹: {task_folder}")
                    else:
                        logger.debug(f"任务 {task_id} 状态为 {TaskStatus.get_name(task_status)}，跳过清理")
                except Exception as e:
                    logger.error(f"清理任务文件夹 {task_folder} 时出错: {str(e)}")
                    continue
                    
            return True
            
        except Exception as e:
            logger.error(f"清理成功任务文件失败: {str(e)}")
            return False
        
    async def _cleanup_old_log_files(self) -> bool:
        """清理历史日志文件"""
        logger.debug("清理历史日志文件")
        try:
            if not Path(self.base_logs_dir).exists():
                logger.info("日志目录不存在，无需清理")
                return True
                
            current_time = datetime.now()
            fifteen_days_ago = current_time - timedelta(days=15)
            
            # 获取所有日志文件（包括压缩文件）
            log_files = list(Path(self.base_logs_dir).glob('*.log'))
            zip_files = list(Path(self.base_logs_dir).glob('*.zip'))
            all_files = log_files + zip_files
            deleted_count = 0
            
            for log_file in all_files:
                try:
                    # 从文件名中提取日期
                    # 新文件名格式: YYYY-MM-DD.log 或 YYYY-MM-DD.zip
                    file_name = log_file.name

                    # 移除扩展名获取日期部分
                    if file_name.endswith('.log'):
                        name_without_ext = file_name[:-4]  # 移除 '.log'
                    elif file_name.endswith('.zip'):
                        name_without_ext = file_name[:-4]  # 移除 '.zip'
                    else:
                        continue  # 跳过不支持的文件格式

                    # 检查是否为新格式 (YYYY-MM-DD)
                    if len(name_without_ext) == 10 and name_without_ext.count('-') == 2:
                        # 新格式: YYYY-MM-DD
                        date_part = name_without_ext
                    else:
                        # 兼容旧格式: YYYY-MM-DD.YYYY-MM-DD_HH-MM-SS_XXXXXX
                        # 取第一个点之前的部分作为日期
                        date_part = name_without_ext.split('.')[0]

                    # 验证日期格式
                    if len(date_part) != 10 or date_part.count('-') != 2:
                        logger.warning(f"跳过无效日期格式的文件: {log_file}")
                        continue

                    # 解析日期
                    file_date = datetime.strptime(date_part, '%Y-%m-%d')

                    # 如果文件日期早于15天前，则删除
                    if file_date < fifteen_days_ago:
                        log_file.unlink()
                        deleted_count += 1
                        logger.info(f"已删除15天前的日志文件: {log_file}")
                except Exception as e:
                    logger.error(f"处理日志文件 {log_file} 时出错: {str(e)}")
                    continue
            
            logger.info(f"日志清理完成，共删除 {deleted_count} 个文件")
            return True
            
        except Exception as e:
            logger.error(f"清理旧日志文件失败: {str(e)}")
            return False
    
    async def _cleanup(self) -> bool:
        """清理任务相关资源"""
        logger.debug(f"[子任务 {self.sub_task_id}] 清理任务相关资源")
        try:
            # 1. 释放资源
            if not await self._release_resources():
                return False
            
            # 2. 删除任务配置文件
            if self.task_file_path.exists():
                self.task_file_path.unlink()
                
            # 3. 删除CPU Profile文件
            template_params_path = self.task_file_path.parent / 'template_params.json'
            if template_params_path.exists():
                template_params_path.unlink()
            
            # 4. 删除成功任务文件
            if not await self._cleanup_successed_task_folders():
                return False
            
            # 5. 删除15天前的任务文件夹
            if not await self._cleanup_old_task_folders():
                return False
            
            # 6. 删除15天前的日志文件
            if not await self._cleanup_old_log_files():
                return False

            return True
            
        except Exception as e:
            logger.error(f"清理任务失败: {str(e)}")
            return False

    # 资源控制相关方法
    async def _occupy_single_resource(self, resource_id: int, resource_type: str) -> bool:
        """占用单个资源"""
        logger.debug(f"[子任务 {self.sub_task_id}] 占用资源: {resource_type}, ID: {resource_id}")
        try:
            if resource_type == 'device':
                await perf_api.update_device_occupied(resource_id, 1)
                self.occupied_resources['devices'].append(resource_id)
            elif resource_type == 'account':
                await perf_api.update_account_occupied(resource_id, 1)
                self.occupied_resources['accounts'].append(resource_id)
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] {resource_type} {resource_id} 占用失败: {str(e)}")
            return False

    async def _occupy_resources(self) -> bool:
        """占用资源"""
        logger.debug(f"[子任务 {self.sub_task_id}] 执行资源占用流程")
        try:
            # 1. 占用性能测试设备
            perf_device = self.device_group.get('perf_device', {})
            perf_device_id = perf_device.get('id')
            if perf_device_id:
                if not await self._occupy_single_resource(perf_device_id, 'device'):
                    return False
                logger.info(f"[子任务 {self.sub_task_id}] 已占用性能测试设备: {perf_device.get('name', '')} (ID: {perf_device_id})")

            # 2. 占用辅助设备
            for device in self.device_group.get('auxil_devices', []):
                device_id = device.get('id')
                if device_id:
                    if not await self._occupy_single_resource(device_id, 'device'):
                        return False
                    logger.info(f"[子任务 {self.sub_task_id}] 已占用辅助设备: {device.get('name', '')} (ID: {device_id})")

            # 3. 占用性能测试账号
            perf_account = self.account_group.get('perf_account', {})
            perf_account_id = perf_account.get('id')
            if perf_account_id:
                if not await self._occupy_single_resource(perf_account_id, 'account'):
                    return False
                logger.info(f"[子任务 {self.sub_task_id}] 已占用性能测试账号: {perf_account.get('username', '')} (ID: {perf_account_id})")

            # 4. 占用辅助账号
            for account in self.account_group.get('auxil_accounts', []):
                account_id = account.get('id')
                if account_id:
                    if not await self._occupy_single_resource(account_id, 'account'):
                        return False
                    logger.info(f"[子任务 {self.sub_task_id}] 已占用辅助账号: {account.get('username', '')} (ID: {account_id})")

            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 占用资源失败: {str(e)}")
            return False
        
    async def _release_resources(self) -> bool:
        """释放资源"""
        logger.debug(f"[子任务 {self.sub_task_id}] 执行资源释放流程")
        try:
            # 检查是否已经释放过资源
            if not self.occupied_resources.get('devices') and not self.occupied_resources.get('accounts'):
                logger.debug(f"[子任务 {self.sub_task_id}] 资源已释放，跳过")
                return True
                
            # 1. 同步释放所有设备资源
            release_device_tasks = []
            for device_id in self.occupied_resources.get('devices', []):
                task = asyncio.create_task(perf_api.update_device_occupied(device_id, 0))
                release_device_tasks.append(task)
            
            # 2. 同步释放所有账号资源    
            release_account_tasks = []
            for account_id in self.occupied_resources.get('accounts', []):
                task = asyncio.create_task(perf_api.update_account_occupied(account_id, 0))
                release_account_tasks.append(task)
            
            # 3. 等待所有释放操作完成
            if release_device_tasks:
                await asyncio.gather(*release_device_tasks)
                logger.info(f"[子任务 {self.sub_task_id}] 已释放所有设备资源")
            
            if release_account_tasks:    
                await asyncio.gather(*release_account_tasks)
                logger.info(f"[子任务 {self.sub_task_id}] 已释放所有账号资源")
            
            # 4. 清空本地记录
            self.occupied_resources = {'devices': [], 'accounts': []}
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 释放资源失败: {str(e)}")
            return False

    async def _check_battery_level(self) -> bool:
        """检查设备电量"""
        logger.debug(f"[子任务 {self.sub_task_id}] 检查设备电量")
        try:
            # 使用成员变量
            perf_device = self.device_group.get('perf_device')
            min_power_level = self.sub_task_detail.get('perf_device_power_level', 50)

            # 1. 检查性能测试设备电量
            if perf_device:
                device_udid = perf_device.get('udid')
                if not device_udid:
                    raise ValueError("性能测试设备UDID为空")

                battery_level = await self._get_device_battery_level(device_udid, perf_device.get('sys_type'))
                if battery_level < min_power_level:
                    logger.error(f"[子任务 {self.sub_task_id}] 性能测试设备 {device_udid} 电量不足: {battery_level}%")
                    return False
                
                logger.info(f"[子任务 {self.sub_task_id}] 性能测试设备 {device_udid} 电量正常: {battery_level}%")

            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 检查设备电量失败: {str(e)}")
            return False

    async def _get_device_battery_level(self, device_udid: str, sys_type: int) -> int:
        """获取设备电量"""
        logger.debug(f"[子任务 {self.sub_task_id}] 获取设备电量，udid: {device_udid}, 类型: {sys_type}")
        try:
            if sys_type == PlatformType.ANDROID.value:
                return await android_device.get_battery_level(device_udid)
            elif sys_type == PlatformType.IOS.value:
                return await ios_device.get_battery_level(device_udid)
            else:
                raise ValueError(f"不支持的设备类型: {sys_type}")

        except Exception as e:
            logger.error(f"获取设备 {device_udid} 电量失败: {str(e)}")
            return 0

    async def _devices_init(self) -> bool:
        """初始化所有设备"""
        logger.debug(f"[子任务 {self.sub_task_id}] 初始化所有设备")
        try:
            # 获取设备信息和采集模式
            perf_device = self.device_group.get('perf_device')
            auxil_devices = self.device_group.get('auxil_devices', [])
            is_wireless_mode = self.sub_task_detail.get('perf_collect_mode', PerfCollectMode.WIRELESS.value) == PerfCollectMode.WIRELESS.value
            
            # 初始化所有设备
            devices_to_init = ([perf_device] if perf_device else []) + auxil_devices
            for device in devices_to_init:
                if not await self._init_single_device(device, is_wireless_mode):
                    return False
                    
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 设备初始化失败: {str(e)}")
            return False
            
    async def _init_single_device(self, device: dict, is_wireless_mode: bool) -> bool:
        """初始化单台设备"""
        logger.debug(f"[子任务 {self.sub_task_id}] 初始化单台设备，udid: {device.get('udid')}，无线: {is_wireless_mode}")
        try:
            device_udid = device.get('udid')
            if not device_udid:
                logger.warning(f"[子任务 {self.sub_task_id}] 跳过UDID为空的设备")
                return True

            # 1. 串口控制
            if not await self._handle_serial_port(device, is_wireless_mode):
                return False

            # 2. Android设备特殊处理
            if device.get('sys_type') == PlatformType.ANDROID.value:
                await self._init_android_device(device_udid, is_wireless_mode)
            elif device.get('sys_type') == PlatformType.IOS.value:
                await self._init_ios_device(device_udid, is_wireless_mode)

            return True
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 设备 {device_udid} 初始化失败: {str(e)}")
            return False
            
    async def _handle_serial_port(self, device: dict, is_wireless_mode: bool) -> bool:
        """处理串口"""
        logger.debug(f"[子任务 {self.sub_task_id}] 处理串口，udid: {device.get('udid')}，无线: {is_wireless_mode}")
        device_udid = device.get('udid')
        serial_port = device.get('serial_port')
        
        if is_wireless_mode and serial_port:
            logger.info(f"[子任务 {self.sub_task_id}] 正在打开设备 {device_udid} 串口...")
            success = await asyncio.to_thread(serial_control.relay_switch, serial_port, True)
            if not success:
                logger.error(f"[子任务 {self.sub_task_id}] 打开设备串口失败")
                return False
        else:
            logger.info(f"[子任务 {self.sub_task_id}] 设备 {device_udid} 跳过串口操作")
            
        return True
        
    async def _init_android_device(self, device_udid: str, is_wireless_mode: bool) -> None:
        """初始化Android设备"""
        logger.debug(f"[子任务 {self.sub_task_id}] 初始化Android设备，udid: {device_udid}，无线: {is_wireless_mode}")
        # 1. 卸载第三方应用
        whitelist_packages = set(["com.volcengine.corplink", "com.ss.android.lark", "com.zhiliaoapp.musically", "com.carriez.flutter_hbb"])
        await android_device.uninstall_third_party_apps(device_udid, whitelist_packages)

        # 2. 根据模式设置屏幕亮度
        brightness = 1 if is_wireless_mode else 60
        await android_device.set_screen_brightness(device_udid, brightness)
        
        # 3. 关闭自动旋转屏幕
        await android_device.close_auto_rotate_screen(device_udid)
    
    async def _init_ios_device(self, device_udid: str, is_wireless_mode: bool) -> None:
        logger.debug(f"[子任务 {self.sub_task_id}] 初始化iOS设备，udid: {device_udid}，无线: {is_wireless_mode}")
        # 1. 卸载第三方应用
        whitelist_packages = set(["com.byteplus.feilian", "com.bytedance.ee.lark", "com.obtenir.runner.TinyHopper", "com.volcengine.corplink", "linmingjun.NewSafari.app", "com.zhiliaoapp.musically", "com.zhiliaoapp.musically.ep"])
        await ios_device.uninstall_third_party_apps(device_udid, whitelist_packages)
        
        # 2. 其他iOS设备特定的初始化操作可以在这里添加
        pass

    async def get_config_group(self, config_id: int) -> Optional[Dict]:
        """获取配置组信息"""
        logger.debug(f"[子任务 {self.sub_task_id}] 获取配置组信息，config_id: {config_id}")
        try:
            if not config_id:
                logger.warning(f"[子任务 {self.sub_task_id}] 配置ID为空，跳过配置组获取")
                return None

            # 调用API获取配置详情
            config_detail = await perf_api.get_config_detail(config_id)
            if not config_detail:
                logger.error(f"[子任务 {self.sub_task_id}] 获取配置 {config_id} 详情失败")
                return None

            logger.info(f"[子任务 {self.sub_task_id}] 成功获取配置组信息: 配置ID={config_id}")
            return config_detail

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 获取配置组信息失败: {str(e)}")
            return None

    async def get_experiment_configs(self, experiment_id: int) -> dict:
        """获取实验配置"""
        logger.debug(f"[子任务 {self.sub_task_id}] 获取实验配置，experiment_id: {experiment_id}")
        if not experiment_id:
            logger.error(f"[子任务 {self.sub_task_id}] 未找到实验ID")
            return {}
        detail = await perf_api.get_experiment_detail(experiment_id)
        if not detail:
            logger.error(f"[子任务 {self.sub_task_id}] 获取实验详情失败")
            return {}

        # 确保返回的数据包含 version_type 信息
        result = {
            'hit_type': detail.get('hit_type'),
            'experiment_group_version_ids': detail.get('experiment_group_version_ids', []),
            'control_group_version_ids': detail.get('control_group_version_ids', []),
            'experiment_group_version_type': LibraGroupType.EXPERIMENT.value,
            'control_group_version_type': LibraGroupType.CONTROL.value
        }
        logger.info(f"[子任务 {self.sub_task_id}] 获取实验配置成功: {result}")
        return result

task_helper = TaskHelper()

if __name__ == "__main__":
    asyncio.run(task_helper._cleanup_old_log_files())