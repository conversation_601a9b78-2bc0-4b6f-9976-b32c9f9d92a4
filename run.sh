#!/bin/bash

# Global Business Client - 引导脚本
#
# 此脚本仅用于引导用户使用正确的安装和启动方式
# 请使用以下命令：
# 1. 安装：sh install.sh
# 2. 启动：gb

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "=========================================="
echo "Global Business Client"
echo "=========================================="
echo ""

# 检查是否已安装 gb 命令
if command -v gb &> /dev/null; then
    echo -e "${GREEN}✅ 检测到已安装的 gb 命令！${NC}"
    echo ""
    echo "🎉 请使用以下命令："
    echo -e "  启动应用: ${GREEN}gb${NC}"
    echo -e "  重启应用: ${GREEN}gb --restart${NC}"
    echo -e "  调试模式: ${GREEN}gb --debug${NC}"
    echo -e "  查看帮助: ${GREEN}gb --help${NC}"
    echo ""
    echo -e "${BLUE}💡 提示：直接使用 gb 命令即可，无需运行此脚本${NC}"
    echo ""

    read -p "是否现在启动应用？(Y/n): " start_now
    case $start_now in
        [Nn]|[Nn][Oo])
            echo -e "${YELLOW}请手动运行: gb${NC}"
            ;;
        *)
            echo -e "${BLUE}正在启动应用...${NC}"
            exec gb "$@"
            ;;
    esac
else
    echo -e "${YELLOW}⚠️  未检测到 gb 命令${NC}"
    echo ""
    echo "🚀 请按以下步骤操作："
    echo -e "  1. 安装: ${YELLOW}sh install.sh${NC}"
    echo -e "  2. 启动: ${YELLOW}gb${NC}"
    echo ""
    echo "📋 install.sh 会自动完成："
    echo "  • 安装所有依赖"
    echo "  • 配置环境变量"
    echo "  • 创建全局 gb 命令"
    echo "  • 设置自动更新"
    echo ""

    if [ -f "install.sh" ]; then
        read -p "是否现在运行 install.sh？(Y/n): " run_install
        case $run_install in
            [Nn]|[Nn][Oo])
                echo -e "${YELLOW}请手动运行: sh install.sh${NC}"
                ;;
            *)
                echo -e "${BLUE}正在运行安装脚本...${NC}"
                exec sh install.sh
                ;;
        esac
    else
        echo -e "${RED}❌ 错误：install.sh 文件不存在${NC}"
        exit 1
    fi
fi