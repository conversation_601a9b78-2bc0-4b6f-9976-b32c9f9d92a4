'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-08 22:48:03
FilePath: /global_rtc_client/utils/common/tos_utils.py
Description: TOS 工具类
'''
import os
import bytedtos
from core.settings_manager import settings_manager
from utils.common.log_utils import get_logger

logger = get_logger(__name__)


class TosClient:
    def __init__(self):
        try:
            # 从settings获取配置
            self.access_key = settings_manager.get('storage.tos.access_key')
            self.bucket = settings_manager.get('storage.tos.bucket')
            self.endpoint = settings_manager.get('storage.tos.endpoint')
            self.timeout = settings_manager.get('storage.tos.timeout', 60)
            self.connect_timeout = settings_manager.get('storage.tos.connect_timeout', 60)
            self.connection_pool_size = settings_manager.get('storage.tos.connection_pool_size', 10)
            
            self.client = bytedtos.Client(
                self.bucket,
                self.access_key,
                # endpoint 是可选参数，设置是否通过子域名初始化
                endpoint=self.endpoint,
                # stream 是可选参数，设置是否流式下载
                stream=False,
                # remote_psm 是可选参数，设置客户端的 PSM
                remote_psm="toutiao.tos.tosapi",
                # timeout 是可选参数，设置请求超时
                timeout=self.timeout,
                # connection_time 是可选参数，设置连接超时
                connect_timeout=self.connect_timeout,
                # 设置connection_pool_size，将连接池大小设置为配置值
                connection_pool_size=self.connection_pool_size
            )
            
        except Exception as e:
            logger.error(f"初始化TOS客户端失败: {str(e)}")
            raise

    def list_prefix(self, prefix_of_obj, target_delimiter="/", target_start_after="", target_max_keys=1000):
        """列举对象，递归到最底层文件夹并获取所有文件路径"""
        all_files = []  # 存储所有文件路径的列表

        try:
            while True:
                # 调用 list_prefix API 列举对象
                resp = self.client.list_prefix(prefix_of_obj, target_delimiter, target_start_after, target_max_keys)
                data = resp.json["payload"]

                # 获取 commonPrefix 和 objects，若不存在则设为空列表
                commonPrefix = data.get("commonPrefix", []) or []
                objects = data.get("objects", []) or []

                # 递归处理子文件夹前缀
                for elem in commonPrefix:
                    # 递归调用 list_prefix 处理子文件夹，并合并结果
                    all_files.extend(self.list_prefix(elem, target_delimiter, "", target_max_keys))

                # 处理当前层级的文件对象
                for item in objects:
                    item_key = item.get("key")
                    all_files.append(item_key)  # 添加文件路径到列表
                    item_size = item.get("size")
                    item_etag = item.get("etag")
                    item_last_modified = item.get("lastModified")
                    item_storage_class = item.get("storageClass")
                    logger.debug(
                        f"File: {item_key}, Size: {item_size}, ETag: {item_etag}, Last Modified: {item_last_modified}, Storage Class: {item_storage_class}")

                # 检查是否有更多未列举完的对象
                is_truncated = data.get("isTruncated", False)
                next_start_after = data.get("startAfter", "")
                if not is_truncated:
                    break  # 退出循环，如果列举完毕
                else:
                    # 如果未列举完，设置 next_start_after 继续列举
                    target_start_after = next_start_after

            logger.debug(
                f"List action successful. Code: {resp.status_code}, Request ID: {resp.headers.get(bytedtos.consts.ReqIdHeader, '')}")

        except bytedtos.TosException as e:
            # 捕获异常并输出错误信息
            logger.error(f"List action failed. Code: {e.code}, Request ID: {e.request_id}, Message: {e.msg}")

        return all_files  # 返回所有文件路径

    def content_upload(self, obj_key, content):
        """
        上传字符流和Bytes流
        @param obj_key:
        @param content: StringIO('Hello TOS')/BytesIO(b'Hello TOS')
        @return:
        """
        try:
            resp = self.client.put_object(obj_key, content)
            logger.debug(
                f"toc put success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            # 操作失败，捕获异常，可从返回信息中获取详细错误信息
            # request id 可定位具体问题，强烈建议日志中保存
            logger.error(f"toc put fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    def multipart_upload(self, obj_key, file_name):
        """
        分片上传
        @param obj_key:
        @param file_name:
        @return:
        """
        total_size = os.path.getsize(file_name)
        part_size = 5 * 1024 * 1024

        try:
            init_resp = self.client.init_upload(obj_key)
            upload_id = init_resp.upload_id
            parts_list = []
            # 上传分片数据
            with open(file_name, 'rb') as f:
                part_number = 1
                offset = 0
                while offset < total_size:
                    if total_size - offset < 2 * part_size:
                        num_to_upload = total_size - offset
                    else:
                        num_to_upload = min(part_size, total_size - offset)
                    f.seek(offset, os.SEEK_SET)
                    cur_data = f.read(num_to_upload)
                    upload_part_resp = self.client.upload_part(obj_key, upload_id, part_number, cur_data)
                    parts_list.append(upload_part_resp.part_number)
                    offset += num_to_upload
                    part_number += 1

            comp_resp = self.client.complete_upload(obj_key, upload_id, parts_list)
            logger.debug("toc complete_upload success. code: {}, request_id: {}".format(comp_resp.status_code,
                                                                                        comp_resp.headers[
                                                                                            bytedtos.consts.ReqIdHeader]))
        except bytedtos.TosException as e:
            logger.debug(
                "toc complete_upload fail. code: {}, request_id: {}, message: {}".format(e.code, e.request_id, e.msg))

    def abort_upload(self, obj_key, upload_id):
        """
        取消分片上传
        @param obj_key:
        @param upload_id:
        @return:
        """
        try:
            resp = self.client.abort_upload(obj_key, upload_id)
            logger.debug("toc abort_upload success. code: {}, request_id: {}".format(resp.status_code,
                                                                                     resp.headers[
                                                                                         bytedtos.consts.ReqIdHeader]))
        except bytedtos.TosException as e:
            logger.debug(
                "toc abort_upload fail. code: {}, request_id: {}, message: {}".format(e.code, e.request_id, e.msg))

    def get_object(self, obj_key):
        """
        普通下载
        @param obj_key:
        @return:
        """
        try:
            resp = self.client.get_object(obj_key)
            # 下载对象到内存中
            logger.debug(
                f"toc get object success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
            return resp
        except bytedtos.TosException as e:
            logger.error(f"toc get object fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    def get_object_range(self, obj_key, start, end):
        """
        范围下载
        @param obj_key:
        @param start:
        @param end:
        @return:
        """
        try:
            resp = self.client.get_object_range(obj_key, start=start, end=end)
            logger.debug(
                f"toc get object range success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            logger.error(
                f"toc get object range fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    def delete_object(self, obj_key):
        """
        删除对象
        @param obj_key:
        @return:
        """
        try:
            resp = self.client.delete_object(obj_key)
            logger.debug(
                f"toc delete object success. code: {resp.status_code}, request_id: {resp.headers[bytedtos.consts.ReqIdHeader]}")
        except bytedtos.TosException as e:
            # 操作失败，捕获异常，可从返回信息中获取详细错误信息
            # request id 可定位具体问题，强烈建议日志中保存
            logger.error(f"toc delete object fail. code: {e.code}, request_id: {e.request_id}, message: {e.msg}")

    # def batch_delete_by_prefix(self, prefix):
    #     """
    #     危险操作，慎用！！！
    #     批量删除指定前缀的所有文件
    #     @param prefix: 要删除的文件夹前缀
    #     @return: (成功删除数量, 失败数量)
    #     """
    #     success_count = 0
    #     failed_count = 0
        
    #     try:
    #         # 获取所有文件列表
    #         all_files = self.list_prefix(prefix)
    #         total_files = len(all_files)
            
    #         if total_files == 0:
    #             logger.info(f"前缀 {prefix} 下没有找到文件")
    #             return success_count, failed_count
                
    #         logger.info(f"开始批量删除前缀为 {prefix} 的文件，共 {total_files} 个文件")
            
    #         # 遍历删除每个文件
    #         for i, file_path in enumerate(all_files, 1):
    #             try:
    #                 self.delete_object(file_path)
    #                 success_count += 1
    #                 if i % 100 == 0:  # 每删除100个文件打印一次进度
    #                     logger.info(f"删除进度: {i}/{total_files}")
    #             except Exception as e:
    #                 failed_count += 1
    #                 logger.error(f"删除文件 {file_path} 失败: {str(e)}")
                    
    #         logger.info(f"批量删除完成。成功: {success_count}, 失败: {failed_count}")
            
    #     except Exception as e:
    #         logger.error(f"批量删除操作失败: {str(e)}")
            
    #     return success_count, failed_count
    
tos_client = TosClient()

def test_tos_client():
    """
    测试TOS上传功能
    此函数测试TOS工具类的主要功能，包括：
    1. 字符流上传
    2. 对象下载
    3. 列出文件
    4. 分片上传
    5. 删除对象
    """
    import io
    import os
    import tempfile
    from datetime import datetime
    
    # 记录测试结果
    test_results = {
        "字符流上传": False,
        "对象下载": False,
        "列出文件": False,
        "分片上传": False,
        "删除对象": False
    }
    
    try:
        print("===== 测试1：字符流上传 =====")
        # 1. 测试字符流上传
        test_content = f"TOS upload test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        test_obj_key = "test/upload_test_string.txt"
        
        print(f"开始测试字符流上传，对象键: {test_obj_key}")
        tos_client.content_upload(test_obj_key, io.BytesIO(test_content.encode('utf-8')))
        test_results["字符流上传"] = True
        
        # 2. 验证上传是否成功
        print("验证上传是否成功...")
        resp = tos_client.get_object(test_obj_key)
        if resp:
            try:
                if hasattr(resp, 'data'):
                    content = resp.data.decode('utf-8')
                    is_content_match = content == test_content
                    test_results["对象下载"] = is_content_match
                    print(f"上传内容验证: {'成功' if is_content_match else '失败'}")
                    print(f"上传内容: {test_content}")
                    print(f"下载内容: {content}")
                else:
                    print("响应对象没有data属性")
                    print(f"可用属性: {dir(resp)}")
            except Exception as e:
                print(f"读取内容时出错: {str(e)}")
                import traceback
                traceback.print_exc()
        else:
            print("获取对象失败，上传可能不成功")
        
        # 3. 测试列出文件
        print("\n测试列出文件...")
        files = tos_client.list_prefix("test/")
        file_count = len(files)
        test_results["列出文件"] = file_count > 0
        print(f"列出的文件数量: {file_count}")
        for file in files:
            print(f"- {file}")
        
        # 4. 测试删除字符流上传文件
        print("\n测试删除字符流上传文件...")
        tos_client.delete_object(test_obj_key)
        print(f"删除文件 {test_obj_key} 完成")
        
        # 验证删除是否成功
        files_after_delete = tos_client.list_prefix("test/")
        is_delete_success = test_obj_key not in files_after_delete
        test_results["删除对象"] = is_delete_success
        if is_delete_success:
            print(f"删除验证: 成功")
        else:
            print(f"删除验证: 失败，文件仍然存在")
        
        print("\n===== 测试2：分片上传 =====")
        # 5. 测试分片上传
        # 创建一个临时文件用于测试分片上传
        temp_file_path = os.path.join(tempfile.gettempdir(), "tos_multipart_test.txt")
        multipart_obj_key = "test/multipart_upload_test.txt"
        
        # 创建一个大于5MB的文件，以触发分片上传
        chunk_size = 1024 * 1024  # 1MB
        chunks = 6  # 创建6MB文件
        
        print(f"创建测试文件: {temp_file_path} (大小: {chunks}MB)")
        with open(temp_file_path, 'wb') as f:
            for i in range(chunks):
                f.write((f"Chunk {i} - " + "X" * (chunk_size - 10)).encode('utf-8'))
        
        print(f"开始分片上传，对象键: {multipart_obj_key}")
        tos_client.multipart_upload(multipart_obj_key, temp_file_path)
        
        # 验证分片上传是否成功
        print("验证分片上传是否成功...")
        files = tos_client.list_prefix("test/")
        is_multipart_success = multipart_obj_key in files
        test_results["分片上传"] = is_multipart_success
        if is_multipart_success:
            print(f"分片上传成功，文件已存在: {multipart_obj_key}")
        else:
            print(f"分片上传失败，文件不存在: {multipart_obj_key}")
        
        # 删除分片上传的文件
        print("\n测试删除分片上传文件...")
        tos_client.delete_object(multipart_obj_key)
        print(f"删除文件 {multipart_obj_key} 完成")
        
        # 删除临时文件
        os.remove(temp_file_path)
        print(f"删除临时文件 {temp_file_path} 完成")
        
        # 输出测试结果汇总
        print("\n===== 测试结果汇总 =====")
        all_passed = True
        for test_name, result in test_results.items():
            status = "通过" if result else "失败"
            if not result:
                all_passed = False
            print(f"{test_name}: {status}")
        
        print(f"\n总体测试结果: {'全部通过' if all_passed else '部分失败'}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tos_client()


