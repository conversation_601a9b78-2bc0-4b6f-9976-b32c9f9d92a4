"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-11 14:36:51
FilePath: /global_rtc_client/utils/perf/app_control.py
Description:
"""
"""应用管理器"""
import os
import json
import asyncio
from typing import Dict, Any, Optional, List
from config.constants import (
    AppType,
    TaskType,
    PlatformType,
    AppInstallMode,
    LibraGroupType,
    ErrorCode,
)
from core.settings_manager import settings_manager
from utils.common.log_utils import get_logger
from utils.common.android_device import android_device
from utils.common.ios_device import ios_device
from utils.perf.case_control import case_control
from utils.common.network_utils import network_utils
from utils.common.file_utils import write_py_kv

logger = get_logger(__name__)


class AppControl:
    """应用控制器"""

    def __init__(self):
        """初始化应用控制器"""
        self.task_id = None
        self.task_type = None
        self.sub_task_id = None
        self.sub_task_detail = None
        self.app_group = {}
        self.case_group = {}
        self.device_group = {}
        self.account_group = {}
        self._current_download_task = None
        self._current_install_task = None
        self.resources_folder = None
        
        # 使用新的配置结构
        self.tasks_base_dir = settings_manager.get('storage.paths.base.tasks', '.tasks')
        self.task_resources_dir = settings_manager.get('storage.paths.task.resources', 'resources')
        self.task_results_dir = settings_manager.get('storage.paths.task.results', 'results')
        self.base_repo_dir = settings_manager.get('storage.paths.base.repo', 'repos')
        self.download_retries = settings_manager.get('app.download.retry.count', 3)
        self.download_retry_interval = settings_manager.get('app.download.retry.interval', 5)
        self.chunk_size = settings_manager.get('app.download.chunk_size', 8192)
        self.download_timeout = settings_manager.get('app.download.timeout', 600)

        # 添加取消事件
        self._cancel_event = asyncio.Event()

        # 添加重打包队列
        self._repack_queue = asyncio.Queue()
        self._repack_tasks = []
        self._max_concurrent_repack = 3  # 最大并发重打包数

    async def _cancel_task(self, task: asyncio.Task, task_name: str) -> bool:
        """取消单个异步任务
        
        Args:
            task: 要取消的异步任务
            task_name: 任务名称(用于日志)
            
        Returns:
            bool: 取消是否成功
        """
        if not task or task.done():
            return True
            
        try:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            logger.info(f"[子任务 {self.sub_task_id}] 终止{task_name}成功")
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 终止{task_name}失败: {str(e)}")
            return False
        
    async def process_apps(self, install_mode: int, resources_folder: str) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 开始处理应用，安装模式: {install_mode}，资源目录: {resources_folder}")
        try:
            # 初始化资源文件夹
            self.resources_folder = resources_folder

            # 1. 获取平台应用列表
            platform = self.sub_task_detail.get('platform')
            app_lists = await self._get_platform_apps(platform)
            if not app_lists:
                logger.error(f"[子任务 {self.sub_task_id}] 获取平台应用列表失败")
                return False
                
            # 2. 获取所有应用
            all_apps = []
            # Libra实验类型只处理第一个性能包和所有辅助包
            if self.task_type == TaskType.LIBRA_EXPERIMENT:
                logger.debug(f"[子任务 {self.sub_task_id}] Libra实验类型，仅处理第一个性能包和所有辅助包")
                perf_apps = app_lists.get('perf_apps', [])
                assist_apps = app_lists.get('assist_apps', [])
                if perf_apps:
                    logger.debug(f"[子任务 {self.sub_task_id}] Libra实验-选中性能包: {perf_apps[0].get('name', '未知')} ")
                    all_apps.append(perf_apps[0])
                logger.debug(f"[子任务 {self.sub_task_id}] Libra实验-辅助包数量: {len(assist_apps)}")
                all_apps.extend(assist_apps)
            else:
                logger.debug(f"[子任务 {self.sub_task_id}] 任务类型: {self.task_type}，处理全部性能包和辅助包")
                for app_type, apps in app_lists.items():
                    logger.debug(f"[子任务 {self.sub_task_id}] 处理 {app_type}，数量: {len(apps)}")
                    all_apps.extend(apps)
                
            if not all_apps:
                logger.error(f"[子任务 {self.sub_task_id}] 没有找到需要处理的应用")
                return False
                
            # 3. 创建重打包队列和任务
            self._repack_queue = asyncio.Queue()
            self._repack_tasks = []
            repack_processor = asyncio.create_task(self._process_repack_queue())
            
            # 4. 下载应用
            processed_apps = []
            for app in all_apps:
                if self._cancel_event.is_set():
                    break
                    
                app_name = app.get('name', '未知应用')
                logger.debug(f"[子任务 {self.sub_task_id}] 待处理应用: {app_name}")
                
                # 下载应用
                downloaded_app = await self._download_single_app(install_mode, app)
                if not downloaded_app:
                    logger.error(f"[子任务 {self.sub_task_id}] 应用 {app_name} 下载失败")
                    return False
                    
                processed_apps.append(downloaded_app)
                
                # 将应用加入重打包队列
                await self._repack_queue.put(downloaded_app)
                
            # 5. 等待所有重打包任务完成
            await self._repack_queue.put(None)  # 发送结束信号
            await repack_processor
            
            # 6. 检查重打包结果，如果失败则重试一次
            repack_failed = False
            failed_apps = []
            
            # 第一次检查重打包结果
            for task in self._repack_tasks:
                if not task.cancelled() and not task.result():
                    repack_failed = True
                    failed_apps.append(task.get_name())  # 记录失败的应用名称
                    
            # 如果有重打包失败的应用，进行重试
            if repack_failed:
                logger.warning(f"[子任务 {self.sub_task_id}] 部分应用重打包失败，开始重试: {', '.join(failed_apps)}")
                
                # 清理之前的重打包任务
                for task in self._repack_tasks:
                    if not task.done():
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
                
                # 重新创建重打包队列和任务
                self._repack_queue = asyncio.Queue()
                self._repack_tasks = []
                repack_processor = asyncio.create_task(self._process_repack_queue())
                
                # 重新将失败的应用加入重打包队列
                for app in processed_apps:
                    if app.get('name') in failed_apps:
                        await self._repack_queue.put(app)
                
                # 等待重试的重打包任务完成
                await self._repack_queue.put(None)
                await repack_processor
                
                # 检查重试结果
                retry_failed = False
                for task in self._repack_tasks:
                    if not task.cancelled() and not task.result():
                        retry_failed = True
                        break
                        
                if retry_failed:
                    logger.error(f"[子任务 {self.sub_task_id}] 应用重打包重试失败，任务终止")
                    return False
                else:
                    logger.info(f"[子任务 {self.sub_task_id}] 应用重打包重试成功")
                
            # 7. 同步已处理的应用信息
            if not await self._update_app_group(platform, app_lists, all_apps, processed_apps):
                logger.error(f"[子任务 {self.sub_task_id}] 同步应用信息失败")
                return False
                
            logger.debug(f"[子任务 {self.sub_task_id}] 已完成所有应用处理，共处理 {len(processed_apps)} 个应用")
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理应用过程中发生错误: {str(e)}", exc_info=True)
            return False
        finally:
            logger.debug(f"[子任务 {self.sub_task_id}] 退出 process_apps")
            # 清理重打包任务
            for task in self._repack_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

    async def _cleanup_empty_files(self, directory: str) -> bool:
        """清理指定目录下的空文件
        
        Args:
            directory: 要清理的目录路径
            
        Returns:
            bool: 清理是否成功
        """
        if not os.path.exists(directory):
            return True
            
        try:
            for file in os.listdir(directory):
                file_path = os.path.join(directory, file)
                if os.path.isfile(file_path) and os.path.getsize(file_path) == 0:
                    try:
                        os.remove(file_path)
                        logger.info(f"[子任务 {self.sub_task_id}] 清理临时文件: {file_path}")
                    except Exception as e:
                        logger.error(f"[子任务 {self.sub_task_id}] 清理临时文件失败: {str(e)}")
                        return False
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 清理目录失败: {str(e)}")
            return False

    async def terminate_app_processes(self) -> bool:
        """终止所有app相关的进程"""
        try:
            # 1. 设置取消标志
            self._cancel_event.set()
            
            # 2. 定义需要取消的任务列表
            tasks_to_cancel = [
                (self._current_download_task, "下载任务"),
                (self._current_install_task, "安装任务")
            ]
            
            # 3. 取消所有重打包任务
            for task in self._repack_tasks:
                if not task.done():
                    tasks_to_cancel.append((task, "重打包任务"))
            
            # 4. 并发取消所有任务
            cancel_results = await asyncio.gather(
                *[self._cancel_task(task, name) for task, name in tasks_to_cancel],
                return_exceptions=True
            )
            
            # 5. 检查任务取消结果
            if not all(isinstance(result, bool) and result for result in cancel_results):
                logger.error(f"[子任务 {self.sub_task_id}] 部分任务终止失败")
                return False
                
            # 6. 重置任务引用
            self._current_download_task = None
            self._current_install_task = None
            self.resources_folder = None
            self._repack_tasks.clear()
            
            logger.info(f"[子任务 {self.sub_task_id}] 终止应用进程成功")
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 终止应用进程失败: {str(e)}")
            return False


    async def install_app(self, device: Dict, app_path: str, package_name: str = None,
                          install_mode: int = None) -> bool:
        """安装应用"""
        try:
            if asyncio.current_task().cancelled():
                return False

            if not app_path:
                logger.warning(f"[子任务 {self.sub_task_id}] APP路径为空，无法安装应用")
                return False

            device_os = device.get('sys_type')
            if device_os is None:
                raise ValueError(f"[子任务 {self.sub_task_id}] 设备系统类型无效: {device.get('name')}")

            device_udid = device.get('udid')
            if not device_udid:
                raise ValueError(f"[子任务 {self.sub_task_id}] 设备ID为空: {device.get('name')}")

            if device_os == PlatformType.ANDROID.value:  # Android
                self._current_install_task = asyncio.create_task(
                    android_device.install_app(
                        device_udid,
                        app_path,
                        install_mode=install_mode,
                        package_name=package_name
                    ),
                    name=f"android-app-install-{device_udid}"
                )

            elif device_os == PlatformType.IOS.value:  # iOS
                self._current_install_task = asyncio.create_task(
                    ios_device.install_app(
                        device_udid,
                        app_path,
                        install_mode=install_mode,
                        repackage_cert=device.get('repackage_cert')
                    ),
                    name=f"ios-app-install-{device_udid}"
                )

            try:
                success = await self._current_install_task
                if success:
                    logger.info(f"[子任务 {self.sub_task_id}] 应用安装成功")
                else:
                    logger.error(f"[子任务 {self.sub_task_id}] 应用安装失败")
                return success
            except asyncio.CancelledError:
                logger.warning(f"[子任务 {self.sub_task_id}] 应用安装任务被取消")
                return False
            finally:
                self._current_install_task = None

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 安装应用失败: {str(e)}")
            return False

    async def _download_app(self, app_info: Dict) -> Optional[str]:
        """下载应用"""
        try:
            # 获取应用信息
            app_url = app_info.get('url', '')
            app_name = app_info.get('name', '未知应用')
            if not app_url:
                raise ValueError(f"[子任务 {self.sub_task_id}] 应用 {app_name} 的URL为空")

            # 根据应用类型设置文件前缀
            app_type = app_info.get('app_type', AppType.PERF.value)  # 默认为性能包
            file_prefix = AppType.get_prefix(app_type)
            
            # 从URL中提取文件名
            file_name = file_prefix + os.path.basename(app_url.split('?')[0])  # 移除URL参数
            if not file_name.endswith(('.apk', '.ipa')):
                # 根据平台类型添加默认扩展名
                platform = app_info.get('platform')
                if platform == PlatformType.ANDROID.value:
                    file_name += '.apk'
                elif platform == PlatformType.IOS.value:
                    file_name += '.ipa'
                
            app_path = os.path.join(self.resources_folder, file_name)

            # 如果文件已存在且有效，直接返回
            if os.path.exists(app_path) and os.path.getsize(app_path) > 0:
                logger.info(f"[子任务 {self.sub_task_id}] 应用文件已存在: {app_path}")
                return app_path

            # 确保目标目录存在
            os.makedirs(os.path.dirname(app_path), exist_ok=True)

            # 下载应用(带重试)
            retry_count = 0
            while retry_count < self.download_retries:
                if self._cancel_event.is_set():
                    logger.info("下载任务被取消")
                    return None
                    
                try:
                    logger.info(f"[子任务 {self.sub_task_id}] 开始下载应用: {app_name}")
                    downloaded_path = await network_utils.download_file(app_url, app_path, self.download_timeout)
                    
                    if downloaded_path:
                        logger.info(f"[子任务 {self.sub_task_id}] 应用下载成功: {app_name}")
                        return downloaded_path
                    else:
                        raise Exception(f"[子任务 {self.sub_task_id}] 下载失败")

                except Exception as e:
                    logger.error(f"[子任务 {self.sub_task_id}] 下载应用失败: {str(e)}")
                    retry_count += 1
                    if retry_count < self.download_retries:
                        logger.info(f"[子任务 {self.sub_task_id}] 下载失败，{self.download_retry_interval}秒后进行第{retry_count + 1}次重试...")
                        await asyncio.sleep(self.download_retry_interval)
                    else:
                        raise Exception(f"[子任务 {self.sub_task_id}] 下载应用失败: {app_name}, URL: {app_url}, 已重试{retry_count}次")

            return None

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 下载应用失败: {str(e)}")
            return None

    async def _repackage_app(self, app_path: str, app_info: Dict) -> Optional[str]:
        logger.debug(f"[子任务 {self.sub_task_id}] 正在重打包应用文件: {app_path}")
        try:
            if not os.path.exists(app_path):
                logger.error(f"[子任务 {self.sub_task_id}] 应用文件不存在")
                return None

            # 获取文件名和目录
            app_dir = os.path.dirname(app_path)
            app_name = os.path.basename(app_path)
            base_name = os.path.splitext(app_name)[0]

            # 根据平台类型构建重打包文件路径
            if app_path.endswith('.apk'):
                repack_path = os.path.join(app_dir, f"{base_name}-repack-signed.apk")
            elif app_path.endswith('.ipa'):
                repack_path = os.path.join(app_dir, f"{base_name}-repack-signed.ipa")
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 不支持的应用类型: {app_path}")
                return None

            # 检查重打包文件是否存在且有效
            if os.path.exists(repack_path) and os.path.getsize(repack_path) > 100 * 1024 * 1024:  # 100MB
                logger.info(f"[子任务 {self.sub_task_id}] 重打包文件已存在且有效，跳过重打包")
                return repack_path

            logger.debug(f"[子任务 {self.sub_task_id}] 开始重打包应用")

            # 根据文件扩展名判断平台并执行重打包
            if app_path.endswith('.apk'):
                result = await android_device.repack_app(
                    app_path=app_path,
                    save_dir=app_dir
                )
            elif app_path.endswith('.ipa'):
                result = await ios_device.repack_app(
                    app_path=app_path,
                    save_dir=app_dir,
                    repackage_cert=app_info.get('repackage_cert')
                )

            if result:
                logger.info(f"[子任务 {self.sub_task_id}] 应用重打包成功")
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 应用重打包失败")

            logger.debug(f"[子任务 {self.sub_task_id}] 已完成重打包，输出路径: {repack_path if 'repack_path' in locals() else None}")
            return result

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 重打包应用失败: {str(e)}", exc_info=True)
            return None
        

    async def _download_single_app(self, install_mode: int, app_info: Dict) -> Optional[Dict]:
        logger.debug(f"[子任务 {self.sub_task_id}] 正在下载应用: {app_info.get('name', 'unknown')}")
        try:
            # 1. 如果当前任务被取消，则返回None
            if self._cancel_event.is_set():
                return None
            
            # 2. 如果安装模式为不安装，则直接返回应用信息
            if install_mode == AppInstallMode.NOT_INSTALL.value:
                logger.info(f"[子任务 {self.sub_task_id}] 安装模式为不安装，不需要处理应用")
                return app_info

            # 3. 检查是否已经有处理好的应用路径
            if app_info.get('app_path') and os.path.exists(app_info['app_path']):
                return app_info

            # 4. 下载应用
            app_name = app_info.get('name', 'unknown')
            self._current_download_task = asyncio.create_task(
                self._download_app(app_info),
                name=f"app-download-{app_name}"
            )
            try:
                app_path = await self._current_download_task
            except asyncio.CancelledError:
                return None
            finally:
                self._current_download_task = None

            # 5. 如果下载失败，则返回None
            if not app_path:
                logger.error(f"[子任务 {self.sub_task_id}] 下载应用失败")
                return None

            # 6. 更新应用路径并返回
            app_info['app_path'] = app_path
            logger.debug(f"[子任务 {self.sub_task_id}] 已完成应用下载，路径: {app_info.get('app_path')}")
            return app_info

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 下载应用失败: {str(e)}", exc_info=True)
            return None

    async def _process_repack_queue(self):
        """处理重打包队列"""
        try:
            while True:
                # 获取队列中的应用
                app_info = await self._repack_queue.get()
                
                # 检查是否是结束信号
                if app_info is None:
                    break
                    
                # 如果任务已取消，停止处理
                if self._cancel_event.is_set():
                    break
                    
                # 创建重打包任务
                app_name = app_info.get('name', 'unknown')
                task = asyncio.create_task(
                    self._repack_single_app(app_info),
                    name=f"app-repack-{app_name}"
                )
                self._repack_tasks.append(task)
                
                # 限制并发重打包数量
                while len([t for t in self._repack_tasks if not t.done()]) >= self._max_concurrent_repack:
                    await asyncio.sleep(1)
                    
                # 检查是否有任务失败
                for t in self._repack_tasks:
                    if t.done() and t.exception():
                        logger.error(f"[子任务 {self.sub_task_id}] 重打包任务异常: {t.exception()}")
                        return False
                        
            # 等待所有重打包任务完成
            if self._repack_tasks:
                await asyncio.gather(*self._repack_tasks)
                
            return True
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理重打包队列失败: {str(e)}")
            return False

    async def _repack_single_app(self, app_info: Dict) -> bool:
        """重打包单个应用"""
        try:
            app_path = app_info.get('app_path')
            app_name = app_info.get('name', '未知应用')
            
            if not app_path or not os.path.exists(app_path):
                logger.error(f"[子任务 {self.sub_task_id}] 应用 {app_name} 路径无效")
                return False
                
            logger.info(f"[子任务 {self.sub_task_id}] 开始重打包应用: {app_name}")
            repack_path = await self._repackage_app(app_path, app_info)
            
            if not repack_path:
                logger.error(f"[子任务 {self.sub_task_id}] 应用 {app_name} 重打包失败")
                return False
                
            app_info['app_path'] = repack_path
            logger.info(f"[子任务 {self.sub_task_id}] 应用 {app_name} 重打包完成")
            return True
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 重打包应用失败: {str(e)}")
            return False

    async def _get_platform_apps(self, platform: int) -> Optional[Dict[str, List]]:
        """获取指定平台的应用列表
        
        Args:
            platform: 平台类型
            
        Returns:
            Dict包含性能测试应用和辅助应用列表
        """
        if platform == PlatformType.ANDROID.value:
            return {
                'perf_apps': self.app_group.get('android_perf_app_id_list', []),
                'assist_apps': self.app_group.get('android_assist_app_id_list', [])
            }
        elif platform == PlatformType.IOS.value:
            return {
                'perf_apps': self.app_group.get('ios_perf_app_id_list', []),
                'assist_apps': self.app_group.get('ios_assist_app_id_list', [])
            }
        else:
            logger.error(f"[子任务 {self.sub_task_id}] 不支持的平台类型: {platform}")
            return None

    async def _batch_install_app_to_devices(self, app: dict, install_mode: int, device_list: list = None) -> bool:
        """批量安装应用到指定设备列表
        
        Args:
            app: 应用信息字典
            install_mode: 安装模式
            device_list: 目标设备列表
            
        Returns:
            bool: 是否全部安装成功
        """
        try:
            # 1. 检查安装模式
            if install_mode == AppInstallMode.NOT_INSTALL.value:
                logger.info(f"[子任务 {self.sub_task_id}] 安装模式为不安装，跳过安装应用")
                return True

            # 2. 检查任务是否已被取消
            if self._cancel_event.is_set():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止安装应用")
                return False

            # 3. 获取应用信息
            app_name = app.get('name', '未知应用')
            app_path = app.get('app_path')
            platform = app.get('platform')
            app_type = app.get('app_type')
            
            # 4. 获取应用包名
            package_name = None
            if platform == PlatformType.ANDROID.value:
                package_name = android_device.get_package_name_by_app(app_path)
                if not package_name:
                    logger.error(f"[子任务 {self.sub_task_id}] 无法获取应用包名")
                    return False

            # 5. 检查应用路径是否有效
            if not app_path or not os.path.exists(app_path):
                logger.error(f"[子任务 {self.sub_task_id}] 应用路径无效: {app_path}")
                return False

            # 6. 获取目标设备列表
            target_devices = device_list or []
            if not target_devices:
                logger.error(f"[子任务 {self.sub_task_id}] 未找到目标设备")
                return False

            # 7. 获取应用类型名称
            app_type_str = AppType.get_name(app_type) if app_type else "未知类型"
            logger.info(f"[子任务 {self.sub_task_id}] 开始安装{app_type_str}应用: {app_name}")

            # 8. 在每个目标设备上安装应用
            for device in target_devices:
                if self._cancel_event.is_set():
                    logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止安装应用")
                    return False

                # 8-1. 安装应用
                if not await self.install_app(
                    device=device,
                    app_path=app_path,
                    package_name=package_name,
                    install_mode=install_mode
                ):
                    raise Exception(f"在设备 {device.get('name')} 上安装应用失败: {app_name}")

                # 8-2. 处理Android设备的mapping文件
                jenkins_url = app.get('jenkins_build_result_url')
                if all([platform == PlatformType.ANDROID.value, jenkins_url, package_name]):
                    if not await self._handle_android_mapping(
                        device.get('udid'),
                        jenkins_url,
                        package_name,
                        app_type
                    ):
                        return False
            return True

        except asyncio.CancelledError:
            logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止安装应用")
            return False
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 安装应用失败: {str(e)}")
            return False

    async def _handle_android_mapping(self, device_udid: str, jenkins_url: str, 
                                    package_name: str, app_type: int) -> bool:
        """处理Android设备的mapping文件"""
        if asyncio.current_task().cancelled():
            return False

        # 下载mapping文件
        if not await self._download_mapping_files(
            jenkins_build_result_url=jenkins_url,
            app_type=app_type
        ):
            logger.error(f"[子任务 {self.sub_task_id}] 下载mapping文件失败")
            return False

        if asyncio.current_task().cancelled():
            return False

        # 推送mapping文件到设备
        if not await android_device.push_mapping_files_to_device(
            device_udid,
            jenkins_url,
            package_name,
            self.resources_folder,
            app_type
        ):
            logger.error(f"[子任务 {self.sub_task_id}] 推送mapping文件失败")
            return False

        return True
    
    async def _handle_assist_apps(self, install_mode: int) -> bool:
        """处理辅助应用

        根据辅助设备的平台类型智能选择应用：
        - Android辅助设备：优先安装Android辅助应用包，如果没有则安装Android性能测试应用包
        - iOS辅助设备：优先安装iOS辅助应用包，如果没有则安装iOS性能测试应用包
        """
        try:
            # 获取辅助设备列表
            auxil_devices = self.device_group.get('auxil_devices', [])
            if not auxil_devices:
                logger.info(f"[子任务 {self.sub_task_id}] 没有辅助设备，跳过辅助应用处理")
                return True

            logger.info(f"[子任务 {self.sub_task_id}] 开始处理 {len(auxil_devices)} 个辅助设备的应用安装")

            # 按设备平台类型分组处理
            android_devices = []
            ios_devices = []

            for device in auxil_devices:
                device_platform = device.get('sys_type')
                if device_platform == PlatformType.ANDROID.value:
                    android_devices.append(device)
                elif device_platform == PlatformType.IOS.value:
                    ios_devices.append(device)
                else:
                    logger.warning(f"[子任务 {self.sub_task_id}] 未知设备平台类型: {device_platform}, 设备: {device.get('name', '未知')}")

            # 处理Android辅助设备
            if android_devices:
                if not await self._handle_assist_devices_by_platform(
                    devices=android_devices,
                    platform_type=PlatformType.ANDROID.value,
                    install_mode=install_mode
                ):
                    return False

            # 处理iOS辅助设备
            if ios_devices:
                if not await self._handle_assist_devices_by_platform(
                    devices=ios_devices,
                    platform_type=PlatformType.IOS.value,
                    install_mode=install_mode
                ):
                    return False

            logger.info(f"[子任务 {self.sub_task_id}] 所有辅助设备应用处理完成")
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理辅助应用失败: {str(e)}")
            return False

    async def _handle_assist_devices_by_platform(self, devices: List[Dict], platform_type: int, install_mode: int) -> bool:
        """根据平台类型处理辅助设备的应用安装

        Args:
            devices: 同一平台类型的辅助设备列表
            platform_type: 平台类型 (PlatformType.ANDROID.value 或 PlatformType.IOS.value)
            install_mode: 安装模式

        Returns:
            bool: 是否处理成功
        """
        try:
            platform_name = "Android" if platform_type == PlatformType.ANDROID.value else "iOS"
            logger.info(f"[子任务 {self.sub_task_id}] 开始处理 {len(devices)} 个 {platform_name} 辅助设备")

            # 获取对应平台的辅助应用包
            assist_app_key = 'android_assist_app_id_list' if platform_type == PlatformType.ANDROID.value else 'ios_assist_app_id_list'
            assist_apps = self.app_group.get(assist_app_key, [])

            # 选择要安装的应用
            apps_to_install = []

            if assist_apps:
                # 如果存在辅助应用包，使用辅助应用包
                apps_to_install = assist_apps
                logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 辅助设备将安装 {len(assist_apps)} 个辅助应用包")
                for i, app in enumerate(assist_apps, 1):
                    logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 辅助应用包 {i}: {app.get('name', '未知')}")
            else:
                # 如果不存在辅助应用包，使用性能测试应用包
                perf_app_key = 'android_perf_app_id_list' if platform_type == PlatformType.ANDROID.value else 'ios_perf_app_id_list'
                perf_apps = self.app_group.get(perf_app_key, [])

                if perf_apps:
                    # 使用第一个性能包作为辅助包，但需要确保应用已经处理过
                    perf_app = perf_apps[0]
                    app_name = perf_app.get('name', '未知')

                    # 检查应用是否已经处理过（有app_path字段）
                    if not perf_app.get('app_path'):
                        logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 性能测试应用包 {app_name} 尚未处理，开始下载和重打包")

                        # 下载应用
                        processed_app = await self._download_single_app(install_mode, perf_app)
                        if not processed_app:
                            logger.error(f"[子任务 {self.sub_task_id}] {platform_name} 性能测试应用包 {app_name} 下载失败")
                            return False

                        # 重打包应用
                        if not await self._repack_single_app(processed_app):
                            logger.error(f"[子任务 {self.sub_task_id}] {platform_name} 性能测试应用包 {app_name} 重打包失败")
                            return False

                        # 更新应用组中的应用信息
                        self.app_group[perf_app_key][0] = processed_app
                        perf_app = processed_app
                        logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 性能测试应用包 {app_name} 处理完成")

                    apps_to_install = [perf_app]
                    logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 辅助设备没有专用辅助应用包，将使用性能测试应用包: {app_name}")
                else:
                    logger.error(f"[子任务 {self.sub_task_id}] {platform_name} 平台既没有辅助应用包也没有性能测试应用包")
                    return False

            # 在设备上安装应用
            for i, app in enumerate(apps_to_install, 1):
                if self._cancel_event.is_set():
                    logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止安装应用")
                    return False

                app_name = app.get('name', '未知应用')
                app_type_str = "辅助应用" if assist_apps else "性能测试应用(作为辅助应用)"

                logger.info(f"[子任务 {self.sub_task_id}] 开始在 {platform_name} 辅助设备上安装第 {i} 个{app_type_str}: {app_name}")

                if not await self._batch_install_app_to_devices(
                    app=app,
                    install_mode=install_mode,
                    device_list=devices
                ):
                    logger.error(f"[子任务 {self.sub_task_id}] 在 {platform_name} 辅助设备上安装{app_type_str}失败: {app_name}")
                    return False

                logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 辅助设备{app_type_str} {app_name} 安装完成")

            logger.info(f"[子任务 {self.sub_task_id}] {platform_name} 辅助设备应用处理完成")
            return True

        except Exception as e:
            platform_name = "Android" if platform_type == PlatformType.ANDROID.value else "iOS"
            logger.error(f"[子任务 {self.sub_task_id}] 处理 {platform_name} 辅助设备应用失败: {str(e)}")
            return False

    async def _execute_version_regression_tests(self, perf_apps: List[Dict], install_mode: int, 
                                          perf_device: List[Dict], output_dir: str, 
                                          cases_to_retry: Dict = None) -> bool:
        """执行版本回归测试
        
        Args:
            perf_apps: 性能测试应用列表
            install_mode: 安装模式
            perf_device: 性能测试设备列表
            output_dir: 输出目录
            cases_to_retry: 需要重试的用例信息，用于重试场景
            
        Returns:
            bool: 是否执行成功
        """
        overall_success = True
        
        for i, perf_app in enumerate(perf_apps, 1):
            if self._cancel_event.is_set():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止处理剩余应用")
                return False

            app_name = perf_app.get('name', '未知应用')
            logger.info(f"[子任务 {self.sub_task_id}] 开始处理第 {i} 个应用: {app_name}")

            # 如果是重试场景，获取应用的重试用例
            if cases_to_retry:
                app_id = perf_app.get('id')
                app_retry_info = cases_to_retry.get(app_id)
                if not app_retry_info:
                    logger.info(f"[子任务 {self.sub_task_id}] 应用 {app_name} 没有需要重试的用例")
                    continue
                perf_app['cases_to_retry'] = app_retry_info

            # 安装应用
            if not await self._batch_install_app_to_devices(
                    app=perf_app,
                    install_mode=install_mode,
                    device_list=perf_device
            ):
                logger.error(f"[子任务 {self.sub_task_id}] 安装性能测试应用失败: {app_name}")
                overall_success = False
                continue
                
            # 执行用例
            try:
                if self._cancel_event.is_set():
                    return False
                
                # 创建测试用例列表，如果是重试场景，只包含需要重试的用例
                test_cases = self.case_group
                
                run_success = await case_control.execute_case_group(
                    test_cases=test_cases,
                    perf_app=perf_app,
                    output_dir=output_dir
                )
                if not run_success:
                    logger.info(f"[子任务 {self.sub_task_id}] 应用 {app_name} 部分用例执行失败")
                    overall_success = False
            except Exception as e:
                logger.error(f"[子任务 {self.sub_task_id}] 应用 {app_name} 执行用例失败: {str(e)}")
                overall_success = False
                
        return overall_success

    async def _execute_libra_experiment_tests(self, perf_app: Dict, install_mode: int,
                                          perf_device: List[Dict], output_dir: str,
                                          experiment_configs: dict = None,
                                          cases_to_retry: dict = None) -> bool:
        """执行Libra实验测试，适配新版 experiment_configs 结构，支持重试时只跑有失败用例的组"""
        app_name = perf_app.get('name', '未知应用')
        logger.info(f"[子任务 {self.sub_task_id}] 开始处理Libra实验应用: {app_name}")
        overall_success = True
        if not experiment_configs:
            logger.error(f"[子任务 {self.sub_task_id}] 未提供实验配置")
            return False
            
        # 安装性能应用
        if not await self._batch_install_app_to_devices(
                app=perf_app,
                install_mode=install_mode,
                device_list=perf_device
        ):
            logger.error(f"[子任务 {self.sub_task_id}] 安装性能测试应用失败: {app_name}")
            return False
                        
        # 从配置中获取 version_type
        experiment_version_type = experiment_configs.get('experiment_group_version_type', LibraGroupType.EXPERIMENT.value)
        control_version_type = experiment_configs.get('control_group_version_type', LibraGroupType.CONTROL.value)
        # 遍历实验组和对照组
        group_configs = [
            {
                'group_type': LibraGroupType.EXPERIMENT,
                'version_ids': experiment_configs.get('experiment_group_version_ids', []),
                'version_type': experiment_version_type
            },
            {
                'group_type': LibraGroupType.CONTROL,
                'version_ids': experiment_configs.get('control_group_version_ids', []),
                'version_type': control_version_type
            }
        ]
        for group_config in group_configs:
            group_type = group_config['group_type']
            version_ids = group_config['version_ids']
            version_type = group_config['version_type']
            if not version_ids:
                continue
            # 如果是重试，且没有该组的重试用例，跳过
            if cases_to_retry is not None and version_type not in cases_to_retry.keys():
                continue
                
            # 写入 repos/global_business_perf/settings.py
            settings_path = os.path.join(self.base_repo_dir, "global_business_perf", 'settings.py')
            write_py_kv(settings_path, 'GLOBAL_BUSINESS_PERF_TASK_CURRENT_EXPERIMENT_TYPE', group_type.value)
            logger.info(f"[子任务 {self.sub_task_id}] 已写入实验组类型: {group_type.value} 到 settings.py")
            
            # 设置实验配置
            perf_app['experiment_config'] = {
                'type': group_type.value,
                'version_ids': version_ids,
                'version_type': version_type 
            }
            logger.info(f"[子任务 {self.sub_task_id}] 设置实验配置: type={group_type.value}, version_type={version_type}")

            try:
                # 执行用例组
                run_success = await case_control.execute_case_group(
                    test_cases=self.case_group,
                    perf_app=perf_app,
                    output_dir=output_dir
                )
                if not run_success:
                    logger.info(f"[子任务 {self.sub_task_id}] 应用 {app_name} 实验组类型 {group_type.value} 部分用例执行失败")
                    overall_success = False
            except Exception as e:
                logger.error(f"[子任务 {self.sub_task_id}] 应用 {app_name} 实验组类型 {group_type.value} 执行用例失败: {str(e)}")
                overall_success = False
        return overall_success

    async def _prepare_retry_cases_by_version_regression(self, group: Dict) -> Dict:
        """准备版本回归测试的重试用例信息"""
        cases_to_retry = []
        for case_stat in group.get('cases', []):
            if case_stat.get('retry_count', 0) > 0:
                cases_to_retry.append({
                    'case_id': case_stat.get('case_id'),
                    'retry_count': case_stat.get('retry_count'),
                })
        return cases_to_retry

    async def _prepare_retry_cases_by_libra(self, group: Dict) -> Dict:
        """准备按实验配置分组的重试用例"""
        cases_to_retry = []
        for case_stat in group.get('cases', []):
            if case_stat.get('retry_count', 0) > 0:
                cases_to_retry.append({
                    'case_id': case_stat.get('case_id'),
                    'retry_count': case_stat.get('retry_count')
                })
        return cases_to_retry

    async def _execute_initial_perf_tests(self, platform: int, install_mode: int, output_dir: str) -> bool:
        """执行初始性能测试"""
        try:
            # 基础检查
            perf_apps = self.app_group.get(
                'android_perf_app_id_list' if platform == PlatformType.ANDROID.value else 'ios_perf_app_id_list',[]
            )
            
            if not perf_apps:
                logger.info(f"[子任务 {self.sub_task_id}] 没有需要处理的性能测试应用")
                return True
                
            if not self.device_group.get('perf_device'):
                logger.error(f"[子任务 {self.sub_task_id}] 未找到性能测试设备")
                return False
                
            perf_device = [self.device_group['perf_device']]
            
            # 根据任务类型执行测试
            if self.task_type == TaskType.VERSION_REGRESSION:
                return await self._execute_version_regression_tests(
                    perf_apps=perf_apps,
                    install_mode=install_mode,
                    perf_device=perf_device,
                    output_dir=output_dir
                )
            elif self.task_type == TaskType.LIBRA_EXPERIMENT:
                # 获取实验配置
                experiment_configs = self.sub_task_detail.get('experiment_configs', {})
                if not experiment_configs:
                    logger.error(f"[子任务 {self.sub_task_id}] Libra实验配置无效")
                    return False
                    
                return await self._execute_libra_experiment_tests(
                    perf_app=perf_apps[0],
                    install_mode=install_mode,
                    perf_device=perf_device,
                    output_dir=output_dir,
                    experiment_configs=experiment_configs
                )
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 不支持的任务类型: {TaskType.get_name(self.task_type)}")
                return False
                
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行初始性能测试失败: {str(e)}")
            return False

    async def _execute_retry_perf_tests(self, platform: int, install_mode: int, output_dir: str, stats: Dict) -> bool:
        """执行重试性能测试"""
        try:
            # 基础检查
            perf_apps = self.app_group.get(
                'android_perf_app_id_list' if platform == PlatformType.ANDROID.value else 'ios_perf_app_id_list',
                []
            )
            
            if not perf_apps:
                logger.info(f"[子任务 {self.sub_task_id}] 没有需要处理的性能测试应用")
                return True
                
            if not self.device_group.get('perf_device'):
                logger.error(f"[子任务 {self.sub_task_id}] 未找到性能测试设备")
                return False
                
            perf_device = [self.device_group['perf_device']]
            group_stats = stats.get('group_stats', [])
            logger.debug(f"[子任务 {self.sub_task_id}] 用例执行统计信息(group_stats): {stats}")

            if self.task_type == TaskType.VERSION_REGRESSION:
                # 准备版本回归测试的重试用例
                cases_to_retry = {}
                for group in group_stats:
                    app_id = group.get('app_id')
                    if not app_id:
                        continue
                    retry_cases = await self._prepare_retry_cases_by_version_regression(group)
                    if retry_cases:
                        logger.info(f"[子任务 {self.sub_task_id}] app_id: {app_id} 版本回归重试分组结果: {retry_cases}")
                        cases_to_retry[app_id] = retry_cases
                
                if not cases_to_retry:
                    logger.info(f"[子任务 {self.sub_task_id}] 没有需要重试的用例")
                    return True
                    
                return await self._execute_version_regression_tests(
                    perf_apps=perf_apps,
                    install_mode=install_mode,
                    perf_device=perf_device,
                    output_dir=output_dir,
                    cases_to_retry=cases_to_retry
                )
            elif self.task_type == TaskType.LIBRA_EXPERIMENT:
                # 准备Libra实验的重试用例
                cases_to_retry = {}
                for group in group_stats:
                    version_type = group.get('version_type')
                    if not version_type:
                        continue
                    retry_cases = await self._prepare_retry_cases_by_libra(group)
                    if retry_cases:
                        logger.info(f"[子任务 {self.sub_task_id}] version_type: {version_type} 按实验配置分组重试分组结果: {retry_cases}")
                        cases_to_retry[version_type] = retry_cases
                
                if not cases_to_retry:
                    logger.info(f"[子任务 {self.sub_task_id}] 没有需要重试的用例")
                    return True
                    
                perf_app = perf_apps[0]
                return await self._execute_libra_experiment_tests(
                    perf_app=perf_app,
                    install_mode=install_mode,
                    perf_device=perf_device,
                    output_dir=output_dir,
                    experiment_configs=self.sub_task_detail.get('experiment_configs'),
                    cases_to_retry=cases_to_retry
                )
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 不支持的任务类型: {TaskType.get_name(self.task_type)}")
                return False
                
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行重试性能测试失败: {str(e)}")
            return False

    async def _download_mapping_files(self, jenkins_build_result_url: str, app_type: int) -> bool:
        """下载并处理Android应用的mapping文件"""
        try:
            # 1. 准备文件名和路径
            file_prefix = AppType.get_prefix(app_type)
            result_file = os.path.join(self.resources_folder, f"{file_prefix}jenkins_build_result.json")
            
            # 2. 获取构建结果
            build_result = None
            if os.path.exists(result_file):
                try:
                    with open(result_file) as f:
                        build_result = json.load(f)
                except json.JSONDecodeError:
                    logger.error(f"[子任务 {self.sub_task_id}] 解析已存在的构建结果失败，将重新下载")
                
            if not build_result:
                if not await network_utils.download_file(
                    url=jenkins_build_result_url,
                    save_path=result_file
                ):
                    logger.error(f"[子任务 {self.sub_task_id}] 下载Jenkins构建结果失败")
                    return False
                
                try:
                    with open(result_file) as f:
                        build_result = json.load(f)
                except json.JSONDecodeError:
                    logger.error(f"[子任务 {self.sub_task_id}] 解析Jenkins构建结果失败")
                    return False
                
            # 3. 获取mapping文件URL
            mapping_files = {
                f'{file_prefix}mapping.txt': build_result.get('mapping_url'),
                f'{file_prefix}class_mapping.txt': build_result.get('extra_archive_file_map', {}).get(
                    'layout_view_class_mapping_txt'),
                f'{file_prefix}resource_mapping.txt': build_result.get('extra_archive_file_map', {}).get(
                    'resource_mapping_txt')
            }
            
            # 4. 下载所有mapping文件
            for file_name, url in mapping_files.items():
                if not url:
                    continue
                
                file_path = os.path.join(self.resources_folder, file_name)
                
                # 跳过已存在的文件
                if os.path.exists(file_path):
                    continue
                
                # 下载新文件
                if not await network_utils.download_file(url=url, save_path=file_path):
                    return False
            
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理mapping文件失败: {str(e)}")
            return False

    async def _update_app_group(self, platform: int, app_lists: Dict[str, List],
                             all_apps: List[Dict], processed_results: List[Any]) -> bool:
        """更新应用组信息
        
        Args:
            platform: 平台类型(Android/iOS)
            app_lists: 包含性能和辅助应用的字典
            all_apps: 所有待处理应用列表
            processed_results: 应用处理结果列表
            
        Returns:
            bool: 更新是否成功
        """
        try:
            perf_app_count = len(app_lists['perf_apps'])
            for i, (app, result) in enumerate(zip(all_apps, processed_results)):
                if not result:
                    continue

                if i < perf_app_count:
                    # 更新性能测试应用
                    perf_app_list_key = ('android_perf_app_id_list' if platform == PlatformType.ANDROID.value 
                                    else 'ios_perf_app_id_list')
                    self.app_group[perf_app_list_key][i] = result
                else:
                    # 更新辅助应用
                    assist_app_index = i - perf_app_count
                    assist_app_list_key = ('android_assist_app_id_list' if platform == PlatformType.ANDROID.value 
                                        else 'ios_assist_app_id_list')
                    self.app_group[assist_app_list_key][assist_app_index] = result
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 更新应用组信息失败: {str(e)}")
            return False
                
    async def _cleanup(self):
        """清理应用控制器"""
        return True


app_control = AppControl()