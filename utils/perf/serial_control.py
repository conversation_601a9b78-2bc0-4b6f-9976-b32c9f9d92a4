import sys
from pathlib import Path
import binascii
import json
import time
import serial
from typing import List, Optional
from pathlib import Path
from utils.common.log_utils import get_logger
from utils.common.android_device import android_device
from utils.common.ios_device import ios_device
from core.settings_manager import settings_manager
logger = get_logger(__name__)

# 定义数据包常量
RELAY_ON_PACKET = "A0 01 01 A2"
RELAY_OFF_PACKET = "A0 01 00 A1"


class SerialControl:
    """串口控制类，用于控制USB继电器开关"""

    def __init__(self):
        """初始化串口控制器"""
        try:
            # 从settings获取配置
            self.baud_rate = settings_manager.get('serial.baud_rate', 9600)
            self.timeout = settings_manager.get('serial.timeout', 0.5)
            self.retry_count = settings_manager.get('serial.retry.count', 3)
            self.retry_interval = settings_manager.get('serial.retry.interval', 5)
            
            # 如果配置中没有串口patterns,使用默认值
            default_patterns = {
                "linux": ["/dev/ttyCH341*"],
                "darwin": ["/dev/cu.wchusbserial*"]
            }
            self.device_patterns = settings_manager.get(f'serial.device.patterns.{sys.platform}', default_patterns.get(sys.platform, []))
            
            self.relay_on_packet = settings_manager.get('serial.relay.packets.on', 'A0 01 01 A2')
            self.relay_off_packet = settings_manager.get('serial.relay.packets.off', 'A0 01 00 A1')
            self.switch_delay = settings_manager.get('serial.relay.switch_delay', 1)
            
            # 获取项目根目录
            base_dir = Path(__file__).parent.parent.parent
            
            # 配置文件路径从paths配置获取，并使用绝对路径
            data_dir = base_dir / settings_manager.get('storage.paths.base.data', 'data')
            self._config_file = data_dir / settings_manager.get('storage.paths.files.serial', 'serial.json')
            
            # 确保数据目录存在
            data_dir.mkdir(exist_ok=True)
            
            self._udid_port_map = {}
            self._load_config()

        except Exception as e:
            logger.error(f"[串口] 初始化串口控制器失败: {str(e)}")
            raise

    def _load_config(self) -> None:
        """加载串口配置"""
        try:
            if not self._config_file.exists():
                logger.warning(f"[串口] 串口配置文件不存在: {self._config_file}")
                # 如果配置文件不存在，创建一个空的配置文件
                self._config_file.parent.mkdir(parents=True, exist_ok=True)
                with open(self._config_file, 'w', encoding='utf-8') as f:
                    json.dump({'serial_mappings': [], 'selected_screen': None}, f, indent=2, ensure_ascii=False)
                return
            
            with open(self._config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self._udid_port_map = {
                    item['udid']: item['port']
                    for item in config.get('serial_mappings', [])
                }
            logger.info(f"[串口] 已加载 {len(self._udid_port_map)} 个串口映射配置")
            
        except Exception as e:
            logger.error(f"[串口] 加载串口配置失败: {str(e)}")

    def get_all_serial(self) -> List[str]:
        """获取所有可用的串口设备"""
        logger.debug("查找可用串口设备")
        try:
            if not self.device_patterns:
                logger.warning(f"[串口] 不支持的操作系统平台: {sys.platform}")
                return []

            # 使用glob安全地获取串口列表
            import glob
            result = []
            for pattern in self.device_patterns:
                result.extend(glob.glob(pattern))

            if result:
                logger.info(f"[串口] 发现 {len(result)} 个串口设备")
            else:
                logger.warning("[串口] 未发现可用串口设备")

            return result

        except Exception as e:
            logger.error(f"[串口] 获取串口设备列表失败: {str(e)}")
            return []

    def connect_serial(self, serial_port: str, baud_rate: Optional[int] = None) -> Optional[serial.Serial]:
        """连接指定的串口设备"""
        logger.debug(f"尝试连接串口设备: {serial_port}，波特率: {baud_rate}")
        try:
            baud_rate = baud_rate or self.baud_rate

            if not isinstance(serial_port, str) or not serial_port:
                raise ValueError("serial_port必须是非空字符串")

            if not isinstance(baud_rate, int) or baud_rate <= 0:
                raise ValueError("baud_rate必须是正整数")

            ser = serial.Serial(serial_port, baud_rate, timeout=self.timeout)
            logger.info(f"[串口] 成功连接串口 {serial_port}, 波特率 {baud_rate}")
            return ser

        except serial.SerialException as e:
            logger.error(f"[串口] 串口连接错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"[串口] 连接串口失败: {str(e)}")
            return None

    def relay_switch(self, serial_port: str, switch_status: bool, baud_rate: Optional[int] = None) -> Optional[int]:
        """控制继电器开关状态"""
        logger.debug(f"控制继电器开关: {serial_port}，目标状态: {'开' if switch_status else '关'}，波特率: {baud_rate}")
        try:
            baud_rate = baud_rate or self.baud_rate

            ser = self.connect_serial(serial_port, baud_rate)
            if not ser:
                logger.error("[串口] 无法连接串口设备")
                return None

            try:
                with ser:
                    packet = self.relay_on_packet if switch_status else self.relay_off_packet
                    data_packet = bytes.fromhex(packet)
                    logger.info(f"[串口] 发送数据包: {data_packet.hex()}")

                    res = ser.write(data_packet)
                    logger.info(f"[串口] 数据写入成功，写入 {res} 字节")

                    if ser.in_waiting:
                        data = str(binascii.b2a_hex(ser.read(ser.in_waiting)))[2:-1]
                        logger.info(f"[串口] 串口 {serial_port} 返回数据: {data}")

                    time.sleep(self.switch_delay)  # 等待操作完成
                    return res

            except Exception as e:
                logger.error(f"[串口] 操作串口时发生错误: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"[串口] 控制继电器异常: {str(e)}")
            return None

serial_control = SerialControl()

if __name__ == '__main__':
        # 初始化串口控制
    serial_control = SerialControl()
    
    # 获取所有可用串口
    serial_ports = serial_control.get_all_serial()
    if not serial_ports:
        logger.warning("未找到任何可用串口设备")
        sys.exit(1)
        
    # 遍历并测试每个串口
    for port in serial_ports:
        serial_control.relay_switch(port, True)
