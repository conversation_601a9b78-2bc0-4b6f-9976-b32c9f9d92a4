'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 17:29:18
FilePath: /global_rtc_client/ui/dialogs/serial_config_dialog.py
Description: 
'''
import json
import os
import time
import asyncio
from PyQt6.QtCore import Qt, QTimer, QSize
from PyQt6.QtGui import QIcon, QPainter, QColor
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QFrame, QLabel, QApplication, QComboBox, QWidget
)

from ui.styles import (
    Colors,
    ButtonStyle,
    InputStyle,
    TableStyle,
    LabelStyle,
    StatusStyle,
    FrameStyle
)
from utils.common.log_utils import get_logger
from core.perf_manager import perf_manager
from utils.perf.serial_control import serial_control
from utils.common.android_device import android_device, android_device
from utils.common.ios_device import ios_device
from ui.components.msg_box import MsgBox

logger = get_logger(__name__)


class SerialDialog(QDialog):
    """串口配置对话框"""

    def __init__(self, parent=None):
        """初始化串口配置对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 从父窗口获取 perf_manager
        self.perf_manager = perf_manager
        self.serial_control = serial_control
        
        # 设置窗口标题和大小
        self.setWindowTitle("串口配置")
        self.resize(800, 600)
        
        # 初始化UI
        self.setup_ui()
        self.setup_connections()
        
        # 加载现有配置
        self.load_config()

    def setup_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(12)
        layout.setContentsMargins(32, 32, 32, 0)
        
        # 创建一个内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(16)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        # 表格容器卡片
        table_container = QFrame()
        table_container.setStyleSheet(FrameStyle.get_card_style())
        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.setSpacing(0)
        
        # 添加验证状态标签
        self.verify_status = QLabel()
        self.verify_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.verify_status.setStyleSheet(StatusStyle.get_verify_status_style())
        self.verify_status.hide()  # 初始隐藏
        table_layout.addWidget(self.verify_status)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["设备UDID", "串口号", "操作"])
        self.table.setStyleSheet(TableStyle.get_serial_dialog_table_style())

        # 基本设置
        self.table.setShowGrid(True)
        self.table.setFrameShape(QFrame.Shape.NoFrame)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # UDID列自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)    # 串口号列固定宽度
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)    # 操作列固定宽度
        self.table.setColumnWidth(1, 200)  # 增加串口号列宽
        self.table.setColumnWidth(2, 240)  # 进一步增加操作列宽度
        header.setDefaultAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
        header.setFixedHeight(48)

        # 设置单元格点击时的光标样式
        self.table.setCursor(Qt.CursorShape.IBeamCursor)

        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(60)
        
        # 设置表格最小高度
        self.table.setMinimumHeight(360)
        
        # 添加表格到容器
        table_layout.addWidget(self.table)
        content_layout.addWidget(table_container)
        
        # 添加内容容器到主布局
        layout.addWidget(content_widget)
        
        # 减小弹簧的影响
        layout.addSpacing(8)
        
        # 视频源选择区域
        screen_container = QFrame()
        screen_container.setStyleSheet(FrameStyle.get_screen_container_style())
        screen_layout = QHBoxLayout(screen_container)
        screen_layout.setContentsMargins(16, 8, 16, 8)
        screen_layout.setSpacing(16)
        
        screen_label = QLabel("视频源屏幕选择")
        screen_label.setStyleSheet(LabelStyle.get_screen_label_style())
        screen_label.setFixedHeight(36)  # 与下拉框高度一致
        
        self.screen_combo = QComboBox()
        self.screen_combo.setStyleSheet(InputStyle.get_combo_box_style())
        
        # 获取并添加屏幕列表
        screens = self.perf_manager.get_screen_list()
        for screen in screens:
            self.screen_combo.addItem(screen['display_text'], userData=screen)
        
        screen_layout.addWidget(screen_label)
        screen_layout.addWidget(self.screen_combo)
        screen_layout.addStretch()
        
        # 将视频源选择添加到主布局（在按钮区域之前）
        layout.addWidget(screen_container)
        
        # 按钮区域
        button_container = QFrame()
        button_container.setFixedHeight(64)  # 减小容器高度
        button_container.setStyleSheet(FrameStyle.get_button_container_style())
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(24, 12, 24, 12)  # 减小边距
        button_layout.setSpacing(12)  # 减小按钮间距

        # 添加按钮
        self.add_btn = QPushButton("新增")
        icon = QIcon("ui/icons/actions/add.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.add_btn.setIcon(QIcon(pixmap))
        self.add_btn.setIconSize(QSize(16, 16))
        self.add_btn.setStyleSheet(ButtonStyle.get_serial_add_button_style())
        self.add_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.add_btn.clicked.connect(self.add_row)

        self.delete_btn = QPushButton("删除")
        icon = QIcon("ui/icons/actions/delete.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.delete_btn.setIcon(QIcon(pixmap))
        self.delete_btn.setIconSize(QSize(16, 16))
        self.delete_btn.setStyleSheet(ButtonStyle.get_serial_delete_button_style())
        self.delete_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.delete_btn.clicked.connect(self.delete_row)

        self.save_btn = QPushButton("保存")
        icon = QIcon("ui/icons/actions/save.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.save_btn.setIcon(QIcon(pixmap))
        self.save_btn.setIconSize(QSize(16, 16))
        self.save_btn.setStyleSheet(ButtonStyle.get_serial_save_button_style())
        self.save_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.save_btn.clicked.connect(self._handle_save_click)

        # 添加全部打开和全部关闭按钮
        self.open_all_btn = QPushButton("全部打开")
        icon = QIcon("ui/icons/actions/start.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.open_all_btn.setIcon(QIcon(pixmap))
        self.open_all_btn.setIconSize(QSize(16, 16))
        self.open_all_btn.setStyleSheet(ButtonStyle.get_serial_add_button_style())
        self.open_all_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.open_all_btn.clicked.connect(self._handle_open_all_ports)
        self.open_all_btn.setMinimumWidth(90)
        self.open_all_btn.setMinimumHeight(36)

        self.close_all_btn = QPushButton("全部关闭")
        icon = QIcon("ui/icons/actions/stop.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.close_all_btn.setIcon(QIcon(pixmap))
        self.close_all_btn.setIconSize(QSize(16, 16))
        self.close_all_btn.setStyleSheet(ButtonStyle.get_serial_delete_button_style())
        self.close_all_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.close_all_btn.clicked.connect(self._handle_close_all_ports)
        self.close_all_btn.setMinimumWidth(90)
        self.close_all_btn.setMinimumHeight(36)

        # 设置按钮大小
        for btn in [self.add_btn, self.delete_btn]:
            btn.setMinimumWidth(90)  # 次要按钮宽度稍小
            btn.setMinimumHeight(36)
        
        self.save_btn.setMinimumWidth(100)  # 主要按钮宽度稍大
        self.save_btn.setMinimumHeight(36)

        # 添加按钮到布局
        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.open_all_btn)
        button_layout.addWidget(self.close_all_btn)
        button_layout.addStretch()  # 添加弹性空间，将保存按钮推到右侧
        button_layout.addWidget(self.save_btn)

        # 将按钮容器添加到主布局
        layout.addWidget(button_container)

    def setup_connections(self):
        """设置连接"""
        # 设置定时器，用于加载配置
        QTimer.singleShot(0, self.load_config)

        # 在初始化时连接信号
        self.screen_combo.currentIndexChanged.connect(self._on_screen_changed)

    def load_config(self):
        """加载配置"""
        try:
            config = {'serial_mappings': [], 'selected_screen': None}
            if os.path.exists(self.perf_manager._serial_config_file):
                with open(self.perf_manager._serial_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 加载串口映射到表格
            mappings = config.get('serial_mappings', [])
            self.table.setRowCount(len(mappings))
            for i, item in enumerate(mappings):
                udid_item = QTableWidgetItem(item.get("udid", ""))
                port_item = QTableWidgetItem(item.get("port", ""))

                # 设置单元格样式
                for table_item in [udid_item, port_item]:
                    table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                self.table.setItem(i, 0, udid_item)
                self.table.setItem(i, 1, port_item)
                
                # 添加操作按钮
                self._add_control_buttons(i)

            # 如果有已选择的屏幕，设置下拉框选项
            if config.get('selected_screen'):
                selected_screen = config['selected_screen']
                # 查找匹配的屏幕并设置为当前选项
                for i in range(self.screen_combo.count()):
                    screen_data = self.screen_combo.itemData(i)
                    if screen_data and screen_data['name'] == selected_screen['name']:
                        self.screen_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            logger.error(f"加载串口配置失败: {str(e)}")
            self._show_error("加载配置失败", str(e))

    def add_row(self):
        """添加行"""
        row_count = self.table.rowCount()
        self.table.insertRow(row_count)
        
        # 设置前两列的单元格
        for col in range(2):
            item = QTableWidgetItem("")
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row_count, col, item)
        
        # 添加操作按钮
        self._add_control_buttons(row_count)

    def _add_control_buttons(self, row):
        """为表格行添加控制按钮"""
        # 创建按钮容器
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(8, 4, 8, 4)
        button_layout.setSpacing(8)

        # 打开按钮
        open_btn = QPushButton("打开")
        icon = QIcon("ui/icons/actions/start.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        open_btn.setIcon(QIcon(pixmap))
        open_btn.setIconSize(QSize(16, 16))
        open_btn.setStyleSheet(ButtonStyle.get_port_control_button_style("open"))
        open_btn.setFixedWidth(80)
        open_btn.clicked.connect(lambda: self._handle_open_port(row))

        # 关闭按钮
        close_btn = QPushButton("关闭")
        icon = QIcon("ui/icons/actions/stop.svg")
        pixmap = icon.pixmap(20, 20)
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        close_btn.setIcon(QIcon(pixmap))
        close_btn.setIconSize(QSize(16, 16))
        close_btn.setStyleSheet(ButtonStyle.get_port_control_button_style("close"))
        close_btn.setFixedWidth(80)
        close_btn.clicked.connect(lambda: self._handle_close_port(row))

        # 添加按钮到布局
        button_layout.addWidget(open_btn)
        button_layout.addWidget(close_btn)
        button_layout.addStretch()

        # 将按钮容器添加到表格
        self.table.setCellWidget(row, 2, button_container)

    def _handle_open_port(self, row):
        """处理打开串口
        
        Args:
            row: 行号
        """
        try:
            port = self.table.item(row, 1).text().strip()
            if not port:
                self._show_warning("请先设置串口号")
                return
            
            if self.serial_control.relay_switch(port, True):
                self._show_success(f"串口 {port} 打开成功")
            else:
                self._show_error("操作失败", f"串口 {port} 打开失败")
            
        except Exception as e:
            logger.error(f"打开串口失败: {str(e)}")
            self._show_error("操作失败", str(e))

    def _handle_close_port(self, row):
        """处理关闭串口
        
        Args:
            row: 行号
        """
        try:
            port = self.table.item(row, 1).text().strip()
            if not port:
                self._show_warning("请先设置串口号")
                return
            
            if self.serial_control.relay_switch(port, False):
                self._show_success(f"串口 {port} 关闭成功")
            else:
                self._show_error("操作失败", f"串口 {port} 关闭失败")
            
        except Exception as e:
            logger.error(f"关闭串口失败: {str(e)}")
            self._show_error("操作失败", str(e))

    def _show_success(self, message: str):
        """显示成功提示"""
        self.verify_status.setText(message)
        self.verify_status.setStyleSheet(StatusStyle.get_verify_status_style("success"))
        self.verify_status.show()
        
        # 3秒后自动隐藏
        QTimer.singleShot(3000, self.verify_status.hide)

    def _show_error(self, title: str, message: str):
        """显示错误提示"""
        dialog = MsgBox(title, message, type="error", parent=self)
        dialog.exec()

    def _show_warning(self, message: str):
        """显示警告提示"""
        dialog = MsgBox("警告", message, type="warning", parent=self)
        dialog.exec()

    def delete_row(self):
        """删除选中的行"""
        selected_rows = set(item.row() for item in self.table.selectedItems())
        if not selected_rows:
            self._show_warning("请先选择要删除的行")
            return

        dialog = MsgBox(
            "确认删除",
            f"确定要删除选中的 {len(selected_rows)} 行吗？",
            type="warning",
            parent=self,
            show_cancel=True
        )
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            for row in sorted(selected_rows, reverse=True):
                self.table.removeRow(row)

    async def _handle_save(self):
        """处理保存操作"""
        try:
            # 1. 收集配置
            config = {
                'serial_mappings': [],
                'selected_screen': None
            }
            
            # 收集串口映射
            for row in range(self.table.rowCount()):
                udid = self.table.item(row, 0)
                port = self.table.item(row, 1)
                if udid and port and udid.text().strip() and port.text().strip():
                    config['serial_mappings'].append({
                        "udid": udid.text().strip(),
                        "port": port.text().strip()
                    })

            # 收集选中的屏幕信息
            selected_screen = self.screen_combo.currentData()
            if selected_screen:
                # 保留原始的屏幕几何信息
                config['selected_screen'] = {
                    'name': selected_screen['name'],
                    'display_text': selected_screen['display_text'],
                    'geometry': {
                        'x': selected_screen['geometry']['x'],  # 保留原始x坐标
                        'y': selected_screen['geometry']['y'],  # 保留原始y坐标
                        'width': selected_screen['geometry']['width'],
                        'height': selected_screen['geometry']['height']
                    }
                }

            # 2. 基础验证
            if not config['serial_mappings'] and not selected_screen:
                self._show_warning("请先添加串口配置或选择视频源")
                return

            # 检查重复项
            udids = [item["udid"] for item in config['serial_mappings']]
            ports = [item["port"] for item in config['serial_mappings']]
            if len(set(udids)) != len(udids):
                self._show_warning("存在重复的设备UDID")
                return
            if len(set(ports)) != len(ports):
                self._show_warning("存在重复的串口号")
                return

            # 3. 保存配置
            if await self.perf_manager.save_serial_config(config):
                self.accept()  # 关闭对话框
            else:
                self._show_error("保存失败", "保存配置失败")

        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            self._show_error("保存失败", str(e))

    def _handle_save_click(self):
        """处理保存按钮点击"""
        import asyncio
        loop = asyncio.get_event_loop()
        if loop and not loop.is_closed():
            loop.create_task(self._handle_save())

    def _on_screen_changed(self, index):
        """处理屏幕选择变化"""
        selected_screen = self.screen_combo.currentData()
        if selected_screen:
            # 处理屏幕选择变化
            self.perf_manager.set_selected_screen(selected_screen)

    def _handle_open_all_ports(self):
        """处理全部打开串口"""
        import asyncio
        loop = asyncio.get_event_loop()
        if loop and not loop.is_closed():
            loop.create_task(self._open_all_ports_async())

    def _handle_close_all_ports(self):
        """处理全部关闭串口"""
        import asyncio
        loop = asyncio.get_event_loop()
        if loop and not loop.is_closed():
            loop.create_task(self._close_all_ports_async())

    async def _open_all_ports_async(self):
        """异步并行处理全部打开串口"""
        success_count = 0
        error_ports = []
        tasks = []
        ports = []

        # 收集所有需要操作的串口
        for row in range(self.table.rowCount()):
            port_item = self.table.item(row, 1)
            if port_item and port_item.text().strip():
                port = port_item.text().strip()
                ports.append(port)
                tasks.append(self._switch_port_async(port, True))

        if not tasks:
            self._show_warning("没有可用的串口配置")
            return

        # 并行执行所有串口操作
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for port, result in zip(ports, results):
            if isinstance(result, Exception):
                logger.error(f"打开串口失败: {str(result)}")
                error_ports.append(port)
            elif result:
                success_count += 1
            else:
                error_ports.append(port)

        # 显示操作结果
        if success_count > 0:
            self._show_success(f"成功打开 {success_count} 个串口")
        
        if error_ports:
            self._show_error("部分操作失败", f"以下串口打开失败：{', '.join(error_ports)}")

    async def _close_all_ports_async(self):
        """异步并行处理全部关闭串口"""
        success_count = 0
        error_ports = []
        tasks = []
        ports = []

        # 收集所有需要操作的串口
        for row in range(self.table.rowCount()):
            port_item = self.table.item(row, 1)
            if port_item and port_item.text().strip():
                port = port_item.text().strip()
                ports.append(port)
                tasks.append(self._switch_port_async(port, False))

        if not tasks:
            self._show_warning("没有可用的串口配置")
            return

        # 并行执行所有串口操作
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for port, result in zip(ports, results):
            if isinstance(result, Exception):
                logger.error(f"关闭串口失败: {str(result)}")
                error_ports.append(port)
            elif result:
                success_count += 1
            else:
                error_ports.append(port)

        # 显示操作结果
        if success_count > 0:
            self._show_success(f"成功关闭 {success_count} 个串口")
        
        if error_ports:
            self._show_error("部分操作失败", f"以下串口关闭失败：{', '.join(error_ports)}")

    async def _switch_port_async(self, port: str, state: bool) -> bool:
        """异步切换串口状态
        
        Args:
            port: 串口号
            state: True表示打开，False表示关闭
            
        Returns:
            bool: 操作是否成功
        """
        try:
            return self.serial_control.relay_switch(port, state)
        except Exception as e:
            logger.error(f"串口操作失败: {str(e)}")
            raise
