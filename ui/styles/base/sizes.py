"""尺寸常量定义"""

class Sizes:
    """尺寸规格类"""
    
    # 按钮尺寸
    class Button:
        """按钮尺寸规格"""
        # 极小按钮 (用于最紧凑型操作按钮)
        EXTRA_SMALL = {
            "min_width": 40,
            "min_height": 16,
            "padding": "1px 4px",
            "font_size": 10,
            "border_radius": 1
        }

        # 超小按钮 (用于紧凑型操作按钮)
        TINY = {
            "min_width": 60,
            "min_height": 20,
            "padding": "2px 8px",
            "font_size": 12,
            "border_radius": 2
        }

        # 小型按钮 (用于紧凑型操作按钮)
        SMALL = {
            "min_width": 80,
            "min_height": 32,
            "padding": "4px 16px",
            "font_size": 13,
            "border_radius": 4
        }
        
        # 中型按钮 (默认标准按钮)
        MEDIUM = {
            "min_width": 100,
            "min_height": 36,
            "padding": "8px 16px",
            "font_size": 14,
            "border_radius": 6
        }
        
        # 大型按钮 (用于主要操作按钮)
        LARGE = {
            "min_width": 120,
            "min_height": 40,
            "padding": "12px 24px",
            "font_size": 14,
            "border_radius": 8
        }
        
        # 超大按钮 (用于强调性操作按钮)
        XLARGE = {
            "min_width": 200,
            "min_height": 48,
            "padding": "12px 24px",
            "font_size": 14,
            "border_radius": 8
        }

    # 图标尺寸
    class Icon:
        """图标尺寸规格"""
        SMALL = {
            "width": 14,
            "height": 14,
            "margin_right": 6
        }
        
        MEDIUM = {
            "width": 16,
            "height": 16,
            "margin_right": 8
        }
        
        LARGE = {
            "width": 20,
            "height": 20,
            "margin_right": 8
        }

    # 头像尺寸
    class Avatar:
        """头像尺寸规格"""
        SMALL = 32
        MEDIUM = 36
        LARGE = 40
        XLARGE = 48

    # 边框圆角
    class BorderRadius:
        """边框圆角规格"""
        SMALL = 4
        MEDIUM = 6
        LARGE = 8
        XLARGE = 12
        CIRCLE = "50%" 