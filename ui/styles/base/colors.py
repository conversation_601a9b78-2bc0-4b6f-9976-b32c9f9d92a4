'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-11 13:16:06
FilePath: /global_rtc_client/ui/styles/base/colors.py
Description: 
'''
"""颜色常量定义"""

class Colors:
    # 主色调 - 飞书风格
    PRIMARY = "#2D8CFF"  # 飞书主色
    PRIMARY_LIGHT = "#E8F3FF"  # 浅蓝色背景
    PRIMARY_DARK = "#2B65FF"
    PRIMARY_HOVER = "#579DFF"
    PRIMARY_PRESSED = "#206CCF"
    PRIMARY_DISABLED = "#A6D1FF"
    PRIMARY_ACTIVE = "#1755DB"
    
    # 按钮状态色 - 飞书风格
    BUTTON_HOVER = "#EBF5FF"  # 按钮悬停态 - 更淡的蓝色
    BUTTON_ACTIVE = "#E1EFFF"  # 按钮激活态 - 中等蓝色
    BUTTON_PRESSED = "#D5E7FF"  # 按钮按压态 - 稍深蓝色

    # 文本颜色
    TEXT_PRIMARY = "#1F2937"
    TEXT_SECONDARY = "#4E5969"
    TEXT_DISABLED = "#C9CDD4"
    TEXT_INVERSE = "#FFFFFF"
    TEXT_LINK = "#1677FF"
    TEXT_LINK_HOVER = "#4096FF"
    TEXT_LINK_ACTIVE = "#0958D9"
    TEXT_SUCCESS = "#52C41A"
    TEXT_WARNING = "#FAAD14"
    TEXT_ERROR = "#FF4D4F"

    # 背景色
    BACKGROUND = "#FFFFFF"
    BACKGROUND_LIGHT = "#F5F6F7"
    BACKGROUND_DARK = "#1F2329"
    SURFACE = "#FFFFFF"
    SURFACE_LIGHT = "#F3F4F6"
    SURFACE_DARK = "#E5E7EB"

    # 边框色
    BORDER = "#E5E6E7"
    BORDER_LIGHT = "#F0F0F0"
    BORDER_DARK = "#D9D9D9"
    BORDER_FOCUS = "#2D8CFF"
    BORDER_ERROR = "#FF4D4F"
    BORDER_WARNING = "#FAAD14"
    BORDER_SUCCESS = "#52C41A"

    # 状态色
    SUCCESS = "#52C41A"
    SUCCESS_LIGHT = "#4ADE80"
    SUCCESS_DARK = "#16A34A"
    SUCCESS_BG = "#F6FFED"
    SUCCESS_BORDER = "#B7EB8F"
    
    WARNING = "#FAAD14"
    WARNING_LIGHT = "#FBBF24"
    WARNING_DARK = "#D97706"
    WARNING_BG = "#FFFBE6"
    WARNING_BORDER = "#FFE58F"
    
    ERROR = "#FF4D4F"
    ERROR_LIGHT = "#F87171"
    ERROR_DARK = "#DC2626"
    ERROR_BG = "#FFF2F0"
    ERROR_BORDER = "#FFCCC7"
    
    INFO = "#1677FF"
    INFO_LIGHT = "#60A5FA"
    INFO_DARK = "#2563EB"
    INFO_BG = "#E6F4FF"
    INFO_BORDER = "#91CAFF"

    # 分割线
    DIVIDER = "#E5E6E7"
    DIVIDER_DARK = "#D1D5DB"

    # 遮罩
    MASK = "rgba(0, 0, 0, 0.45)"
    MASK_LIGHT = "rgba(0, 0, 0, 0.2)"
    MASK_DARK = "rgba(0, 0, 0, 0.65)"

    # 特殊状态
    HOVER = "#F5F6F7"
    SELECTED = "#E8F3FF"
    DISABLED = "#F7F8FA"
    FOCUSED = "#E1EFFF"

    # 链接
    LINK = "#1677FF"
    LINK_HOVER = "#4096FF"
    LINK_ACTIVE = "#0958D9"
    LINK_VISITED = "#722ED1"

    # 阴影
    SHADOW = "rgba(0, 0, 0, 0.15)"
    SHADOW_LIGHT = "rgba(0, 0, 0, 0.06)"
    SHADOW_DARK = "rgba(0, 0, 0, 0.3)"
    
    # 透明度
    OPACITY_DISABLED = 0.7
    OPACITY_HOVER = 0.8
    OPACITY_ACTIVE = 1.0 