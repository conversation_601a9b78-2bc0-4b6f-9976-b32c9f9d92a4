{"api": {"default_timeout": 30, "retry_count": 3, "retry_interval": 5, "current_env": "prod", "auth": {"api_key": "830993ebe0a3bfd9506e035c8e2aee6b", "jwt_url": "https://cloud.bytedance.net/auth/api/v1/jwt", "user_url": "https://cloud-page.bytedance.net/component/api/v1/user"}, "gstest": {"local": "http://[::]:8000/api", "boe": "http://gstest-api-boe.bytedance.net/api", "prod": "http://gstest-api.bytedance.net/api"}}, "storage": {"tos": {"access_key": "EX4GVMDFW7W20J5GC645", "secret_key": "B9ZYDkJUSffYzvRonS+xh3R96qLZh6mo/DYS6n1V", "endpoint": "tos-cn-north.byted.org", "bucket": "global-rtc-test-platform", "timeout": 60, "connect_timeout": 60, "connection_pool_size": 10}, "paths": {"base": {"tasks": ".tasks", "log": "logs", "data": "data", "repo": "repos", "config": "config"}, "files": {"config": "config.json", "auth": "auth.json", "task": "task.json", "serial": "serial.json"}, "task": {"resources": "resources", "results": "results"}}}, "poll_interval": {"device": 20, "task": 5, "request": 5}, "repo": {"app_url": "******************:bytertc_i18n/global_rtc_client.git", "perf_case_url": "https://yinyanting.2022:<EMAIL>/bytertc_i18n/global_business_perf.git"}, "device": {"adb_path": "", "bdc_path": "", "power": {"check_interval": 180, "max_wait_time": 1800}}, "app": {"download": {"chunk_size": 8192, "timeout": 1800, "retry": {"count": 3, "interval": 5}}, "install": {"timeout": 600, "retry": {"count": 3, "interval": 5}}, "repack": {"timeout": 300, "retry": {"count": 3, "interval": 5}}}, "case": {"run": {"timeout": 3600, "retry": {"count": 3, "interval": 5}}, "battery": {"check_interval": 180, "check_retries": 10}}, "serial": {"baud_rate": 9600, "timeout": 0.5, "retry": {"count": 3, "interval": 5}, "device": {"patterns": {"linux": ["/dev/ttyCH341*"], "darwin": ["/dev/cu.wchusbserial*"]}}, "relay": {"packets": {"on": "A0 01 01 A2", "off": "A0 01 00 A1"}, "switch_delay": 1}}}