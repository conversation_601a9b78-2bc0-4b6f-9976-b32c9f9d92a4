"""控制任务的执行流程,包括资源分配、应用安装、用例执行等"""
import os
import asyncio
from typing import Dict
from datetime import datetime
from apis.perf_api import perf_api
from config.constants import TaskStatus, AppInstallMode, ErrorCode, TaskType, PerfToolType
from utils.common.log_utils import get_logger
from utils.perf.app_control import app_control
from utils.perf.case_control import case_control
from utils.perf.task_helper import task_helper
from utils.common.network_utils import network_utils
from core.settings_manager import settings_manager

logger = get_logger(__name__)


class TaskControl:
    """控制任务的执行流程,包括资源分配、应用安装、用例执行等"""

    def __init__(self):
        """初始化任务控制器"""
        # 初始化任务信息
        self.tasks_base_dir = settings_manager.get('storage.paths.base.tasks', '.tasks')
        self.task_folder = None
        self.sub_task_folder = None
        self.sub_task_dirs = {}

        # 初始化子任务信息
        self.sub_task_context = None
        self.sub_task_id = None

        # 初始化资源信息
        self.occupied_resources = {'devices': [], 'accounts': []}

        # 控制器实例
        self.app_control = app_control
        self.case_control = case_control
        self.task_helper = task_helper
        self.network_utils = network_utils
        
        # group属性初始化
        self.case_group = None
        self.app_group = None
        self.device_group = None
        self.account_group = None
        self.config_group = None
        self.experiment_configs = None
        
        # 错误处理
        self.error_code = None
        self.error_detail = None
        
        # 取消和锁机制
        self._cancel_event = asyncio.Event()
        self._termination_lock = asyncio.Lock()
        self._cleanup_lock = asyncio.Lock()
        self._termination_timeout = 30  # 终止操作超时时间(秒)
        self._cleanup_timeout = 10  # 清理操作超时时间(秒)
        self._cleanup_completed = False  # 清理完成标志
        
    def _reset_cancel_state(self):
        """重置取消状态"""
        self._cancel_event.clear()
        
    async def _check_cancellation(self):
        """检查是否已取消"""
        return self._cancel_event.is_set()
        
    async def _propagate_cancel_event(self):
        """将取消事件传播到所有控制器"""
        # 同步取消事件
        controllers = [
            self.app_control,
            self.case_control,
            self.task_helper
        ]
        
        if self._cancel_event.is_set():
            # 如果主控制器是取消状态，传播取消事件
            for controller in controllers:
                if hasattr(controller, '_cancel_event'):
                    controller._cancel_event.set()
                if hasattr(controller, '_is_cancelled'):
                    controller._is_cancelled = True
        else:
            # 如果主控制器不是取消状态，重置所有控制器的取消状态
            for controller in controllers:
                if hasattr(controller, 'reset_cancel_state'):
                    controller.reset_cancel_state()
                elif hasattr(controller, '_cancel_event'):
                    controller._cancel_event.clear()
                if hasattr(controller, '_is_cancelled'):
                    controller._is_cancelled = False

    def _init_controllers(self):
        logger.debug(f"同步控制器属性到各子模块，包含: {list(self.__dict__.keys())}")
        """初始化控制器，同步所有控制器的属性"""
        # 定义需要同步的属性和对应的值
        attrs = {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'client_id': self.client_id,
            'business_id': self.business_id,
            'sub_task_id': self.sub_task_id,
            'sub_task_context': self.sub_task_context,
            'task_detail': self.task_detail,
            'sub_task_detail': self.sub_task_detail,
            'case_group': self.case_group,
            'app_group': self.app_group,
            'device_group': self.device_group,
            'account_group': self.account_group,
            'config_group': self.config_group,
            'error_code': self.error_code,
            'error_detail': self.error_detail
        }
        
        controllers = [
            self.app_control,
            self.case_control, 
            self.task_helper
        ]
        
        # 使用列表推导式同步每个控制器的属性
        [setattr(controller, attr, value) 
         for controller in controllers
         for attr, value in attrs.items() 
         if hasattr(controller, attr)]

    async def start_sub_task(self) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 开始执行子任务流程")
        """开始执行子任务"""
        # 重置清理完成标志
        self._cleanup_completed = False
        sub_task_status = TaskStatus.FAILED.value
        try:
            if not await self.task_init():
                logger.error(f"[子任务 {self.sub_task_id}] 初始化任务环境失败")
                return False

            # 执行子任务
            self.sub_task_folder = os.path.join(self.task_folder, str(self.sub_task_id))
            if not await self._start_task_exec():
                sub_task_status = TaskStatus.FAILED.value
            else:
                sub_task_status = TaskStatus.SUCCESS.value
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行失败: {str(e)}")
            return False
        finally:
            await self._cleanup_sub_task(sub_task_status)

    async def retry_sub_task(self) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 开始重试子任务流程")
        """重试子任务"""
        # 重置清理完成标志
        self._cleanup_completed = False
        sub_task_status = TaskStatus.FAILED.value
        try:
            if not await self.task_init():
                logger.error(f"[子任务 {self.sub_task_id}] 初始化任务环境失败")
                return False

            # 获取子任务用例执行统计信息
            stats = await perf_api.get_case_execution_stats(self.sub_task_id)
            if not stats:
                logger.error(f"[子任务 {self.sub_task_id}] 获取用例执行统计信息失败")
                return False

            # 执行重试
            self.sub_task_folder = os.path.join(self.task_folder, str(self.sub_task_id))
            if not await self._retry_task_exec(stats):
                sub_task_status = TaskStatus.FAILED.value
            else:
                sub_task_status = TaskStatus.SUCCESS.value
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 重试失败: {str(e)}")
            return False
        finally:
            await self._cleanup_sub_task(sub_task_status)

    async def task_init(self) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 初始化任务环境参数")
        """初始化任务环境
        
        Args:
            sub_task: 子任务信息
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 重置取消状态
            self._reset_cancel_state()
            # 传播取消事件
            await self._propagate_cancel_event()
            
            self.sub_task_id = self.sub_task_context.get('id')
            self.sub_task_detail = None
            self.task_id = self.sub_task_context.get('task_id')
            self.task_detail = await perf_api.get_task_detail_by_id(self.task_id)
            self.client_id = self.task_detail.get('client_id')
            self.business_id = self.task_detail.get('business_id')
            self.task_type = self.task_detail.get('type')
            logger.info(f"[子任务 {self.sub_task_id}] 开始初始化任务环境")
            
            # 清除队头子任务
            await perf_api.remove_head_sub_task(self.client_id)
            
            self.task_folder = os.path.join(
                os.getcwd(),
                self.tasks_base_dir,
                str(self.task_id)
            )
            os.makedirs(self.task_folder, exist_ok=True)
            
            self._init_controllers()
                
            # 1. 初始化用例仓库
            if not await self.task_helper._init_case_repo(self.task_helper.perf_case_repo_url, self.task_helper.perf_case_repo_path):
                self.error_code = ErrorCode.REPO_INIT_FAILED.value
                self.error_detail = f"初始化用例仓库失败: {self.task_helper.perf_case_repo_url}"
                return False

            # 2. 获取应用组
            self.app_group = await self.task_helper.get_app_group()
            if not self.app_group:
                self.error_code = ErrorCode.APP_GROUP_BUILD_FAILED.value
                self.error_detail = "获取应用组失败"
                return False
            self._init_controllers()

            # 3. 获取用例组
            self.case_group = await self.task_helper.get_case_group()
            if not self.case_group:
                self.error_code = ErrorCode.CASE_GROUP_BUILD_FAILED.value
                self.error_detail = "获取用例组失败"
                return False
            self._init_controllers()

            # 4. 获取设备组
            self.device_group = await self.task_helper.get_device_group()
            if not self.device_group:
                self.error_code = ErrorCode.DEVICE_GROUP_BUILD_FAILED.value
                self.error_detail = "获取设备组失败"
                return False
            self._init_controllers()

            # 5. 获取账号组
            self.account_group = await self.task_helper.get_account_group(self.device_group)
            if not self.account_group:
                self.error_code = ErrorCode.ACCOUNT_GROUP_BUILD_FAILED.value
                self.error_detail = "获取账号组失败"
                return False
            self._init_controllers()

            # 6. 获取配置组信息
            config_id = self.task_detail.get('config_id')
            if config_id:
                self.config_group = await self.task_helper.get_config_group(config_id)
                if not self.config_group:
                    self.error_code = ErrorCode.CONFIG_GROUP_BUILD_FAILED.value
                    self.error_detail = "获取配置组信息失败" 
                    return False
            self._init_controllers()

            # 7. 获取实验配置
            if self.task_type == TaskType.LIBRA_EXPERIMENT:
                self.experiment_configs = await self.task_helper.get_experiment_configs(self.task_detail.get('experiment_id'))
                if not self.experiment_configs:
                    self.error_code = ErrorCode.EXPERIMENT_CONFIGS_BUILD_FAILED.value
                    self.error_detail = "获取实验配置失败"
                    return False
            self._init_controllers()

            # 8. 构建最终的子任务详情
            self.sub_task_detail = self.sub_task_context.copy()
            self.sub_task_detail.update({
                'task_info': {k: v for k, v in self.task_detail.items() if k != "sub_tasks"},
                'device_group': self.device_group,
                'account_group': self.account_group,
                'app_group': self.app_group,
                'case_group': self.case_group,
                'config_group': self.config_group or {},
                'experiment_configs': self.experiment_configs or {}
            })
            self.platform = self.sub_task_detail.get('platform')
            self.install_mode = self.sub_task_detail.get('app_install_type', AppInstallMode.NOT_INSTALL.value)
            self._init_controllers()
            
            # 7. 检查性能设备电量
            logger.info(f"[子任务 {self.sub_task_id}] 开始检查性能设备电量")
            if not await self.task_helper._check_battery_level():
                self.error_code = ErrorCode.DEVICE_BATTERY_LOW.value
                self.error_detail = f"性能设备电量不足"
                return False

            # 8. 设备初始化
            logger.info(f"[子任务 {self.sub_task_id}] 开始初始化设备")
            if not await self.task_helper._devices_init():
                self.error_code = ErrorCode.DEVICE_INIT_FAILED.value
                self.error_detail = f"设备初始化失败"
                return False

            # 9. 占用资源
            logger.info(f"[子任务 {self.sub_task_id}] 开始占用资源")
            if not await self.task_helper._occupy_resources():
                self.error_code = ErrorCode.RESOURCE_OCCUPY_FAILED.value
                self.error_detail = f"占用资源失败"
                return False

            # 10. 更新任务状态
            if not await self._update_task_start_status(TaskStatus.RUNNING.value):
                self.error_code = ErrorCode.TASK_STATUS_UPDATE_FAILED.value
                self.error_detail = f"更新任务状态失败"
                return False

            logger.info(f"[子任务 {self.sub_task_id}] 任务环境初始化完成")
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 初始化任务环境失败: {str(e)}")
            return False

    async def _execute_task_common(self) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 执行任务通用步骤")
        """执行任务的通用步骤"""
        try:
            # 1. 创建任务目录结构
            self.sub_task_dirs = await self.task_helper.create_sub_task_directories(self.sub_task_folder)
            if not self.sub_task_dirs:
                self.error_code = ErrorCode.TASK_DIR_CREATE_FAILED.value
                self.error_detail = "创建任务目录失败"
                return False

            # 2. 准备任务配置
            logger.info(f"[子任务 {self.sub_task_id}] 开始准备任务配置")
            if not await self.task_helper.copy_sub_task_configuration(self.sub_task_folder):
                self.error_code = ErrorCode.TASK_CONFIG_COPY_FAILED.value
                self.error_detail = "复制子任务配置失败"
                return False

            # 2.5. 创建性能数据目录
            logger.info(f"[子任务 {self.sub_task_id}] 开始创建性能数据目录")
            # 从任务详情中获取性能工具类型
            task_info = self.sub_task_detail.get('task_info', {})
            tool_type = task_info.get('perf_tool_type', PerfToolType.DS)
            perf_data_dir = self.task_helper.create_perf_data_directory(tool_type)
            if not perf_data_dir:
                self.error_code = ErrorCode.TASK_DIR_CREATE_FAILED.value
                self.error_detail = "创建性能数据目录失败"
                return False

            # 3. 播放视频资源（如果配置了视频URL）
            video_url = self.sub_task_detail.get('video_url')
            if video_url:
                logger.info(f"[子任务 {self.sub_task_id}] 开始准备视频资源")
                video_path = await self.task_helper.download_sub_task_videos(self.sub_task_dirs, video_url)
                if not video_path:
                    self.error_code = ErrorCode.VIDEO_DOWNLOAD_FAILED.value
                    self.error_detail = "下载视频文件失败"
                    return False

                if not await self.task_helper.play_test_video(video_path):
                    self.error_code = ErrorCode.VIDEO_PLAY_FAILED.value
                    self.error_detail = "播放视频文件失败"
                    return False
            else:
                logger.info(f"[子任务 {self.sub_task_id}] 未配置视频URL，跳过视频相关步骤")

            # 4. 处理应用
            logger.info(f"[子任务 {self.sub_task_id}] 开始处理应用")
            if not await self.app_control.process_apps(self.install_mode, self.sub_task_dirs['resources']):
                self.error_code = ErrorCode.APP_OPERATION_FAILED.value
                self.error_detail = "处理应用失败"
                return False
            
            # 5. 安装辅助应用
            logger.info(f"[子任务 {self.sub_task_id}] 开始处理辅助应用")
            if not await self.app_control._handle_assist_apps(self.install_mode):
                self.error_code = ErrorCode.APP_ASSIST_HANDLE_FAILED.value
                self.error_detail = "处理辅助应用失败"
                return False

            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行任务通用步骤失败: {str(e)}")
            return False

    async def _start_task_exec(self) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 执行主任务流程")
        """开始执行任务"""
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始执行任务")
            
            # 执行通用步骤
            if not await self._execute_task_common():
                return False

            # 检查任务是否已取消
            if self._cancel_event.is_set():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止执行")
                return False

            # 安装和测试性能应用
            logger.info(f"[子任务 {self.sub_task_id}] 开始处理性能测试应用")
            if not await self.app_control._execute_initial_perf_tests(self.platform, self.install_mode, self.sub_task_dirs['results']):
                logger.info(f"[子任务 {self.sub_task_id}] 执行初始性能测试失败")
                self.error_code = ErrorCode.APP_OPERATION_FAILED.value
                self.error_detail = "执行初始性能测试失败"
                return False
            
            logger.info(f"[子任务 {self.sub_task_id}] 任务执行成功")
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行任务失败: {str(e)}")
            return False

    async def _retry_task_exec(self, stats: Dict) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 执行重试任务流程")
        """重试任务执行"""
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始执行重试任务")
            
            # 执行通用步骤
            if not await self._execute_task_common():
                return False
            
            # 检查任务是否已取消
            if self._cancel_event.is_set():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止执行")
                sub_task_status = TaskStatus.CANCELED.value
                return False
            
            # 安装和测试性能应用(重试模式)
            logger.info(f"[子任务 {self.sub_task_id}] 开始处理性能测试应用")
            if not await self.app_control._execute_retry_perf_tests(self.platform, self.install_mode, self.sub_task_dirs['results'], stats):
                logger.error(f"[子任务 {self.sub_task_id}] 执行重试性能测试失败")
                self.error_code = ErrorCode.APP_OPERATION_FAILED.value
                self.error_detail = "执行重试性能测试失败"
                return False
            
            logger.info(f"[子任务 {self.sub_task_id}] 重试任务执行成功")
            return True

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行重试任务失败: {str(e)}")
            return False

    async def _update_task_start_status(self, sub_task_status: int) -> bool:
        """更新开始执行任务状态"""
        try:
            # 如果子任务准备执行,更新状态为开始
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logger.info(f"[任务 {self.task_id}] 准备执行,更新任务状态为开始")

            # 更新任务状态
            current_status = self.task_detail.get('status')
            task_params = {
                'task_id': self.task_id,
                'task_status': sub_task_status
            }
            if current_status == TaskStatus.PENDING.value:
                task_params['start_time'] = current_time

            await perf_api.update_task_status_by_id(**task_params)

            # 更新子任务状态
            logger.info(f"[子任务 {self.sub_task_id}] 准备执行,更新子任务状态为开始")
            sub_task_params = {
                'sub_task_id': self.sub_task_id,
                'status': sub_task_status
            }
            if current_status == TaskStatus.PENDING.value:
                sub_task_params['start_time'] = current_time

            await perf_api.update_sub_task_status_by_id(**sub_task_params)

            return True
        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 检查任务未开始状态失败: {str(e)}")
            return False

    async def _update_task_end_status(self, sub_task_status: int, error_code: int = None, error_title: str = None, error_detail: str = None) -> bool:  
        """更新结束执行任务状态"""
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 先更新子任务状态
            await perf_api.update_sub_task_status_by_id(
                self.sub_task_id,
                sub_task_status,
                end_time=current_time,
                error_code=error_code,
                error_title=error_title,
                error_detail=error_detail
            )

            # 获取最新的任务详情
            logger.info(f"[子任务 {self.sub_task_id}] 获取最新任务状态")
            task_detail = await perf_api.get_task_detail_by_id(self.task_id)
            if not task_detail:
                logger.error(f"[任务 {self.task_id}] 获取任务详情失败")
                return False

            # 获取子任务列表
            sub_tasks = task_detail.get('sub_tasks', [])
            if not sub_tasks:
                logger.warning(f"[任务 {self.task_id}] 没有子任务")
                return False

            # 检查每个子任务的状态
            finished_count = 0
            has_failed = False
            enabled_tasks = [task for task in sub_tasks if task.get('enabled', True)]
            total_enabled = len(enabled_tasks)

            if total_enabled == 0:
                logger.warning(f"[任务 {self.task_id}] 没有启用的子任务")
                return False

            for sub_task in enabled_tasks:
                status = sub_task.get('status')
                if status in [TaskStatus.SUCCESS.value, TaskStatus.FAILED.value, TaskStatus.CANCELED.value,
                              TaskStatus.TIMEOUT.value]:
                    finished_count += 1
                    if status != TaskStatus.SUCCESS.value:
                        has_failed = True

            completion_rate = finished_count / total_enabled
            logger.info(f"[任务 {self.task_id}] 完成率: {completion_rate:.2f} ({finished_count}/{total_enabled})")

            # 只有当所有子任务都完成时才更新任务状态
            if finished_count == total_enabled:
                final_status = TaskStatus.FAILED.value if has_failed else TaskStatus.SUCCESS.value
                logger.info(
                    f"[任务 {self.task_id}] 所有子任务执行完成，更新任务状态为{TaskStatus.get_name(final_status)}")
                await perf_api.update_task_status_by_id(
                    self.task_id,
                    final_status,
                    end_time=current_time
                )

            return True

        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 更新任务结束状态失败: {str(e)}")
            return False

    async def terminate_sub_task(self) -> bool:
        """终止子任务执行
        
        实现可靠的终止流程:
        1. 使用锁防止重复终止
        2. 设置统一的取消事件
        3. 并发执行所有终止操作
        4. 添加超时控制
        5. 完善错误处理
        
        Returns:
            bool: 是否成功终止
        """
        async with self._termination_lock:
            if await self._check_cancellation():
                logger.info(f"[子任务 {self.sub_task_id}] 终止操作已在进行中")
                return True
                
            if not self.sub_task_context:
                logger.warning("当前没有正在执行的子任务")
                return True

            try:
                # 1. 设置取消事件
                self._cancel_event.set()
                await self._propagate_cancel_event()
                
                # 2. 准备所有终止操作
                termination_tasks = [
                    (self.network_utils.cancel_all_downloads(), ErrorCode.DOWNLOAD_CANCEL_FAILED, "终止下载任务"),
                    (self.case_control.terminate_case_process(), ErrorCode.CASE_PROCESS_TERMINATE_FAILED, "终止用例进程"),
                    (self.app_control.terminate_app_processes(), ErrorCode.APP_PROCESS_TERMINATE_FAILED, "终止应用进程"),
                ]

                # 3. 并发执行所有终止操作
                results = await asyncio.gather(*[task[0] for task in termination_tasks], return_exceptions=True)
                
                # 4. 检查结果
                for result, task_info in zip(results, termination_tasks):
                    if isinstance(result, Exception):
                        logger.error(f"[子任务 {self.sub_task_id}] {task_info[2]}失败: {str(result)}")
                        self.error_code = task_info[1].value
                        self.error_detail = f"{task_info[2]}失败: {str(result)}"
                        return False
                    elif not result:
                        logger.error(f"[子任务 {self.sub_task_id}] {task_info[2]}失败")
                        self.error_code = task_info[1].value
                        self.error_detail = f"{task_info[2]}失败"
                        return False
                
                # 5. 清理子任务
                await self._cleanup_sub_task()
                logger.info(f"[子任务 {self.sub_task_id}] 终止成功")

                return True

            except Exception as e:
                logger.error(f"[子任务 {self.sub_task_id}] 终止失败: {str(e)}")
                self.error_code = ErrorCode.TASK_TERMINATE_FAILED.value
                self.error_detail = f"终止失败: {str(e)}"
                return False
            finally:
                self._reset_cancel_state()

    async def _cleanup_sub_task(self, sub_task_status: TaskStatus = None) -> None:
        """清理子任务"""
        # 使用清理锁确保清理操作只执行一次
        async with self._cleanup_lock:
            # 检查是否已经清理过
            if hasattr(self, '_cleanup_completed') and self._cleanup_completed:
                logger.debug(f"[子任务 {self.sub_task_id}] 清理操作已完成，跳过重复清理")
                return

            try:
                logger.debug(f"[子任务 {self.sub_task_id}] 开始清理子任务资源")

                # 1. 停止视频播放
                await self.task_helper.stop_play_test_video()

                # 2. 清理任务辅助器
                if not await self.task_helper._cleanup():
                    self.error_code = ErrorCode.TASK_CLEANUP_FAILED.value
                    self.error_detail = "清理任务辅助器失败"

                # 3. 清理用例控制器
                if not await self.case_control._cleanup():
                    self.error_code = ErrorCode.CASE_CONTROL_CLEANUP_FAILED.value
                    self.error_detail = "清理用例控制器失败"

                # 4. 清理应用控制器
                if not await self.app_control._cleanup():
                    self.error_code = ErrorCode.APP_CONTROL_CLEANUP_FAILED.value
                    self.error_detail = "清理应用控制器失败"

                # 标记清理完成
                self._cleanup_completed = True
                logger.debug(f"[子任务 {self.sub_task_id}] 子任务资源清理完成")

            except Exception as e:
                logger.error(f"[子任务 {self.sub_task_id}] 清理资源失败: {str(e)}")
            finally:
                # 只在非取消状态下更新任务和子任务状态
                if sub_task_status:
                    error_title = ErrorCode.get_message(self.error_code) if self.error_code else "未知错误"
                    await self._update_task_end_status(
                        sub_task_status,
                        error_code=self.error_code,
                        error_title=error_title,
                        error_detail=self.error_detail
                    )


task_control = TaskControl()