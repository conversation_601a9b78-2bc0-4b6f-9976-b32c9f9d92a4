"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-11 16:42:48
FilePath: /global_rtc_client/utils/perf/case_control.py
Description:
"""

import os
import time
import asyncio
import json
from config.constants import CaseStatus
from typing import Tuple, Optional, Dict, List
from utils.common.file_utils import get_python_path, find_first_file_recursively
from utils.common.log_utils import get_logger, stream_realtime_logs, monitor_process_status
from utils.perf.perf_upload import perf_upload
from utils.perf.perf_parser import perf_parser
from core.auth_manager import auth_manager
from apis.perf_api import perf_api
from core.settings_manager import settings_manager
from datetime import datetime
from config.constants import AppType, PlatformType, PerfToolType
from utils.common.android_device import android_device
from utils.common.ios_device import ios_device
from utils.perf.profile_analyzer import ProfileAnalyzer
from utils.perf.task_helper import task_helper
logger = get_logger(__name__)


class CaseControl:
    """用例控制器"""

    def __init__(self):
        """初始化用例控制器"""
        # 基本属性
        self.task_id = None
        self.sub_task_id = None
        self.sub_task_detail = None
        self.case_group = []
        self.app_group = {}
        self.device_group = {}
        self.account_group = {}

        # 文件路径
        self.perf_data_path = None
        self.byteio_data_path = None
        self.cpu_profile_path = None

        # 进程管理
        self._current_case_process = None
        
        self.user_info = None
        self.perf_device = None
        self.power_level = None
        self.case_run_count = None
        self.case_retry_count = None
        self.perf_app = None
        self.test_case = None
        self.run_id = None
        self.output_dir = None
        self.case_dir = None
        self.timestamp = None

        self.perf_uploader = perf_upload
        self.python_executable = get_python_path()
        self.profile_analyzer = None

        # 使用新的配置结构
        case_config = settings_manager.get('case', {})
        self.case_retry_interval = case_config.get('run', {}).get('retry', {}).get('interval', 5)
        
        battery_config = settings_manager.get('case', {}).get('battery', {})
        self.battery_check_interval = battery_config.get('check_interval', 180)
        self.battery_check_retries = battery_config.get('check_retries', 15)

        self.case_process = None  # 当前用例执行进程
        
        # 添加取消事件
        self._cancel_event = asyncio.Event()
        self._is_cancelled = False  # 新增取消状态标志

    def get_perf_tool_type(self) -> int:
        """获取性能工具类型

        Returns:
            int: 性能工具类型，默认为DS
        """
        # 从任务详情中获取性能工具类型
        if self.sub_task_detail:
            task_info = self.sub_task_detail.get('task_info', {})
            tool_type = task_info.get('perf_tool_type')
            if tool_type is not None:
                return tool_type

        # 默认返回DS工具类型
        return PerfToolType.DS

    def get_perf_file_prefix(self, tool_type: int) -> str:
        """根据性能工具类型获取文件前缀

        Args:
            tool_type: 性能工具类型

        Returns:
            str: 文件前缀
        """
        if tool_type == PerfToolType.DS:
            return "ds"
        elif tool_type == PerfToolType.GAMEPERF:
            return "gameperf"
        else:
            return ""

    async def _init_case_execution(self, perf_app: Dict, output_dir: str) -> None:
        """初始化用例执行环境"""
        self.user_info = auth_manager.get_current_user()
        self.sub_task_id = self.sub_task_detail.get('id')
        self.perf_app = perf_app
        self.output_dir = output_dir
        self.perf_device = self.device_group.get('perf_device', {})
        self.power_level = self.sub_task_detail.get('perf_device_power_level', 20)
        self.case_run_count = self.sub_task_detail.get('case_run_count', 1)
        self.case_retry_count = self.sub_task_detail.get('case_retry_count', 1)
        self.profile_analyzer = ProfileAnalyzer(
            rule_tags=["all"],  # rtc/all/liveplayer
            common_info={
                'sub_task_id': str(self.sub_task_id),
                'perf_device': self.perf_device,
                'perf_app': self.perf_app,
                'business_name': self.user_info.get('business_name'),
            }
        )

    def _prepare_test_cases(self) -> Tuple[List[Dict], bool, int]:
        """准备要执行的用例列表
        
        Returns:
            Tuple[List[Dict], bool, int]: (用例列表, 是否重试任务, 总用例数)
        """
        cases_to_retry = self.perf_app.get('cases_to_retry', [])
        is_retry_task = len(cases_to_retry) > 0
        
        if is_retry_task:
            # 创建case_id到重试信息的映射，方便查找
            retry_case_map = {case['case_id']: case for case in cases_to_retry}
            
            # 过滤需要重试的用例
            test_cases = []
            for case in self.case_group:
                case_id = case.get('case_id')
                if case_id in retry_case_map:
                    retry_info = retry_case_map[case_id]
                    if retry_info.get('retry_count', 0) > 0:
                        test_cases.append(case)
                        logger.info(f"[子任务 {self.sub_task_id}] 用例 {case.get('title')} (ID: {case_id}) 需要重试 {retry_info.get('retry_count')} 次")
            
            total_cases = len(test_cases)
            if total_cases == 0:
                logger.warning(f"[子任务 {self.sub_task_id}] 没有找到需要重试的用例")
                return [], is_retry_task, 0
            logger.info(f"[子任务 {self.sub_task_id}] 开始执行重试用例组 (总数: {total_cases})")
        else:
            test_cases = self.case_group
            total_cases = len(test_cases)
            logger.info(f"[子任务 {self.sub_task_id}] 开始执行用例组 (总数: {total_cases})")
            
        return test_cases, is_retry_task, total_cases

    async def execute_case_group(self, test_cases: List[Dict], perf_app: Dict, output_dir: str) -> bool:
        logger.debug(f"[子任务 {self.sub_task_id}] 开始执行用例组，共{len(test_cases)}条用例，应用: {perf_app.get('name', 'unknown')}")
        try:
            # 1. 初始化执行环境
            await self._init_case_execution(perf_app, output_dir)
            
            # 检查取消状态
            if self.is_cancelled():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止执行用例组")
                return False
                
            # 2. 准备用例列表
            prepared_test_cases, is_retry_task, total_cases = self._prepare_test_cases()
            if total_cases == 0:
                return True
                
            # 3. 获取应用类型
            app_type = self.perf_app.get("app_type")
            app_type_name = AppType.get_name(app_type) if app_type else "unknown"
            passed_cases = 0
            
            # 获取重试信息
            cases_to_retry = self.perf_app.get('cases_to_retry', [])
            
            # 4. 执行用例
            for case_idx, test_case in enumerate(prepared_test_cases, 1):
                # 检查任务是否已取消
                if self.is_cancelled():
                    logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止执行用例")
                    return False

                try:
                    self.test_case = test_case
                    case_title = test_case.get("title", "")
                    case_path = test_case.get("dir", "")
                    case_dir = os.path.join(output_dir, app_type_name, case_title)
                    
                    # 获取需要执行的次数
                    if is_retry_task:
                        case_retry_info = next((case for case in cases_to_retry if case['case_id'] == test_case.get('case_id')), None)
                        if not case_retry_info:
                            logger.warning(f"[子任务 {self.sub_task_id}] 未找到用例 {case_title} 的重试信息，跳过执行")
                            continue
                        need_run_count = case_retry_info.get('retry_count', 0)
                        if need_run_count <= 0:
                            logger.warning(f"[子任务 {self.sub_task_id}] 用例 {case_title} 重试次数为0，跳过执行")
                            continue
                    else:
                        need_run_count = self.case_run_count
                    
                    actual_run_count = need_run_count
                    
                    logger.info(f"[子任务 {self.sub_task_id}] 执行用例 [{case_idx}/{total_cases}]: {case_title}")
                    logger.info(f"[子任务 {self.sub_task_id}] 计划执行 {actual_run_count} 次，最大重试 {self.case_retry_count} 次")

                    # 执行用例
                    successful_runs = await self._execute_single_case(test_case, case_dir, case_path, actual_run_count)
                    
                    # 处理执行结果
                    if successful_runs == actual_run_count:
                        tool_type = self.get_perf_tool_type()
                        # 使用新的聚合方法计算多数据源平均值
                        total_avg_perf_data = perf_parser.calculate_aggregated_avg_perf_data(case_dir, tool_type)
                        await self._upload_perf_data(True, perf_data=total_avg_perf_data)
                        passed_cases += 1
                        logger.info(f"[子任务 {self.sub_task_id}] 用例 {case_title} 执行成功")
                    else:
                        logger.info(f"[子任务 {self.sub_task_id}] 用例 {case_title} 执行失败")

                    # 输出进度
                    logger.info(f"[子任务 {self.sub_task_id}] 当前进度: {(case_idx/total_cases*100):.1f}% ({case_idx}/{total_cases} 用例已完成)")

                except Exception as e:
                    logger.error(f"[子任务 {self.sub_task_id}] 用例 {case_title} 执行异常: {str(e)}")
                    continue

            # 5. 完成处理
            self.profile_analyzer.trigger_analysis()
            logger.info(f"[子任务 {self.sub_task_id}] 执行完成 - 总用例数: {total_cases}, 通过用例数: {passed_cases}")
            logger.debug(f"[子任务 {self.sub_task_id}] 用例组执行完成，成功数: {passed_cases}")
            return passed_cases == total_cases

        except asyncio.CancelledError:
            logger.warning(f"[子任务 {self.sub_task_id}] execute_case_group 被取消")
            return False
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行用例组失败: {str(e)}", exc_info=True)
            return False

    async def _execute_single_case(self, test_case: Dict, case_dir: str, case_path: str, need_run_count: int) -> int:
        """执行单个用例"""
        successful_runs = 0
        case_title = test_case.get("title", "")

        for run_idx in range(need_run_count):
            # 检查取消状态
            if self.is_cancelled():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止执行用例")
                return successful_runs

            logger.info(f"[子任务 {self.sub_task_id}] 开始第 {run_idx + 1}/{need_run_count} 次执行")
            
            # 创建运行记录
            self.run_id = await self._create_run_record()
            if not self.run_id:
                logger.error(f"[子任务 {self.sub_task_id}] 创建运行记录失败")
                continue

            # 执行用例
            completed = await self.run_test_case(case_dir, case_path)
            
            # 处理执行结果
            handler = self._handle_successful_run if completed else self._handle_failed_run
            result = await handler(run_dir=self.current_run_dir)
            
            if result.get('status') == CaseStatus.SUCCESS.value:
                # 处理成功情况
                updated_runs = await self._handle_run_result(
                    result=result,
                    successful_runs=successful_runs,
                    is_retry=False
                )
                successful_runs = updated_runs if updated_runs is not None else successful_runs
                continue

            # 处理失败情况
            await self._handle_run_result(
                result=result,
                case_title=case_title,
                is_retry=False
            )
            
            # 进行重试
            retry_success = await self._retry_failed_case(case_dir, case_path, case_title)
            if retry_success:
                successful_runs += 1
                
        return successful_runs

    async def _retry_failed_case(self, case_dir: str, case_path: str, case_title: str) -> bool:
        """重试失败的用例"""
        for retry_idx in range(self.case_retry_count):
            # 检查取消状态
            if self.is_cancelled():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止重试")
                return False

            logger.info(f"[子任务 {self.sub_task_id}] 开始第 {retry_idx + 1}/{self.case_retry_count} 次重试")
                
            await asyncio.sleep(self.case_retry_interval)
            
            self.run_id = await self._create_run_record()
            if not self.run_id:
                logger.error(f"[子任务 {self.sub_task_id}] 创建重试运行记录失败")
                continue
            
            retry_completed = await self.run_test_case(case_dir, case_path)
            handler = self._handle_successful_run if retry_completed else self._handle_failed_run
            retry_result = await handler(run_dir=self.current_run_dir)
            
            if retry_result.get('status') == CaseStatus.SUCCESS.value:
                # 处理重试成功情况
                await self._handle_run_result(
                    result=retry_result,
                    case_title=case_title,
                    is_retry=True
                )

                return True

            # 处理重试失败情况
            await self._handle_run_result(
                result=retry_result,
                case_title=case_title,
                is_retry=True
            )
        return False

    async def _handle_run_result(self, result: Dict, successful_runs: Optional[int] = None, case_title: Optional[str] = None,
                              is_retry: bool = False) -> Optional[int]:
        """处理用例运行结果的通用方法

        Args:
            result: 运行结果字典
            successful_runs: 成功运行次数，仅当成功且非重试时使用
            case_title: 用例标题，仅当需要记录日志时使用
            is_retry: 是否是重试结果

        Returns:
            Optional[int]: 如果是成功结果且传入了successful_runs，返回更新后的successful_runs；否则返回None
        """
        # 更新运行记录
        await self._update_run_record(result)

        # 处理成功情况下的额外操作
        if result.get('status') == CaseStatus.SUCCESS.value:
            # 处理性能数据
            _avg_perf_data = result.get('_avg_perf_data', {})
            if _avg_perf_data:
                await self._upload_perf_data(False, perf_data=_avg_perf_data)

            # 处理CPU性能分析数据
            if self.cpu_profile_path:
                await self._add_cpu_profile()

            # 记录日志
            if case_title and is_retry:
                logger.info(f"[子任务 {self.sub_task_id}] 用例 {case_title} 重试成功")

            # 更新成功次数并返回
            if successful_runs is not None and not is_retry:
                return successful_runs + 1
        else:
            # 失败情况下记录日志
            if case_title:
                logger.error(f"[子任务 {self.sub_task_id}] 用例 {case_title} {'重试' if is_retry else '执行'}失败")

        return None

    async def run_test_case(self, case_output_dir: str, case_file_path: str) -> bool:
        """运行测试用例

        Args:
            case_output_dir: 用例输出目录
            case_file_path: 用例文件路径

        Returns:
            bool: 用例是否执行成功
        """
        # 检查取消状态
        if self.is_cancelled():
            logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止执行用例")
            return False

        # 初始化运行环境
        self.timestamp = str(int(time.time()))
        case_module_path = case_file_path.lstrip("/").replace("/", ".").replace(".py", "").replace("global_business_perf.", "")
        self.current_run_dir = os.path.join(os.getcwd(), case_output_dir, self.timestamp)

        repo_dir = os.path.join(
            os.getcwd(),
            settings_manager.get('storage.paths.base.repo', 'repos'),
            "global_business_perf"
        )
        logger.info(f"[子任务 {self.sub_task_id}] 开始执行用例: {os.path.basename(case_file_path)}")

        try:
            # 准备执行环境
            if not await self._prepare_execution_environment(repo_dir):
                return False

            # 检查设备电量
            if not await self._ensure_device_battery_level():
                return False

            # 执行用例
            return await self._execute_case_command(case_module_path, repo_dir)

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行用例异常: {str(e)}")
            await self._update_run_record_on_failure()
            return False

    async def _prepare_execution_environment(self, repo_dir: str) -> bool:
        """准备执行环境

        Args:
            repo_dir: 仓库目录

        Returns:
            bool: 准备是否成功
        """
        try:
            # 检查并创建目录
            os.makedirs(self.current_run_dir, exist_ok=True)
            if not os.path.exists(repo_dir):
                logger.error(f"[子任务 {self.sub_task_id}] 性能用例仓库不存在: {repo_dir}")
                return False
            return True
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 准备执行环境失败: {str(e)}")
            return False

    async def _ensure_device_battery_level(self) -> bool:
        """确保设备电量充足

        Returns:
            bool: 电量是否充足
        """
        try:
            for retry in range(self.battery_check_retries):
                # Android设备需要先亮屏
                if self.perf_device.get('platform') == PlatformType.ANDROID.value:
                    await android_device.turn_on_screen(self.perf_device.get('udid'))

                # 检查电量
                if await self._check_perf_device_battery_level(self.perf_device, self.power_level):
                    return True

                # 如果不是最后一次重试，等待后继续
                if retry < self.battery_check_retries - 1:
                    logger.warning(f"[子任务 {self.sub_task_id}] 设备电量不足,等待{self.battery_check_interval}秒后重试 ({retry + 1}/{self.battery_check_retries})")

                    # Android设备关屏
                    if self.perf_device.get('platform') == PlatformType.ANDROID.value:
                        await android_device.turn_off_screen(self.perf_device.get('udid'))

                    # 分段等待，以便能够及时响应取消请求
                    if not await self._wait_with_cancel_check(self.battery_check_interval):
                        return False

            # 所有重试都失败
            logger.error(f"[子任务 {self.sub_task_id}] 设备电量不足且等待超时,跳过用例: {self.test_case.get('title')}")
            return False

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 检查设备电量异常: {str(e)}")
            return False

    async def _wait_with_cancel_check(self, seconds: int) -> bool:
        """等待指定秒数，同时检查取消状态

        Args:
            seconds: 等待秒数

        Returns:
            bool: 是否正常完成等待（未被取消）
        """
        for _ in range(seconds):
            if self.is_cancelled():
                logger.info(f"[子任务 {self.sub_task_id}] 任务已被取消，停止等待")
                return False
            await asyncio.sleep(1)
        return True

    async def _execute_case_command(self, case_module_path: str, repo_dir: str) -> bool:
        """执行用例命令

        Args:
            case_module_path: 用例模块路径
            repo_dir: 仓库目录

        Returns:
            bool: 执行是否成功
        """
        try:
            # 构建命令参数
            cmd_args = [
                self.python_executable,
                f"{repo_dir}/manage.py",
                "runtest",
                case_module_path,
                "-w", self.current_run_dir,
                "--report-type", "json"
            ]

            # 创建进程
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=repo_dir,
                limit=1024 * 1024
            )
            self._current_case_process = process

            try:
                # 处理进程输出和等待结果
                return await self._handle_process_execution(process)
            finally:
                self._current_case_process = None

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 执行用例命令异常: {str(e)}")
            await self._update_run_record_on_failure()
            return False

    async def _handle_process_execution(self, process) -> bool:
        """处理进程执行 - 实时流式日志处理

        实现实时日志流处理功能：
        1. 逐行读取并处理进程输出，避免缓冲区阻塞
        2. 异步非阻塞的日志读取机制
        3. 实时解析和处理每行日志
        4. 同时监控进程状态和日志流

        Args:
            process: 子进程对象

        Returns:
            bool: 执行是否成功
        """
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始实时处理进程输出")

            # 创建实时日志处理任务
            stdout_task = asyncio.create_task(
                stream_realtime_logs(process.stdout, "STDOUT", self.sub_task_id),
                name="case-stdout-realtime"
            )
            stderr_task = asyncio.create_task(
                stream_realtime_logs(process.stderr, "STDERR", self.sub_task_id),
                name="case-stderr-realtime"
            )

            # 创建进程监控任务
            process_monitor_task = asyncio.create_task(
                monitor_process_status(process, self.sub_task_id),
                name="case-process-monitor"
            )

            # 并行执行所有任务，等待进程完成
            try:
                # 等待进程结束
                exit_code = await process.wait()

                # 进程结束后，给日志处理任务一些时间来处理剩余输出
                logger.debug(f"[子任务 {self.sub_task_id}] 进程已结束，等待日志处理完成...")

                # 等待日志处理任务完成，设置超时避免无限等待
                await asyncio.wait_for(
                    asyncio.gather(stdout_task, stderr_task, return_exceptions=True),
                    timeout=5.0
                )

            except asyncio.TimeoutError:
                logger.warning(f"[子任务 {self.sub_task_id}] 日志处理超时，强制结束")
                stdout_task.cancel()
                stderr_task.cancel()

            finally:
                # 确保监控任务被取消
                process_monitor_task.cancel()
                try:
                    await process_monitor_task
                except asyncio.CancelledError:
                    pass

            # 处理执行结果
            if exit_code == 0:
                logger.info(f"[子任务 {self.sub_task_id}] 用例执行完成，退出码: {exit_code}")
                return True
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 用例执行失败，退出码: {exit_code}")
                return False

        except asyncio.CancelledError:
            logger.warning(f"[子任务 {self.sub_task_id}] 用例执行被取消，正在清理进程...")
            await self._cleanup_cancelled_process(process)
            raise
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理进程执行异常: {str(e)}")
            return False

    async def _cleanup_cancelled_process(self, process) -> None:
        """清理被取消的进程

        增强的进程清理机制，确保实时日志流也被正确清理

        Args:
            process: 子进程对象
        """
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 开始清理被取消的进程 (PID: {process.pid})")

            if process.returncode is None:
                # 首先尝试优雅终止
                logger.debug(f"[子任务 {self.sub_task_id}] 发送 SIGTERM 信号...")
                process.terminate()

                try:
                    # 等待进程优雅退出
                    await asyncio.wait_for(process.wait(), timeout=5.0)
                    logger.info(f"[子任务 {self.sub_task_id}] 进程已优雅终止")
                except asyncio.TimeoutError:
                    # 如果优雅终止失败，强制杀死进程
                    logger.warning(f"[子任务 {self.sub_task_id}] 优雅终止超时，强制杀死进程...")
                    process.kill()

                    try:
                        # 等待强制终止完成
                        await asyncio.wait_for(process.wait(), timeout=2.0)
                        logger.info(f"[子任务 {self.sub_task_id}] 进程已强制终止")
                    except asyncio.TimeoutError:
                        logger.error(f"[子任务 {self.sub_task_id}] 强制终止进程失败")
            else:
                logger.debug(f"[子任务 {self.sub_task_id}] 进程已经结束，退出码: {process.returncode}")

            # 确保流被关闭
            try:
                if process.stdout and not process.stdout.is_closing():
                    process.stdout.close()
                if process.stderr and not process.stderr.is_closing():
                    process.stderr.close()
                logger.debug(f"[子任务 {self.sub_task_id}] 进程流已关闭")
            except Exception as stream_err:
                logger.warning(f"[子任务 {self.sub_task_id}] 关闭进程流时出错: {stream_err}")

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 清理进程失败: {str(e)}")
        finally:
            logger.debug(f"[子任务 {self.sub_task_id}] 进程清理完成")

    async def _update_run_record_on_failure(self) -> None:
        """更新失败时的运行记录"""
        await self._update_run_record({
            'case_log_tos_path': None,
            'screenshot_tos_path': [],
            'perf_data_tos_path': None,
            'byteio_data_tos_path': None,
            'cpu_profile_tos_path': None,
            'status': CaseStatus.FAILED.value
        })



    def _get_case_result(self, run_dir: str) -> bool:
        """从 shoots-report.json 获取用例执行结果
        
        Args:
            run_dir: 运行目录路径
            
        Returns:
            bool: 用例是否执行成功
        """
        try:
            # 读取 shoots-report.json
            report_file = os.path.join(run_dir, "shoots-report.json")
            if not os.path.exists(report_file):
                logger.warning(f"[子任务 {self.sub_task_id}] 未找到用例报告文件: {report_file}")
                return False
                
            # 检查文件是否为空
            if os.path.getsize(report_file) == 0:
                logger.warning(f"[子任务 {self.sub_task_id}] 用例报告文件为空: {report_file}")
                return False
                
            with open(report_file, 'r') as f:
                report_data = json.load(f)

            # 获取执行结果
            return report_data.get('summary', {}).get('succeed', False)
            
        except Exception as e:
            logger.warning(f"[子任务 {self.sub_task_id}] 获取用例执行结果失败: {str(e)}")
            return False

    def _sort_screenshot_tos_path(self, screenshot_tos_path: List[str]) -> List[str]:
        """按时间戳对截图URL进行排序
        
        Args:
            screenshot_tos_path: 截图URL列表
            
        Returns:
            List[str]: 排序后的URL列表
        """
        def extract_timestamp(url: str) -> int:
            try:
                # 尝试从URL中提取时间戳
                # 处理两种格式:
                # 1. custom_screenshot_*_时间戳.jpg
                # 2. screenshot_*_时间戳.jpg
                filename = url.split('/')[-1]
                if '_' in filename:
                    timestamp = filename.split('_')[-1].split('.')[0]
                    # 如果是数字格式的时间戳，直接返回
                    if timestamp.isdigit():
                        return int(timestamp)
                    # 如果是日期时间格式(如20250523_035502222)，转换为时间戳
                    try:
                        dt = datetime.strptime(timestamp, '%Y%m%d_%H%M%S%f')
                        return int(dt.timestamp() * 1000)
                    except ValueError:
                        return 0
                return 0
            except Exception:
                return 0
                
        return sorted(screenshot_tos_path, key=extract_timestamp)

    async def _handle_successful_run(self, run_dir: str):
        """处理成功用例运行"""
        logger.debug(f"[子任务 {self.sub_task_id}] 处理成功用例运行，目录: {run_dir}")
        try:
            # 获取用例执行结果
            case_result = self._get_case_result(run_dir)

            # 上传目录中的所有日志和JSON文件
            case_log_tos_urls, perf_data_tos_urls = await self.perf_uploader.upload_directory_files_by_type(run_dir)

            # 上传截图并排序
            screenshot_tos_path = await self.perf_uploader.upload_all_screenshots_to_tos(run_dir)
            screenshot_tos_path = self._sort_screenshot_tos_path(screenshot_tos_path)

            if not case_result:
                return {
                    'status': CaseStatus.FAILED.value,
                    'case_log_tos_urls': case_log_tos_urls,
                    'perf_data_tos_urls': perf_data_tos_urls,
                    'screenshot_tos_path': screenshot_tos_path
                }
            
            # 上传CPU profile文件
            self.cpu_profile_path = os.path.join(run_dir, "cpu_profile.data")
            cpu_profile_tos_path = await self.perf_uploader.upload_perf_file_to_tos(self.cpu_profile_path)

            # 获取性能数据目录并上传其中的JSON文件
            device_id = self.perf_device.get("udid")
            data_dir = os.path.join(run_dir, "data", device_id)

            # 如果数据目录存在，扫描并上传其中的JSON文件
            if os.path.exists(data_dir):
                logger.debug(f"[子任务 {self.sub_task_id}] 扫描性能数据目录: {data_dir}")
                _, additional_perf_data_urls = await self.perf_uploader.upload_directory_files_by_type(data_dir)

                # 合并性能数据URL
                if additional_perf_data_urls:
                    perf_data_tos_urls.update(additional_perf_data_urls)
                    logger.info(f"[子任务 {self.sub_task_id}] 合并性能数据文件: {len(additional_perf_data_urls)}个")

            # 聚合性能数据用于后续上传
            aggregated_perf_data = self._aggregate_perf_data(data_dir)

            logger.debug(f"[子任务 {self.sub_task_id}] 成功用例处理完成，是否通过: {case_result}")
            return {
                'status': CaseStatus.SUCCESS.value,
                'case_log_tos_urls': case_log_tos_urls,  # 新格式：所有.log文件
                'perf_data_tos_urls': perf_data_tos_urls,  # 新格式：所有.json文件
                'screenshot_tos_path': screenshot_tos_path,
                'cpu_profile_tos_path': cpu_profile_tos_path,
                '_avg_perf_data': aggregated_perf_data,  # 添加聚合的性能数据
                # 注意：byteio_data_tos_path 已移除，数据通过聚合处理
            }
        
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理成功的用例运行失败: {str(e)}", exc_info=True)
            return {
                'status': CaseStatus.FAILED.value
            }
        
    async def _handle_failed_run(self, run_dir: str):
        """处理失败用例运行"""
        logger.debug(f"[子任务 {self.sub_task_id}] 处理失败用例运行，目录: {run_dir}")
        try:
            logger.info(f"[子任务 {self.sub_task_id}] 处理失败的用例运行")
            
            # 获取并上传用例日志
            case_log_file = os.path.join(run_dir, "case.log")
            case_log_tos_path = await self.perf_uploader.upload_perf_file_to_tos(case_log_file)
            
            # 上传截图并排序
            screenshot_tos_path = []
            try:
                screenshot_tos_path = await self.perf_uploader.upload_all_screenshots_to_tos(run_dir)
                # 对截图URL进行排序
                screenshot_tos_path = self._sort_screenshot_tos_path(screenshot_tos_path)
            except Exception as e:
                logger.warning(f"[子任务 {self.sub_task_id}] 上传失败用例截图失败: {str(e)}")
            
            logger.debug(f"[子任务 {self.sub_task_id}] 失败用例处理完成")
            return {
                'status': CaseStatus.FAILED.value,
                'case_log_tos_path': case_log_tos_path,
                'screenshot_tos_path': screenshot_tos_path
            }
        
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 处理失败的用例运行失败: {str(e)}", exc_info=True)
            return {
                'status': CaseStatus.FAILED.value
            }
        
    async def _run_failed_case(self, case_dir: str, case_path: str, retry_count: int) -> bool:
        """处理失败用例的重试逻辑"""
        try:
            # 如果还有重试次数，进行重试
            for retry_idx in range(retry_count):
                logger.info(f"[子任务 {self.sub_task_id}] 开始第 {retry_idx + 1}/{retry_count} 次重试")
                
                # 等待一段时间再重试
                await asyncio.sleep(self.case_retry_interval)
                
                # 创建新的运行记录
                self.run_id = await self._create_run_record()
                if not self.run_id:
                    logger.error(f"[子任务 {self.sub_task_id}] 创建重试运行记录失败")
                    continue
                
                # 执行重试用例
                completed = await self.run_test_case(case_dir, case_path)
                if completed:
                    break
            return completed

        except Exception as e:
            logger.error(f"处理失败用例异常: {str(e)}")
            return False
        
    def _load_avg_perf_data(self, avg_file_path: str) -> Optional[dict]:
        """加载性能数据平均值"""
        if os.path.exists(avg_file_path):
            with open(avg_file_path, 'r') as f:
                data = json.load(f)
                return data
        return None

    async def _create_run_record(self) -> Optional[int]:
        """创建运行记录"""
        try:
            # 使用成员变量
            app_id = self.perf_app.get('id')
            if not app_id:
                logger.error("[子任务 {self.sub_task_id}] 无法从perf_app获取app_id")
                return None

            # 构建运行记录数据
            run_data = {
                "sub_task_id": self.sub_task_id,
                'app_id': app_id,
                'device_id': self.perf_device.get('id'),
                'case_id': self.test_case.get('case_id'),
                'status': CaseStatus.RUNNING.value,
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'end_time': None,
                'case_log_tos_url': None,
                'perf_data_tos_url': None,
                'byteio_data_tos_url': None,
                'screenshottos_urls': []
            }
            
            # 如果是Libra实验类型，添加version_type字段
            experiment_config = self.perf_app.get('experiment_config', {})
            if experiment_config and 'version_type' in experiment_config:
                run_data['version_type'] = experiment_config.get('version_type')
                logger.info(f"[子任务 {self.sub_task_id}] 添加实验版本类型: {run_data['version_type']}")

            # 验证必需字段
            required_fields = ['sub_task_id', 'app_id', 'device_id', 'case_id', 'status']
            missing_fields = [field for field in required_fields if not run_data.get(field)]
            if missing_fields:
                logger.error(f"[子任务 {self.sub_task_id}] 运行记录缺少必需字段: {', '.join(missing_fields)}")
                return None

            response = await perf_api.upload_sub_task_case_run_detail(**run_data)
            if response and isinstance(response, dict):
                run_id = response.get('id')
                if run_id:
                    logger.info(f"[子任务 {self.sub_task_id}] 创建运行记录成功: run_id={run_id}")
                    return run_id
                else:
                    logger.error("[子任务 {self.sub_task_id}] 响应中未找到run_id")
                    return None
            else:
                logger.error("[子任务 {self.sub_task_id}] 创建运行记录失败")
                return None

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 创建运行记录失败: {str(e)}")
            return None

    async def _update_run_record(self, data: Dict) -> None:
        """更新运行记录"""
        try:
            if not self.run_id:
                logger.error(f"[子任务 {self.sub_task_id}] 更新运行记录失败: run_id 为空")
                return

            # 检查是否已经是终止状态，避免重复更新
            status = data.get('status')
            if status == CaseStatus.CANCELED.value and hasattr(self, '_record_canceled') and self._record_canceled:
                return
            
            # 获取新格式的URL数据
            case_log_tos_urls = data.get('case_log_tos_urls', {})
            perf_data_tos_urls = data.get('perf_data_tos_urls', {})
            screenshot_tos_path = data.get('screenshot_tos_path')
            cpu_profile_tos_path = data.get('cpu_profile_tos_path')
            # 注意：byteio_data_tos_path 已根据API适配指南移除

            # 转换为完整URL格式
            final_case_log_tos_urls = None
            if case_log_tos_urls:
                final_case_log_tos_urls = {}
                for filename, tos_path in case_log_tos_urls.items():
                    final_case_log_tos_urls[filename] = f"https://tosv.byted.org/obj/global-rtc-test-platform/{tos_path}"

            final_perf_data_tos_urls = None
            if perf_data_tos_urls:
                final_perf_data_tos_urls = {}
                for filename, tos_path in perf_data_tos_urls.items():
                    final_perf_data_tos_urls[filename] = f"https://tosv.byted.org/obj/global-rtc-test-platform/{tos_path}"

            run_data = {
                'id': self.run_id,  # 确保提供 id 字段
                'sub_task_id': self.sub_task_id,
                'device_id': self.perf_device.get("id"),
                'app_id': self.perf_app.get("id"),
                'case_id': self.test_case.get("case_id"),
                'case_log_tos_urls': final_case_log_tos_urls,  # 新格式：JSON，包含所有.log文件
                'screenshot_tos_urls': [f"https://tosv.byted.org/obj/global-rtc-test-platform/{url}" for url in screenshot_tos_path] if screenshot_tos_path else [],
                'perf_data_tos_urls': final_perf_data_tos_urls,  # 新格式：JSON，包含所有.json文件
                'cpu_profile_tos_url': f"https://tosv.byted.org/obj/global-rtc-test-platform/{cpu_profile_tos_path}" if cpu_profile_tos_path else None,
                # 移除 byteio_data_tos_url 参数（根据适配指南）
                'status': status,
                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 如果是Libra实验类型，添加version_type字段
            experiment_config = self.perf_app.get('experiment_config', {})
            if experiment_config and 'version_type' in experiment_config:
                run_data['version_type'] = experiment_config.get('version_type')

            # 调用API更新记录
            response = await perf_api.upload_sub_task_case_run_detail(**run_data)
            if response:
                logger.info(f"[子任务 {self.sub_task_id}] 更新运行记录成功: run_id={self.run_id}, status={status}")
                # 标记记录已经被取消
                if status == CaseStatus.CANCELED.value:
                    self._record_canceled = True
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 更新运行记录失败: run_id={self.run_id}")
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 更新运行记录失败: {str(e)}")
            logger.exception(e)

    def _load_data_source(self, data_dir: str, filename: str, source_name: str) -> Optional[dict]:
        """加载单个数据源

        Args:
            data_dir: 数据目录
            filename: 文件名
            source_name: 数据源名称（用于日志）

        Returns:
            Optional[dict]: 成功返回数据字典，失败返回None
        """
        try:
            file_path = os.path.join(data_dir, filename)
            if not os.path.exists(file_path):
                logger.debug(f"[子任务 {self.sub_task_id}] {source_name}数据文件不存在: {file_path}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if not isinstance(data, dict):
                logger.warning(f"[子任务 {self.sub_task_id}] {source_name}数据格式无效: {file_path}")
                return None

            logger.debug(f"[子任务 {self.sub_task_id}] 成功加载{source_name}数据: {len(data)}个指标")
            return data

        except json.JSONDecodeError as e:
            logger.warning(f"[子任务 {self.sub_task_id}] {source_name}数据JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            logger.warning(f"[子任务 {self.sub_task_id}] 加载{source_name}数据失败: {str(e)}")
            return None

    def _aggregate_perf_data(self, data_dir: str) -> dict:
        """聚合多数据源的性能数据

        Args:
            data_dir: 数据目录路径

        Returns:
            dict: 聚合后的性能数据
        """
        aggregated_data = {}

        # 定义数据源配置
        data_sources = [
            # 必需数据源（至少需要一个）
            {'filename': 'ds_avg_perf_data.json', 'name': 'DS性能', 'required': True},
            {'filename': 'gameperf_avg_perf_data.json', 'name': 'GamePerf性能', 'required': True},
            # 可选数据源
            {'filename': 'trace_avg_data.json', 'name': 'Trace', 'required': False},
            {'filename': 'byteio_avg_data.json', 'name': 'ByteIO', 'required': False},
        ]

        required_data_found = False
        successful_sources = []
        failed_sources = []

        # 加载各个数据源
        for source_config in data_sources:
            filename = source_config['filename']
            name = source_config['name']
            is_required = source_config['required']

            data = self._load_data_source(data_dir, filename, name)

            if data:
                # 成功加载数据
                aggregated_data.update(data)
                successful_sources.append(name)
                if is_required:
                    required_data_found = True
            else:
                # 加载失败
                if is_required:
                    failed_sources.append(name)
                else:
                    logger.debug(f"[子任务 {self.sub_task_id}] 可选数据源{name}未找到，跳过")

        # 检查是否有必需数据源
        if not required_data_found:
            logger.error(f"[子任务 {self.sub_task_id}] 未找到任何必需的性能数据源")
            return {}

        # 记录聚合结果
        if successful_sources:
            logger.info(f"[子任务 {self.sub_task_id}] 成功聚合性能数据，数据源: {', '.join(successful_sources)}")
        if failed_sources:
            logger.warning(f"[子任务 {self.sub_task_id}] 部分必需数据源加载失败: {', '.join(failed_sources)}")

        logger.debug(f"[子任务 {self.sub_task_id}] 聚合后数据包含 {len(aggregated_data)} 个指标")
        return aggregated_data

    async def _upload_perf_data(self, is_avg: bool, perf_data: dict = None, data_dir: str = None):
        """上传性能数据

        Args:
            is_avg: 是否是平均值数据
            perf_data: 直接传入的性能数据（优先使用）
            data_dir: 数据目录路径（当perf_data为None时使用）
        """
        try:
            # 获取性能数据
            if perf_data is not None:
                # 使用直接传入的数据
                final_perf_data = perf_data
                logger.debug(f"[子任务 {self.sub_task_id}] 使用直接传入的性能数据")
            elif data_dir is not None:
                # 从数据目录聚合数据
                final_perf_data = self._aggregate_perf_data(data_dir)
                logger.debug(f"[子任务 {self.sub_task_id}] 从数据目录聚合性能数据")
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 未提供性能数据或数据目录")
                return

            if not final_perf_data:
                logger.warning(f"[子任务 {self.sub_task_id}] 性能数据为空，跳过上传")
                return

            if not self.run_id:
                logger.error(f"[子任务 {self.sub_task_id}] 无法获取 case_run_detail_id，上传性能数据失败")
                return

            # 获取平台类型
            platform = self.perf_device.get('sys_type')
            if not platform:
                logger.error(f"[子任务 {self.sub_task_id}] 无法获取设备平台类型，上传性能数据失败")
                return

            # 构建上传数据
            upload_data = {
                'case_run_detail_id': self.run_id,
                'is_avg': is_avg,
                'platform': platform,
                'metrics_data': final_perf_data
            }

            # 上传性能数据
            await perf_api.upload_perf_data(**upload_data)
            logger.info(f"[子任务 {self.sub_task_id}] 性能数据上传成功，包含 {len(final_perf_data)} 个指标")

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 上传性能数据失败: {str(e)}")
            logger.exception(e)

    async def _check_perf_device_battery_level(self, perf_device: dict, power_level: int):
        """检查设备电量
        
        Args:
            perf_device: 性能测试设备信息
            power_level: 电量阈值
        """
        try:
            device_udid = perf_device.get('udid')
            platform = perf_device.get('sys_type')
            
            if not device_udid:
                logger.error(f"[子任务 {self.sub_task_id}] 设备UDID为空")
                return False
                
            # 根据平台类型处理
            if platform == PlatformType.ANDROID.value:
                # Android设备需要先亮屏
                await android_device.turn_on_screen(device_udid)
                battery_level = await android_device.get_battery_level(device_udid)
            elif platform == PlatformType.IOS.value:
                # iOS设备直接获取电量
                battery_level = await ios_device.get_battery_level(device_udid)
            else:
                logger.error(f"[子任务 {self.sub_task_id}] 不支持的设备平台: {platform}")
                return False
                
            # 检查电量
            if battery_level < power_level:
                logger.warning(f"[子任务 {self.sub_task_id}] 设备电量不足: {battery_level}% < {power_level}%")
                if platform == PlatformType.ANDROID.value:
                    await android_device.turn_off_screen(device_udid)
                return False
                
            logger.info(f"[子任务 {self.sub_task_id}] 设备电量充足: {battery_level}% >= {power_level}%")
            return True
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 检查设备电量失败: {str(e)}")
            return False

    async def _add_cpu_profile(self):
        """添加CPU性能分析数据"""
        try:
            if not self.cpu_profile_path:
                logger.warning(f"[子任务 {self.sub_task_id}] CPU性能分析数据路径为空")
                return

            if not os.path.exists(self.cpu_profile_path):
                logger.warning(f"[子任务 {self.sub_task_id}] CPU性能分析数据文件不存在: {self.cpu_profile_path}")
                return

            # 添加profile信息
            profile_info = {
                'profile_id': self.run_id,
                'case_info': self.test_case
            }
            self.profile_analyzer.add_trace(self.cpu_profile_path, profile_info)
            logger.info(f"[子任务 {self.sub_task_id}] 添加CPU性能数据到分析器成功: {self.cpu_profile_path}")

        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 添加CPU性能分析数据失败: {str(e)}")
            logger.exception(e)
            
    async def terminate_case_process(self) -> bool:
        """终止用例进程"""
        try:
            # 设置取消状态
            self._is_cancelled = True
            self._cancel_event.set()
            
            # 终止当前用例进程
            if self._current_case_process:
                # 检查进程是否还在运行
                if self._current_case_process.returncode is None:
                    try:
                        # 先尝试正常终止
                        self._current_case_process.terminate()
                        try:
                            # 等待进程结束
                            await asyncio.wait_for(self._current_case_process.wait(), timeout=30)
                            logger.info(f"[子任务 {self.sub_task_id}] 终止用例进程成功")
                        except asyncio.TimeoutError:
                            # 如果超时，尝试使用更安全的方式强制结束进程
                            logger.warning(f"[子任务 {self.sub_task_id}] 进程未能及时终止，正在强制结束...")
                            try:
                                # 使用subprocess.Popen来执行kill命令
                                kill_cmd = ["pkill", "-P", str(self._current_case_process.pid)]
                                process = await asyncio.create_subprocess_exec(
                                    *kill_cmd,
                                    stdout=asyncio.subprocess.PIPE,
                                    stderr=asyncio.subprocess.PIPE
                                )
                                await process.communicate()
                                
                                # 等待进程结束
                                await asyncio.wait_for(self._current_case_process.wait(), timeout=5)
                            except Exception as e:
                                logger.error(f"[子任务 {self.sub_task_id}] 强制结束进程失败: {str(e)}")
                                return False
                    except Exception as e:
                        logger.error(f"[子任务 {self.sub_task_id}] 终止进程时发生错误: {str(e)}")
                        return False
                    finally:
                        self._current_case_process = None
                        
            return True
            
        except Exception as e:
            logger.error(f"[子任务 {self.sub_task_id}] 终止用例进程失败: {str(e)}")
            return False
        
    async def _cleanup(self) -> None:
        """清理用例资源"""
        return True

    async def cancel(self):
        """取消当前任务"""
        self._is_cancelled = True
        self._cancel_event.set()
        await self.terminate_case_process()
        
    def is_cancelled(self):
        """检查是否已取消"""
        return self._is_cancelled or self._cancel_event.is_set()
        
    def reset_cancel_state(self):
        """重置取消状态"""
        self._is_cancelled = False
        self._cancel_event.clear()

# 创建用例控制器实例
case_control = CaseControl()