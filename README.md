# Global Business Client

## 项目简介

Global Business Client 是一个专业的性能测试客户端应用程序，专门为 Global Business 项目提供全方位的性能测试和监控解决方案。该项目集成了多种性能采集工具、设备管理、自动化测试等核心功能，为移动应用性能测试提供一站式解决方案。

## 🌟 核心特性

### � 一键安装与部署

- **全局命令行工具**
  - `gb` 命令全局可用，支持一键启动
  - 自动代码更新机制，确保使用最新版本
  - 智能环境检测和配置
  - 优雅的错误处理和用户反馈

- **自动化安装**
  - `install.sh` 一键安装脚本，支持 macOS 平台
  - 自动依赖检测和安装（Python、FFmpeg 等）
  - 环境变量自动配置
  - `uninstall.sh` 完整卸载支持

### 🔧 性能测试管理

- **多工具支持**
  - DS 性能采集工具集成
  - GamePerf 性能采集工具支持
  - 统一的性能数据格式和上传机制
  - 智能工具类型识别和切换

- **设备管理**
  - iOS 设备自动检测和配置
  - Android 设备管理支持
  - 串口设备智能管理
  - 设备状态实时监控

- **数据处理**
  - 性能数据自动解析和处理
  - 多种数据格式支持（raw、processed、avg）
  - CPU Profile 数据采集
  - ByteIO 数据统计分析
  - TOS 对象存储自动上传

### � 任务控制系统

- **智能任务管理**
  - 任务状态实时跟踪
  - 自动化测试用例执行
  - 任务队列管理
  - 错误恢复机制

- **应用控制**
  - 应用包自动管理（性能包、基准包、辅助包）
  - 应用安装和卸载自动化
  - 应用状态监控
  - 实验配置管理

### 🎨 用户界面

- **现代化界面**
  - PyQt6 现代化 GUI 框架
  - 自定义主题和样式系统
  - 响应式设计支持
  - 浮动球快捷操作

- **交互体验**
  - 优雅的信号处理（Cmd+C/Ctrl+C 退出）
  - 实时状态反馈
  - 智能错误提示
  - 多窗口管理

## 🛠 技术架构

### 核心技术栈

- **UI 框架**
  - PyQt6 6.8.1：现代化 GUI 开发框架
  - Qt Style Sheets：自定义主题样式系统
  - 异步信号处理机制

- **网络与认证**
  - aiohttp 3.11.14：异步 HTTP 客户端
  - PyJWT 2.10.1：JWT 令牌认证
  - cryptography 42.0.8：加密算法支持

- **数据处理**
  - bytedtos 1.1.19：字节对象存储 SDK
  - bytedbdc 0.2.5：数据库连接组件
  - bytedance.profile_toolkit：性能分析工具

- **媒体处理**
  - FFmpeg：专业媒体处理引擎（支持 Intel/M1/M2）
  - 多格式视频分析工具

- **系统集成**
  - pyserial 3.5：串口设备通信
  - loguru 0.7.3：高级日志系统
  - PyYAML 6.0.2：配置文件处理

## 💻 系统要求

### 基础环境
- **Python**: 3.8+ (推荐 3.9+)
- **FFmpeg**: 最新稳定版
- **内存**: 2GB+ RAM
- **存储**: 1GB+ 可用磁盘空间

### 支持平台
- **macOS**: 10.15+ (Intel/M1/M2/M3) - 主要支持平台
- **其他平台**: 理论支持但未充分测试

### 硬件要求
- **iOS 设备**: 支持 USB 连接的 iPhone/iPad
- **Android 设备**: 支持 ADB 调试的设备
- **串口设备**: 支持标准串口协议的设备

## 📦 安装指南

### 🎯 一键安装（推荐）

```bash
# 1. 克隆项目
<NAME_EMAIL>:bytertc_i18n/global_rtc_client.git
cd global_rtc_client

# 2. 运行一键安装脚本
./install.sh
```

安装脚本将自动完成：
- ✅ 系统环境检测
- ✅ Python 环境配置
- ✅ 依赖包安装
- ✅ FFmpeg 工具安装
- ✅ 全局命令配置
- ✅ 环境变量设置

### 🔧 手动安装

如果需要手动安装，请按以下步骤操作：

```bash
# 1. 检查 Python 版本
python3 --version  # 需要 3.8+

# 2. 创建虚拟环境（推荐）
python3 -m venv .venv
source .venv/bin/activate  # macOS/Linux

# 3. 安装 Python 依赖
pip install -r requirements.txt

# 4. 安装 FFmpeg
brew install ffmpeg  # macOS

# 5. 配置全局命令（可选）
sudo ln -sf "$(pwd)/gb" /usr/local/bin/gb
```

## 🚀 快速开始

### 1. 安装应用

```bash
# 首次使用：一键安装（推荐）
sh install.sh
```

### 2. 启动应用

```bash
# 使用全局命令（推荐）
gb

# 其他启动选项
gb --restart    # 重启应用
gb --debug      # 调试模式
gb --help       # 查看帮助

# 开发模式（不推荐日常使用）
sh run.sh
```

### 3. 首次配置

- **账号授权**: 使用内网账号登录系统
- **设备检测**: 自动检测连接的 iOS/Android 设备
- **工具配置**: 选择性能采集工具类型（DS/GamePerf）

### 4. 核心功能

- **性能测试**: 创建和执行性能测试任务
- **设备管理**: 管理测试设备和串口连接
- **数据分析**: 查看性能数据和分析报告
- **系统设置**: 配置应用参数和偏好设置

## 📁 项目结构

```
global_rtc_client/
├── 📱 app.py                    # 应用程序主入口
├── 🛠️ gb                        # 全局命令行工具
├── 📦 install.sh                # 一键安装脚本
├── 🗑️ uninstall.sh              # 卸载脚本
├── 🏃 run.sh                    # 运行脚本
├── 📋 requirements.txt          # Python 依赖列表
│
├── 🔌 apis/                     # API 接口层
│   ├── auth_api.py             # 用户认证 API
│   ├── perf_api.py             # 性能测试 API
│   └── trace_api.py            # 追踪日志 API
│
├── ⚙️ config/                   # 配置管理
│   ├── config.json             # 默认配置
│   ├── config.local.json       # 本地配置
│   └── constants.py            # 系统常量（PerfToolType、AppType 等）
│
├── 🧠 core/                     # 核心业务逻辑
│   ├── auth_manager.py         # 认证管理器
│   ├── main_manager.py         # 主程序管理器
│   ├── perf_manager.py         # 性能管理器
│   └── settings_manager.py     # 设置管理器
│
├── 🎨 ui/                       # 用户界面模块
│   ├── pages/                  # 主要页面
│   │   ├── auth_window.py      # 登录窗口
│   │   ├── main_window.py      # 主窗口
│   │   ├── perf_window.py      # 性能测试窗口
│   │   └── settings_window.py  # 设置窗口
│   ├── dialogs/                # 对话框组件
│   ├── components/             # 通用 UI 组件
│   ├── styles/                 # 样式和主题
│   └── icons/                  # 图标资源
│
├── 🔧 utils/                    # 工具模块
│   ├── perf/                   # 性能工具集
│   │   ├── app_control.py      # 应用控制器
│   │   ├── case_control.py     # 用例控制器
│   │   ├── task_control.py     # 任务控制器
│   │   ├── perf_parser.py      # 性能数据解析器
│   │   ├── perf_upload.py      # 性能数据上传器
│   │   └── profile_analyzer.py # 性能分析器
│   └── common/                 # 通用工具库
│       ├── file_utils.py       # 文件操作工具
│       ├── system_utils.py     # 系统工具（专注 macOS）
│       ├── network_utils.py    # 网络工具
│       ├── android_device.py   # Android 设备管理
│       ├── ios_device.py       # iOS 设备管理
│       └── log_utils.py        # 日志工具
│
├── 🗂️ data/                     # 数据目录
├── 📊 logs/                     # 日志目录
├── 📁 repos/                    # 仓库目录
└── 🛠️ tools/                    # 外部工具（FFmpeg 等）
```

### 🔍 核心模块详解

#### 📦 安装与部署
- **install.sh**: 智能安装脚本，自动检测环境并配置依赖
- **gb**: 全局命令行工具，支持一键启动和代码更新
- **uninstall.sh**: 完整卸载脚本，清理所有安装文件

#### 🧠 核心业务逻辑
- **perf_manager.py**: 性能测试核心管理器，支持多种性能工具
- **auth_manager.py**: 统一认证管理，支持内网账号登录
- **settings_manager.py**: 配置管理器，支持本地和远程配置

#### 🔧 性能工具集
- **case_control.py**: 用例控制器，支持 DS 和 GamePerf 工具
- **perf_upload.py**: 统一数据上传器，支持多种文件格式
- **profile_analyzer.py**: 性能分析器，支持 CPU Profile 分析

#### 🎨 用户界面
- **现代化设计**: 基于 PyQt6 的响应式界面
- **浮动球操作**: 快捷操作和状态显示
- **多窗口管理**: 支持多个功能窗口同时运行

## 🔄 开发工作流

### 🌿 分支管理策略
```bash
# 1. 创建功能分支
git checkout -b feature/your-feature-name

# 2. 开发并提交
git add .
git commit -m "feat: 添加新功能描述"

# 3. 推送分支
git push origin feature/your-feature-name

# 4. 创建 Merge Request
# 访问 GitLab 创建 MR，等待代码审查

# 5. 合并到主分支
# 审查通过后合并到 master 分支
```

### ⚙️ 配置管理
- **config.json**: 默认配置，包含基础设置
- **config.local.json**: 本地配置，覆盖默认设置
- **constants.py**: 系统常量，包含枚举类型定义
- **动态配置**: 支持运行时配置更新

### 📊 日志系统
- **loguru**: 高级日志框架，支持结构化日志
- **自动轮转**: 按日期自动分割日志文件
- **多级别**: DEBUG、INFO、WARNING、ERROR、CRITICAL
- **文件输出**: 日志自动保存到 `logs/` 目录

### 🧪 测试与调试
```bash
# 调试模式启动
gb --debug

# 查看日志
tail -f logs/$(date +%Y-%m-%d).*.log

# 重启应用
gb --restart
```

## 📝 更新日志

### v2.0.0 (2025-07-14) - 重大架构升级
- 🚀 **新增一键安装机制**: 支持 `install.sh` 自动安装和 `gb` 全局命令
- 🔧 **重构性能管理器**: 支持 DS 和 GamePerf 双工具类型
- 📊 **统一数据上传**: 整合多种性能数据格式的上传机制
- 🎨 **优化用户界面**: 改进浮动球操作和窗口管理
- 🛠️ **简化工具模块**: 转换为模块级函数，提升代码组织性
- 🍎 **专注 macOS 支持**: 移除冗余平台兼容代码，专注 macOS 优化
- 🗑️ **清理废弃代码**: 移除过时脚本和配置文件

### v1.0.0 (2024-10-25) - 初始版本
- 🎯 核心功能实现
- 🏗️ 基础框架搭建
- 🔐 用户认证系统
- 📱 设备管理功能

## 🤝 参与贡献

欢迎提交 Issue 和 Pull Request 来帮助改进项目！

### 📋 贡献指南
1. **Fork 项目** 并创建功能分支
2. **遵循代码规范** 使用中文注释和文档
3. **编写测试用例** 确保功能正常工作
4. **更新文档** 包括 README 和代码注释
5. **提交 MR** 并等待代码审查

### 🔍 代码规范
- 使用中文注释和日志信息
- 遵循 PEP 8 Python 代码规范
- 优先使用类型注解
- 保持函数和类的单一职责

## 📞 联系方式

- **项目负责人**: hejiabei.oxep
- **邮箱**: <EMAIL>
- **项目地址**: [GitLab Repository](https://code.byted.org/bytertc_i18n/global_rtc_client)

## 📄 许可证

本项目为内部项目，仅供 ByteDance Global Business 团队使用。

---

**🎯 让性能测试变得简单高效！**
