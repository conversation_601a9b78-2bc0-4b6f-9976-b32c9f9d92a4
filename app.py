"""
应用程序主入口
"""
import os
import platform
import signal
import subprocess
import sys

import asyncio
from PyQt6.QtCore import QTimer, Qt, QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication, QMessageBox

from core.auth_manager import auth_manager
from ui.pages.auth_window import AuthWindow
from ui.pages.main_window import MainWindow
from utils.common.log_utils import get_logger

logger = get_logger(__name__)

base_path = os.path.dirname(os.path.abspath(__file__))
config_path = os.path.join(base_path, 'config', 'config.json')


class SignalHandler(QObject):
    """信号处理器类，用于处理系统信号"""

    # 定义信号
    shutdown_requested = pyqtSignal()

    def __init__(self):
        super().__init__()
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            # 设置 SIGINT (Ctrl+C) 信号处理器
            signal.signal(signal.SIGINT, self._handle_interrupt)

            # 设置 SIGTERM 信号处理器（用于优雅关闭）
            signal.signal(signal.SIGTERM, self._handle_interrupt)

            # 在 Windows 上设置 SIGBREAK 信号处理器
            if platform.system() == 'Windows':
                signal.signal(signal.SIGBREAK, self._handle_interrupt)

            logger.info("信号处理器设置完成")

        except Exception as e:
            logger.error(f"设置信号处理器失败: {str(e)}")

    def _handle_interrupt(self, signum, frame):
        """处理中断信号"""
        signal_name = {
            signal.SIGINT: "SIGINT (Ctrl+C)",
            signal.SIGTERM: "SIGTERM",
        }.get(signum, f"Signal {signum}")

        if platform.system() == 'Windows' and signum == signal.SIGBREAK:
            signal_name = "SIGBREAK (Ctrl+Break)"

        logger.info(f"接收到信号: {signal_name}，请求优雅退出...")

        # 发射关闭请求信号
        self.shutdown_requested.emit()


class AsyncApplication:
    def __init__(self):
        """初始化应用"""
        try:
            # 首先创建QApplication实例
            self.app = QApplication(sys.argv)

            # 初始化事件循环
            self._setup_event_loop()

            # 初始化窗口引用
            self.auth_window = None
            self.main_window = None

            # 初始化退出标志
            self._shutdown_requested = False
            self._cleanup_completed = False

            # 设置信号处理器
            self._setup_signal_handler()

            # 设置应用样式
            self._setup_app_resources()

        except Exception as e:
            logger.error(f"初始化应用失败: {str(e)}")
            raise

    def run(self) -> None:
        """运行应用"""
        try:
            # 检查是否已经登录
            if auth_manager.is_authenticated:
                logger.info("检测到有效的登录状态，直接进入主界面")
                self._show_main_window()
            else:
                # 显示登录窗口
                self._show_auth_window()

            # 执行Qt应用
            exit_code = self.app.exec()

            # 如果是通过信号处理器请求的关闭，不需要额外处理
            if self._shutdown_requested:
                logger.info("应用程序已通过信号优雅退出")
                return

            # 处理重启请求
            if self._validate_restart_code(exit_code):
                logger.info("正在重启应用...")
                self._restart_process()
            else:
                logger.info(f"用户退出，退出码: {exit_code}")

        except KeyboardInterrupt:
            # 处理 Ctrl+C 中断
            logger.info("接收到键盘中断，执行优雅退出...")
            self._handle_graceful_shutdown()
        except Exception as e:
            logger.error(f"运行应用失败: {str(e)}")
            # 确保在异常情况下也执行清理
            if not self._cleanup_completed:
                self._perform_cleanup()
            sys.exit(1)

    def _setup_event_loop(self):
        """设置事件循环"""
        try:
            # 在Windows上使用ProactorEventLoop
            if platform.system() == 'Windows':
                loop = asyncio.ProactorEventLoop()
                asyncio.set_event_loop(loop)
            self.loop = asyncio.get_event_loop()

            # 设置定时器处理异步事件
            self.async_timer = QTimer()
            self.async_timer.moveToThread(self.app.thread())  # 确保定时器在主线程运行
            self.async_timer.timeout.connect(self._process_async_events)
            self.async_timer.start(1)  # 每1ms检查一次

        except Exception as e:
            logger.error(f"设置事件循环失败: {str(e)}")
            raise

    def _setup_signal_handler(self):
        """设置信号处理器"""
        try:
            self.signal_handler = SignalHandler()
            self.signal_handler.shutdown_requested.connect(self._handle_graceful_shutdown)

            # 设置定时器来定期检查信号
            self.signal_timer = QTimer()
            self.signal_timer.timeout.connect(lambda: None)  # 空操作，只是为了让事件循环能够处理信号
            self.signal_timer.start(100)  # 每100ms检查一次

            logger.info("信号处理器连接完成")
        except Exception as e:
            logger.error(f"设置信号处理器失败: {str(e)}")

    def _handle_graceful_shutdown(self):
        """处理优雅关闭请求"""
        if self._shutdown_requested:
            return  # 避免重复处理

        self._shutdown_requested = True
        logger.info("开始执行优雅关闭流程...")

        try:
            # 显示状态消息
            self._show_shutdown_message()

            # 执行清理操作
            self._perform_cleanup()

            # 关闭应用程序
            self._close_application()

        except Exception as e:
            logger.error(f"优雅关闭过程中发生错误: {str(e)}")
            # 强制退出
            sys.exit(1)

    def _show_shutdown_message(self):
        """显示关闭消息"""
        try:
            # 如果有主窗口，在状态栏显示消息
            if self.main_window and hasattr(self.main_window, 'statusBar'):
                self.main_window.statusBar().showMessage("正在优雅退出应用程序...", 2000)

            # 处理待处理的事件
            self.app.processEvents()

        except Exception as e:
            logger.error(f"显示关闭消息失败: {str(e)}")

    def _perform_cleanup(self):
        """执行清理操作"""
        if self._cleanup_completed:
            return

        try:
            logger.info("执行应用程序清理操作...")

            # 保存应用状态
            self._save_application_state()

            # 关闭异步事件循环
            self._cleanup_event_loop()

            # 关闭窗口
            self._close_windows()

            # 清理资源
            self._cleanup_resources()

            self._cleanup_completed = True
            logger.info("清理操作完成")

        except Exception as e:
            logger.error(f"清理操作失败: {str(e)}")

    def _save_application_state(self):
        """保存应用程序状态"""
        try:
            # 这里可以添加保存用户设置、窗口位置等逻辑
            logger.info("保存应用程序状态...")

            # 如果有认证管理器，保存认证状态
            if hasattr(auth_manager, 'save_state'):
                auth_manager.save_state()

        except Exception as e:
            logger.error(f"保存应用程序状态失败: {str(e)}")

    def _cleanup_event_loop(self):
        """清理事件循环"""
        try:
            # 停止信号定时器
            if hasattr(self, 'signal_timer') and self.signal_timer:
                self.signal_timer.stop()

            # 停止异步事件定时器
            if hasattr(self, 'async_timer') and self.async_timer:
                self.async_timer.stop()

            if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
                # 取消所有待处理的任务
                pending_tasks = [task for task in asyncio.all_tasks(self.loop) if not task.done()]
                if pending_tasks:
                    logger.info(f"取消 {len(pending_tasks)} 个待处理的异步任务...")
                    for task in pending_tasks:
                        task.cancel()

                self.loop.stop()

        except Exception as e:
            logger.error(f"清理事件循环失败: {str(e)}")

    def _close_windows(self):
        """关闭所有窗口"""
        try:
            # 关闭主窗口
            if self.main_window:
                logger.info("关闭主窗口...")
                self.main_window.close()
                self.main_window = None

            # 关闭认证窗口
            if self.auth_window:
                logger.info("关闭认证窗口...")
                self.auth_window.close()
                self.auth_window = None

        except Exception as e:
            logger.error(f"关闭窗口失败: {str(e)}")

    def _cleanup_resources(self):
        """清理其他资源"""
        try:
            # 这里可以添加其他资源清理逻辑
            # 例如：关闭数据库连接、网络连接等
            logger.info("清理其他资源...")

        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")

    def _close_application(self):
        """关闭应用程序"""
        try:
            logger.info("正在关闭应用程序...")

            # 处理剩余的事件
            self.app.processEvents()

            # 退出应用程序
            self.app.quit()

        except Exception as e:
            logger.error(f"关闭应用程序失败: {str(e)}")
            sys.exit(1)

    def _validate_restart_code(self, exit_code: int) -> bool:
        """验证重启代码是否合法
        
        Args:
            exit_code: 退出码
            
        Returns:
            bool: 是否是合法的重启代码
        """
        # 定义合法的重启代码
        VALID_RESTART_CODES = {42}  # 可以添加其他合法的重启代码
        return exit_code in VALID_RESTART_CODES

    def _restart_process(self) -> None:
        """重启进程"""
        try:
            # 获取当前进程的可执行文件路径
            executable = sys.executable
            script_path = os.path.abspath(sys.argv[0])

            # 验证可执行文件和脚本路径
            if not os.path.exists(executable) or not os.path.exists(script_path):
                raise ValueError("Invalid executable or script path")

            # 构建命令列表
            cmd = [executable, script_path]

            # 使用安全的方式启动新进程
            subprocess.Popen(
                cmd,
                shell=False,  # 禁用shell执行
                close_fds=True,  # 关闭所有文件描述符
                start_new_session=True  # 在新会话中启动
            )

            # 退出当前进程
            sys.exit(0)

        except Exception as e:
            logger.error(f"重启进程失败: {str(e)}")
            sys.exit(1)

    def _setup_app_resources(self) -> None:
        """设置应用资源"""
        try:
            # 设置应用信息
            self._setup_app_info()

            # 设置应用图标
            self._setup_app_icon()

            # 设置样式表
            self._setup_app_style()

        except Exception as e:
            logger.error(f"设置应用资源失败: {str(e)}")

    def _setup_app_info(self) -> None:
        """设置应用信息"""
        try:
            self.app.setApplicationName("Global Business Client")
            self.app.setApplicationDisplayName("Global Business Client")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("Global Business Team")
            self.app.setOrganizationDomain("global-business.com")

            logger.debug("应用信息设置完成")

        except Exception as e:
            logger.error(f"设置应用信息失败: {str(e)}")

    def _setup_app_icon(self) -> None:
        """设置应用图标"""
        try:
            from utils.common.icon_utils import icon_manager

            # 获取应用图标
            app_icon = icon_manager.get_app_icon()

            # 如果图标为空，创建一个默认图标
            if app_icon.isNull():
                logger.info("创建默认应用图标...")
                app_icon = icon_manager.create_app_icon_from_text("GB", 512)

            # 设置应用图标
            if not app_icon.isNull():
                self.app.setWindowIcon(app_icon)
                logger.info("应用图标设置完成")

                # 输出图标信息
                icon_info = icon_manager.get_icon_info()
                logger.debug(f"图标信息: {icon_info}")
            else:
                logger.warning("无法设置应用图标")

        except Exception as e:
            logger.error(f"设置应用图标失败: {str(e)}")

    def _setup_app_style(self) -> None:
        """设置应用样式"""
        try:
            from ui.styles import WindowStyle
            self.app.setStyleSheet(WindowStyle.get_window_base_style())

        except Exception as e:
            logger.error(f"设置应用样式失败: {str(e)}")

    def _center_window(self, window) -> None:
        """使窗口居中显示"""
        try:
            screen = QApplication.primaryScreen()
            if not screen:
                return
            screen_geometry = screen.availableGeometry()
            x = (screen_geometry.width() - window.width()) // 2
            y = (screen_geometry.height() - window.height()) // 2
            window.move(x, y)
        except Exception as e:
            logger.error(f"窗口居中显示失败: {str(e)}")

    def _show_auth_window(self):
        """显示登录窗口"""
        try:
            # 创建新的登录窗口
            if not self.auth_window:
                self.auth_window = AuthWindow()  # 不传递任何参数
                self.auth_window.auth_success.connect(self._on_auth_success)
            
            # 显示登录窗口
            self.auth_window.show()
            
        except Exception as e:
            logger.error(f"显示登录窗口失败: {str(e)}")
            if self.main_window:
                self.main_window.window_controls.show_error_message("错误", "显示登录窗口失败")

    def _on_auth_success(self):
        """处理认证成功事件"""
        try:
            # 隐藏登录窗口
            if self.auth_window:
                self.auth_window.hide()
                self.auth_window = None

            # 显示主窗口
            self._show_main_window()
            
        except Exception as e:
            logger.error(f"处理认证成功事件失败: {str(e)}")

    def _process_async_events(self):
        """处理异步事件"""
        try:
            if self.loop and not self.loop.is_closed():
                self.loop.stop()
                self.loop.run_forever()
        except Exception as e:
            logger.error(f"处理异步事件失败: {str(e)}")

    def _show_main_window(self):
        """显示主窗口"""
        try:
            # 创建主窗口前确保样式已正确加载
            if not hasattr(self, 'main_window') or not self.main_window:
                self.main_window = MainWindow()

            # 显示窗口
            self.main_window.show()
            self._center_window(self.main_window)
            
        except Exception as e:
            logger.error(f"显示主窗口失败: {str(e)}")
            logger.exception(e)  # 打印完整的堆栈跟踪
            self._show_auth_window()


def main():
    """应用程序入口函数"""
    try:
        # 设置信号处理，确保能够处理 Ctrl+C
        signal.signal(signal.SIGINT, signal.SIG_DFL)

        # 在创建 QApplication 之前设置环境变量
        if platform.system() == 'Darwin':
            # 完全禁用 Qt 日志输出中的警告信息
            os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false;qt.qpa.drawing=false'
            # 优化 macOS 图层渲染
            os.environ['QT_MAC_WANTS_LAYER'] = '1'
            os.environ['QT_QPA_PLATFORM'] = 'cocoa'
            # 禁用原生控件兄弟窗口创建
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)
            # 启用 Metal 渲染器
            os.environ['QT_MAC_WANTS_METAL'] = '1'

        # 设置高DPI支持 - 使用Qt6兼容的方式
        QApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
        )

        # 启用自动缩放
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)

        # 启用 Mac 风格
        if hasattr(Qt, 'AA_MacDontSwapCtrlAndMeta'):
            QApplication.setAttribute(Qt.ApplicationAttribute.AA_MacDontSwapCtrlAndMeta)

        app = AsyncApplication()
        return app.run()
    except KeyboardInterrupt:
        logger.info("应用程序被用户中断")
        return 0
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        return 1


if __name__ == "__main__":
    # 设置环境变量以支持高DPI
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    os.environ["QT_ENABLE_HIGHDPI_SCALING"] = "1"
    
    # 设置日志规则来抑制不必要的警告
    os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'
    
    main()
