"""
设置页面样式
"""
from ui.styles.base.colors import Colors
from ui.styles.base.fonts import Fonts

class SettingsStyle:
    @classmethod
    def get_container_style(cls) -> str:
        """获取容器样式"""
        return f"""
            QWidget {{
                background: {Colors.BACKGROUND};
            }}
        """
    
    @classmethod
    def get_nav_container_style(cls) -> str:
        """获取导航容器样式"""
        return f"""
            QWidget {{
                background: {Colors.SURFACE};
                border-right: 1px solid {Colors.BORDER};
                min-width: 220px;
                max-width: 220px;
            }}
        """
    
    @classmethod
    def get_nav_button_style(cls, selected: bool = False) -> str:
        """获取导航按钮样式"""
        if selected:
            return f"""
                QPushButton {{
                    text-align: left;
                    padding: 12px 16px;
                    border: none;
                    border-radius: 8px;
                    color: white;
                    background: {Colors.PRIMARY};
                    font-size: 14px;
                    font-weight: 600;
                }}
                QPushButton:hover {{
                    background: {Colors.PRIMARY_HOVER};
                }}
                QPushButton::icon {{
                    margin-right: 8px;
                    color: white;
                }}
            """
        return f"""
            QPushButton {{
                text-align: left;
                padding: 12px 16px;
                border: none;
                border-radius: 8px;
                color: {Colors.TEXT_PRIMARY};
                background: transparent;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background: {Colors.SURFACE_LIGHT};
                color: {Colors.PRIMARY};
            }}
            QPushButton::icon {{
                margin-right: 8px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QPushButton:hover::icon {{
                color: {Colors.PRIMARY};
            }}
        """
    
    @classmethod
    def get_content_container_style(cls) -> str:
        """获取内容容器样式"""
        return f"""
            QWidget {{
                background: {Colors.BACKGROUND};
            }}
            QScrollArea {{
                border: none;
                background: transparent;
            }}
            QScrollArea > QWidget > QWidget {{
                background: transparent;
            }}
            QScrollBar:vertical {{
                border: none;
                background: {Colors.SURFACE};
                width: 8px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background: {Colors.BORDER};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {Colors.PRIMARY}50;
            }}
            QScrollBar::add-line:vertical {{
                height: 0px;
            }}
            QScrollBar::sub-line:vertical {{
                height: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
        """
    
    @classmethod
    def get_section_title_style(cls) -> str:
        """获取分区标题样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 20px;
                font-weight: 600;
                padding: 24px 0 16px 0;
            }}
        """
    
    @classmethod
    def get_section_container_style(cls) -> str:
        """获取分区容器样式"""
        return f"""
            QFrame {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 12px;
                padding: 4px;
            }}
            QFrame:hover {{
                border-color: {Colors.PRIMARY}50;
                background: {Colors.SURFACE}F8;
            }}
        """
    
    @classmethod
    def get_group_title_style(cls) -> str:
        """获取组标题样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 8px;
            }}
        """
    
    @classmethod
    def get_form_label_style(cls) -> str:
        return f"""
            QLabel {{
                color: {Colors.TEXT_SECONDARY};
                font-size: 14px;
                padding: 0 32px 0 0;
                min-width: 60px;
                max-width: 160px;
                font-weight: 500;
                text-align: left;
            }}
        """
    
    @classmethod
    def get_form_field_container_style(cls) -> str:
        """获取表单字段容器样式"""
        return """
            QWidget {
                background: transparent;
                min-width: 200px;
                max-width: 300px;
            }
            QLabel {
                min-width: 120px;
                max-width: 120px;
                margin-right: 16px;
                qproperty-alignment: 'AlignVCenter | AlignLeft';
                color: #424242;
            }
            QSpinBox {
                min-width: 200px;
                max-width: 300px;
            }
            QLineEdit {
                min-width: 200px;
                max-width: 300px;
            }
            QComboBox {
                min-width: 200px;
                max-width: 300px;
            }
        """
    
    @classmethod
    def get_description_style(cls) -> str:
        return f"""
            QLabel {{
                color: {Colors.TEXT_SECONDARY};
                font-size: 13px;
                padding: 4px 0 0 0;
                font-weight: 400;
            }}
        """
    
    @classmethod
    def get_divider_style(cls) -> str:
        """获取分割线样式"""
        return f"""
            QFrame {{
                border: none;
                background: {Colors.BORDER};
                max-height: 1px;
                margin: 8px 0;
            }}
        """

    @classmethod
    def get_button_container_style(cls) -> str:
        """获取按钮容器样式"""
        return f"""
            QWidget {{
                background: {Colors.SURFACE};
                border-top: 1px solid {Colors.BORDER};
                padding: 16px 32px;
            }}
            QPushButton {{
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: 600;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: {Colors.PRIMARY}15;
            }}
        """ 