<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="32" viewBox="0 0 200 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 定义渐变 -->
    <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#0095FF"/>
            <stop offset="100%" style="stop-color:#0088FF"/>
        </linearGradient>
    </defs>
    
    <!-- 左侧图标 -->
    <g transform="translate(0, 0)">
        <!-- 背景圆形 -->
        <circle cx="16" cy="16" r="16" fill="url(#logoGradient)"/>
        
        <!-- 播放图标 - 稍微放大并居中 -->
        <path d="M11 9.5L23 16L11 22.5V9.5Z" fill="white"/>
        
        <!-- 优化的信号波纹 -->
        <path d="M24 12C26 12 28 14 28 16C28 18 26 20 24 20" 
              stroke="white" stroke-width="1.8" 
              stroke-linecap="round" 
              opacity="0.7"
              filter="blur(0.2px)"/>
              
        <path d="M26 8C30 8 32 12 32 16C32 20 30 24 26 24" 
              stroke="white" stroke-width="1.8" 
              stroke-linecap="round" 
              opacity="0.4"
              filter="blur(0.3px)"/>
    </g>
    
    <!-- 右侧文字 - 调整间距和字重 -->
    <g transform="translate(40, 0)">
        <text x="0" y="20" 
              font-family="Arial" 
              font-size="20" 
              font-weight="600" 
              letter-spacing="0.3px"
              fill="#2C3E50">
            Global RTC
        </text>
    </g>
</svg> 