"""
认证窗口
"""

import asyncio
import platform
from PyQt6.QtCore import (
    Qt, pyqtSignal, QTimer, QRectF, QMetaObject, pyqtSlot,
    Q_ARG, QSize
)
from PyQt6.QtGui import QColor, QPainterPath, QRegion, QIcon
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QLabel,
    QFrame,
    QGraphicsDropShadowEffect,
    QMessageBox
)

from core.auth_manager import auth_manager
from ui.styles import (
    Colors,
    ButtonStyle,
    InputStyle,
    LabelStyle,
    WindowStyle,
    FrameStyle,
    DialogStyle
)
from utils.common.log_utils import get_logger

logger = get_logger(__name__)


class AuthWindow(QMainWindow):
    """认证窗口"""

    # 定义信号
    auth_success = pyqtSignal()  # 添加认证成功信号

    # 1. 初始化相关
    def __init__(self):
        super().__init__()
        # 使用导入的单例
        self.auth_manager = auth_manager
        
        # 初始化必要的属性
        self._business_list_loaded = False
        self.search_timer = QTimer()
        self.search_timer.setInterval(500)
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self._do_search)
        self.current_search_text = ""
        self.last_search_result = []

        # 设置窗口属性
        self.setWindowTitle("欢迎登录")
        if platform.system() == 'Darwin':
            # macOS 特定的窗口设置
            self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowStaysOnTopHint)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
            self.setAttribute(Qt.WidgetAttribute.WA_MacShowFocusRect, False)
            # 设置窗口图层
            self.setAttribute(Qt.WidgetAttribute.WA_DontCreateNativeAncestors)
        else:
            self.setWindowFlags(Qt.WindowType.Window)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 设置窗口大小为行业标准尺寸
        self.setFixedSize(380, 560)  # 黄金比例的登录窗口尺寸
        
        # 初始化UI
        self.setup_ui()
        self.setup_connections()
        
        # 应用样式
        self.setStyleSheet(WindowStyle.get_auth_window_style())

        # 加载业务列表
        self._init_business_list()

    def _init_business_list(self):
        """初始化业务列表"""
        try:
            loop = asyncio.get_event_loop()
            if not loop or loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            if loop.is_running():
                loop.create_task(self._load_business_list())
            else:
                loop.run_until_complete(self._load_business_list())
        except Exception as e:
            logger.error(f"初始化业务列表失败: {str(e)}")
            self.business_combo.clear()
            self.business_combo.addItem("加载失败，请重试", None)
            self.business_combo.setEnabled(True)

    def setup_ui(self):
        """设置UI"""
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建logo容器
        logo_container = QFrame()
        logo_container.setFixedHeight(200)
        logo_layout = QVBoxLayout(logo_container)
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_layout.setSpacing(16)

        # 添加logo
        logo_label = QLabel()
        icon = QIcon("ui/icons/logo.svg")
        # 设置固定的正方形尺寸
        size = QSize(96, 96)  # 96x96 的正方形
        pixmap = icon.pixmap(size, QIcon.Mode.Normal, QIcon.State.Off)
        logo_label.setFixedSize(size)  # 设置标签大小为固定尺寸
        logo_label.setPixmap(pixmap)
        logo_label.setScaledContents(True)  # 让图片自适应标签大小
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_layout.addWidget(logo_label, alignment=Qt.AlignmentFlag.AlignCenter)

        # 添加标题
        title_label = QLabel("欢迎登录")
        title_label.setObjectName("titleLabel")
        title_label.setStyleSheet(LabelStyle.get_auth_title_style())
        logo_layout.addWidget(title_label, alignment=Qt.AlignmentFlag.AlignCenter)

        # 添加副标题
        subtitle_label = QLabel("流媒体国际客户端")
        subtitle_label.setStyleSheet(LabelStyle.get_auth_subtitle_style())
        logo_layout.addWidget(subtitle_label, alignment=Qt.AlignmentFlag.AlignCenter)

        main_layout.addWidget(logo_container)

        # 创建表单容器
        form_container = QFrame()
        form_container.setStyleSheet(FrameStyle.get_auth_form_style())
        form_layout = QVBoxLayout(form_container)
        form_layout.setContentsMargins(32, 32, 32, 32)
        form_layout.setSpacing(24)

        # 业务选择
        business_label = QLabel("业务")
        business_label.setStyleSheet(LabelStyle.get_auth_label_style())
        form_layout.addWidget(business_label)

        self.business_combo = QComboBox()
        self.business_combo.setStyleSheet(InputStyle.get_auth_combobox_style())
        self.business_combo.setPlaceholderText("请选择业务")
        form_layout.addWidget(self.business_combo)

        # 用户名输入
        username_label = QLabel("用户名")
        username_label.setStyleSheet(LabelStyle.get_auth_label_style())
        form_layout.addWidget(username_label)

        self.username_combo = QComboBox()
        self.username_combo.setStyleSheet(InputStyle.get_auth_combobox_style())
        self.username_combo.setEditable(True)
        self.username_combo.setPlaceholderText("请输入用户名")
        form_layout.addWidget(self.username_combo)

        # 登录按钮
        self.auth_button = QPushButton("登录")
        self.auth_button.setStyleSheet(ButtonStyle.get_login_button_style())
        self.auth_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.auth_button.clicked.connect(self._handle_login)  # 添加点击事件处理
        form_layout.addWidget(self.auth_button)

        main_layout.addWidget(form_container, 1)

        # 添加版权信息
        copyright_label = QLabel("© 2024 ByteDance Inc. All rights reserved.")
        copyright_label.setStyleSheet(LabelStyle.get_auth_copyright_style())
        copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(copyright_label)

        # 设置窗口属性
        self.setFixedSize(480, 640)
        self.setWindowTitle("欢迎登录")

        # 设置窗口样式
        self.setStyleSheet(WindowStyle.get_auth_window_style())

    def _handle_login(self):
        """处理登录"""
        try:
            username = self.username_combo.currentText().strip()
            service = self.business_combo.currentText().strip()
            
            if not username or not service:
                QMessageBox.warning(self, "提示", "请输入用户名和选择业务")
                return
            
            # 创建事件循环并执行异步认证
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            if loop.is_running():
                loop.create_task(self._async_login(username, service))
            else:
                loop.run_until_complete(self._async_login(username, service))
                
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"登录失败: {str(e)}")

    async def _async_login(self, username: str, service: str):
        """异步执行登录
        
        Args:
            username: 用户名
            service: 业务名称
        """
        try:
            # 调用认证管理器进行认证
            success = await self.auth_manager.authenticate(username, service)
            
            if success:
                # 发送认证成功信号
                self.auth_success.emit()
                self.hide()  # 隐藏登录窗口
            else:
                QMessageBox.warning(self, "认证失败", "请检查用户名和业务是否正确")
                
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"登录失败: {str(e)}")

    def setup_connections(self):
        """设置信号连接"""
        self.username_combo.currentTextChanged.connect(self._handle_text_changed)
        self.username_combo.lineEdit().textEdited.connect(self._handle_text_edited)
        self.username_combo.activated.connect(self._handle_item_selected)
        self.business_combo.currentIndexChanged.connect(self._handle_business_changed)

    def _handle_text_changed(self, text: str) -> None:
        """理输入文本变化"""
        self._update_auth_button_state()

    def _handle_text_edited(self, text: str) -> None:
        """处理编辑框文本编辑"""
        try:
            if not text:
                self.username_combo.clear()
                self.username_combo.hidePopup()  # 隐藏下拉框
                return

            # 如果文本长度小于2，不触发搜索
            if len(text) < 2:
                return

            # 如果当前文本与上次搜索文本相同，不重复搜索
            if text == self.current_search_text:
                return

            # 更新当前搜索文本
            self.current_search_text = text

            # 重启搜索定时器
            self.search_timer.stop()
            self.search_timer.start()

        except Exception as e:
            logger.error(f"处理文本编辑失败: {str(e)}")

    def _handle_item_selected(self, index: int) -> None:
        """处理选项被选中"""
        if index >= 0:
            # 停止搜索定时器
            self.search_timer.stop()
            # 隐藏下拉框
            self.username_combo.hidePopup()
            # 更新按钮状态
            self._update_auth_button_state()

    def _do_search(self) -> None:
        """执搜索"""
        try:
            loop = asyncio.get_event_loop()
            if loop and not loop.is_closed():
                loop.create_task(self._search_users(self.current_search_text))
        except Exception as e:
            logger.error(f"创建搜索任务败: {str(e)}")

    async def _search_users(self, text: str) -> None:
        """搜索用户"""
        if not text:
            self.username_combo.clear()
            self.username_combo.hidePopup()  # 隐藏下拉框
            return

        try:
            # 使用 auth_manager 搜索用户
            users = await self.auth_manager.search_users(text)

            if users:
                # 保存当前文本
                current_text = self.username_combo.currentText()

                # 更新下拉列表
                self.username_combo.clear()
                for username in users:  # users 现在是字符串列表
                    if username:  # 确保用户名不为空
                        self.username_combo.addItem(username)

                # 恢复当前文本
                self.username_combo.setCurrentText(current_text)

                # 只在有搜索结果且用户正在输入时显示下拉列表
                if self.username_combo.hasFocus():
                    self.username_combo.showPopup()

        except Exception as e:
            logger.error(f"搜索用户失败: {str(e)}")

    def _handle_business_changed(self, index: int) -> None:
        """处理业务选择变化"""
        self._update_auth_button_state()

    def _update_auth_button_state(self) -> None:
        """更新认证按钮状态"""
        username = self.username_combo.currentText().strip()
        business_id = self.business_combo.currentData()
        self.auth_button.setEnabled(bool(username) and business_id is not None)

    async def _load_business_list(self) -> None:
        """加载业务列表"""
        try:
            # 如果已经加载过，直接返回
            if self._business_list_loaded:
                return

            # 显示加载状态
            self.business_combo.clear()
            self.business_combo.addItem("正在加载业务列表...")
            self.business_combo.setEnabled(False)

            # 获取业务列表
            businesses = await self.auth_manager.get_business_list()

            def update_ui():
                self.business_combo.clear()
                if not businesses:
                    self.business_combo.addItem("暂无可用业务", None)
                else:
                    # 添加业务选项
                    for business in businesses:
                        # 从响应中获取业务信息
                        business_id = business.get('id')
                        business_name = business.get('business_name', '')  # 使用 business_name 字段
                        description = business.get('description', '')
                        
                        # 构建显示文本
                        display_text = business_name
                        if description:
                            display_text += f" - {description}"
                        
                        # 添加到下拉框
                        self.business_combo.addItem(display_text, business_id)

                        # 设置工具提示
                        index = self.business_combo.count() - 1
                        tooltip = f"ID: {business_id}\n名称: {business_name}"
                        if description:
                            tooltip += f"\n描述: {description}"
                        self.business_combo.setItemData(index, tooltip, Qt.ItemDataRole.ToolTipRole)

                self.business_combo.setEnabled(True)
                self._business_list_loaded = True

            # 在主线程中更新UI
            QMetaObject.invokeMethod(self,
                                     "_update_business_combo",
                                     Qt.ConnectionType.QueuedConnection,
                                     Q_ARG(object, update_ui))

        except Exception as e:
            logger.error(f"加载业务列表失败: {str(e)}")

            def show_error():
                self.business_combo.clear()
                self.business_combo.addItem("加载失败，请重试", None)
                self.business_combo.setEnabled(True)

            QMetaObject.invokeMethod(self,
                                     "_update_business_combo",
                                     Qt.ConnectionType.QueuedConnection,
                                     Q_ARG(object, show_error))

    @pyqtSlot(object)
    def _update_business_combo(self, update_func):
        """在主线程中更新下拉框"""
        update_func()

    async def handle_auth_success(self, user_info):
        """处理认证成功事件"""
        try:
            # 确保 user_info 是字典类型
            if isinstance(user_info, str):
                username = user_info
                user_info = {
                    'username': username,
                    'name': username,
                    'business_id': self.business_combo.currentData(),
                    'business_name': self.business_combo.currentText()
                }

            # 确保用户信息已更新后再发送信号
            current_user = self.auth_manager.get_current_user()
            if current_user:
                self.auth_success.emit()
                logger.info(f"用户 {current_user.get('username')} 认证成功")
            else:
                logger.error("认证成功但无法获取用户信息")

        except Exception as e:
            logger.error(f"处理认证成功事件失败: {str(e)}")

    def logout(self) -> None:
        """登出时清除缓存"""
        self._business_list_loaded = False  # 重置标志位
        self.auth_manager.clear_business_list_cache()  # 清除缓存
