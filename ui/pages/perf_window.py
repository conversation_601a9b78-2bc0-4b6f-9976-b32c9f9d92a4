"""
性能自动化窗口
"""
import asyncio
import os

from PyQt6.QtCore import Qt, QTimer, QSize
from PyQt6.QtGui import QColor, QIcon, QPainter
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QCheckBox, QFrame, QGraphicsDropShadowEffect, QGridLayout, QDialog
)
from config.constants import ServiceStatus
from core.main_manager import main_manager
from core.perf_manager import perf_manager
from ui.dialogs.serial_dialog import SerialDialog

from ui.styles import (
    ButtonStyle,
    WindowStyle,
    LabelStyle,
    CheckboxStyle,
    FrameStyle
)
from utils.common.log_utils import get_logger

logger = get_logger(__name__)


class PerfWindow(QWidget):
    """性能自动化窗口"""

    # 1. 初始化相关
    def __init__(self, parent=None):
        super().__init__(parent)
        self.perf_manager = perf_manager

        # 设置窗口标题
        self.setWindowTitle("性能")

        # 应用现代风格
        self.setStyleSheet(WindowStyle.get_window_base_style())

        self.setup_ui()
        self.setup_connections()
        self._start_status_update()

        # 添加串口配置按钮状态跟踪
        self._update_serial_config_button_state()

    def setup_ui(self) -> None:
        """设置UI组件"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(20)

        # 创建服务管理卡片
        services_card = QFrame()
        services_card.setObjectName("servicesCard")
        services_card.setStyleSheet(FrameStyle.get_perf_window_card_style())

        services_layout = QVBoxLayout(services_card)
        services_layout.setContentsMargins(24, 24, 24, 24)
        services_layout.setSpacing(20)

        # 标题区域
        title_layout = QHBoxLayout()
        title = QLabel("性能服务管理")
        title.setStyleSheet(LabelStyle.get_perf_window_title_style())
        title_layout.addWidget(title)
        title_layout.addStretch()

        # 串口配置按钮
        self.serial_config_btn = QPushButton("串口配置")
        icon = QIcon("ui/icons/actions/serial.svg")
        pixmap = icon.pixmap(20, 20)
        # 创建一个画家来修改图标颜色
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.serial_config_btn.setIcon(QIcon(pixmap))
        self.serial_config_btn.setIconSize(QSize(16, 16))
        self.serial_config_btn.setStyleSheet(ButtonStyle.get_serial_config_button_style())
        title_layout.addWidget(self.serial_config_btn)

        services_layout.addLayout(title_layout)

        # 创建服务卡片网格
        grid_layout = QGridLayout()
        grid_layout.setSpacing(12)

        services = [
            ("screen_lock", "屏幕锁定防护", "lock"),
            ("client", "性能机房", "desktop"),
            ("device", "性能设备监控", "device"),
            ("task", "性能任务轮询", "task"),
            ("request", "性能请求接收", "request")  # 添加新的服务卡片
        ]

        # 调整布局为两行三列
        for i, (service_id, service_name, icon) in enumerate(services):
            row = i // 3  # 每行3个卡片
            col = i % 3   # 列索引为0,1,2
            service_card = self._create_service_card(service_id, service_name, icon)
            grid_layout.addWidget(service_card, row, col)

        services_layout.addLayout(grid_layout)

        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet(FrameStyle.get_separator_style())
        services_layout.addWidget(separator)

        # 一键操作按钮
        self.toggle_all_button = QPushButton("一键启动")
        icon = QIcon("ui/icons/actions/power.svg")
        pixmap = icon.pixmap(20, 20)
        # 创建一个画家来修改图标颜色
        painter = QPainter(pixmap)
        painter.setCompositionMode(QPainter.CompositionMode.CompositionMode_SourceIn)
        painter.fillRect(pixmap.rect(), QColor("white"))
        painter.end()
        self.toggle_all_button.setIcon(QIcon(pixmap))
        self.toggle_all_button.setIconSize(QSize(16, 16))
        self.toggle_all_button.setStyleSheet(ButtonStyle.get_one_key_start_button_style())
        services_layout.addWidget(self.toggle_all_button, 0, Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(services_card)
        layout.addStretch()

        # 添加阴影效果
        shadow_style = FrameStyle.get_shadow_effect_style()
        shadow = QGraphicsDropShadowEffect(services_card)
        shadow.setBlurRadius(shadow_style['blur_radius'])
        shadow.setXOffset(shadow_style['x_offset'])
        shadow.setYOffset(shadow_style['y_offset'])
        shadow.setColor(shadow_style['color'])
        services_card.setGraphicsEffect(shadow)

    def _create_service_card(self, service_id: str, service_name: str, icon: str) -> QFrame:
        """创建服务卡片"""
        card = QFrame()
        card.setObjectName(f"{service_id}_card")
        card.setStyleSheet(FrameStyle.get_perf_window_card_style())

        layout = QVBoxLayout(card)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 标题和图标
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)

        # 服务图标
        icon_label = QLabel()
        icon_label.setPixmap(QIcon(f"ui/icons/actions/{icon}.svg").pixmap(20, 20))
        header_layout.addWidget(icon_label)

        # 服务名称
        name_label = QLabel(service_name)
        name_label.setStyleSheet(LabelStyle.get_service_name_label_style())
        header_layout.addWidget(name_label)
        header_layout.addStretch()

        # 选择框
        checkbox = QCheckBox()
        checkbox.setStyleSheet(CheckboxStyle.get_perf_window_checkbox_style())
        setattr(self, f"{service_id}_checkbox", checkbox)
        header_layout.addWidget(checkbox)

        layout.addLayout(header_layout)

        # 状态标签
        status = QLabel("状态: 未运行")
        status.setProperty("status", "stopped")
        status.setStyleSheet(LabelStyle.get_stats_label_style("stopped"))
        status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        setattr(self, f"{service_id}_status", status)
        layout.addWidget(status)

        return card

    def setup_connections(self) -> None:
        """设置信号连接"""
        if not self.perf_manager:
            logger.error("性能管理器未初始化")
            return

        # 连接服务状态切换事件
        self.screen_lock_checkbox.stateChanged.connect(
            lambda state: self._handle_service_toggle('screen_lock', bool(state))
        )
        self.client_checkbox.stateChanged.connect(
            lambda state: self._handle_service_toggle('client', bool(state))
        )
        self.device_checkbox.stateChanged.connect(
            lambda state: self._handle_service_toggle('device', bool(state))
        )
        self.task_checkbox.stateChanged.connect(
            lambda state: self._handle_service_toggle('task', bool(state))
        )
        self.request_checkbox.stateChanged.connect(
            lambda state: self._handle_service_toggle('request', bool(state))
        )

        # 连接一键操作按钮
        self.toggle_all_button.clicked.connect(self._handle_toggle_all)
        self.serial_config_btn.clicked.connect(self._show_serial_config)

    def _start_status_update(self) -> None:
        """启动状态更新定时器"""
        try:
            self.status_timer = QTimer()
            self.status_timer.timeout.connect(self._update_all_status)
            self.status_timer.start(1000)  # 每秒更次状态
        except Exception as e:
            logger.error(f"启动状态更新定时器失败: {str(e)}")

    def _update_all_status(self) -> None:
        """更新所有服务状态"""
        try:
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                status = self.perf_manager.get_service_status(service)
                self._update_service_status(service, status)

            # 更新串口配置按钮状态
            self._update_serial_config_button_state()

        except Exception as e:
            logger.error(f"更新服务状态失败: {str(e)}")
            logger.exception(e)

    def _update_serial_config_button_state(self) -> None:
        """更新串口配置按钮状态"""
        try:
            device_status = self.perf_manager.get_service_status('device')
            task_status = self.perf_manager.get_service_status('task')

            should_enable = (device_status == ServiceStatus.STOPPED and
                           task_status == ServiceStatus.STOPPED)

            self.serial_config_btn.setEnabled(should_enable)

        except Exception as e:
            logger.error(f"更新串口配置按钮状态失败: {str(e)}")
            self.serial_config_btn.setEnabled(False)

    def _update_service_status(self, service_type: str, status: ServiceStatus) -> None:
        """更新服务状态显示"""
        try:
            status_label = getattr(self, f"{service_type}_status")
            checkbox = getattr(self, f"{service_type}_checkbox")

            # 获取当前状态
            current_status = status_label.property("status")

            # 确保status是ServiceStatus枚举类型
            if isinstance(status, int):
                status = ServiceStatus(status)

            # 状态值映射
            status_value = {
                ServiceStatus.RUNNING: "running",
                ServiceStatus.STOPPED: "stopped",
                ServiceStatus.FAILED: "error",
                ServiceStatus.STARTING: "starting",
                ServiceStatus.STOPPING: "stopping"
            }.get(status, "stopped")  # 默认为stopped而不是unknown

            # 状态文本映射
            status_text = {
                ServiceStatus.RUNNING: "运行中",
                ServiceStatus.STOPPED: "已停止",
                ServiceStatus.FAILED: "错误",
                ServiceStatus.STARTING: "启动中",
                ServiceStatus.STOPPING: "停止中"
            }.get(status, "已停止")  # 默认为"已停止"而不是"未知"

            # 如果当前是 "starting" 状态，则只允许更新为 "running" 或 "error"
            if current_status == "starting" and status_value not in ["running", "error"]:
                return

            # 只在状态确实变化且符合逻辑的情况下更新
            if current_status != status_value:
                # 防止从启动中状态直接变为停止中状态
                if not (current_status == "starting" and status_value == "stopping"):
                    status_label.setText(f"状态: {status_text}")
                    status_label.setStyleSheet(LabelStyle.get_stats_label_style(status_value))
                    status_label.setProperty("status", status_value)
                    status_label.style().unpolish(status_label)
                    status_label.style().polish(status_label)

            # 更新复选框状态（不触发信号）
            checkbox.blockSignals(True)
            checkbox.setChecked(status == ServiceStatus.RUNNING)
            checkbox.setEnabled(status not in [ServiceStatus.STARTING, ServiceStatus.STOPPING])
            checkbox.blockSignals(False)

            # 更新一键操作按钮状态
            self._update_toggle_all_button_state()

        except Exception as e:
            logger.error(f"更新服务状态显示失败: {str(e)}")

    def _update_toggle_all_button_state(self) -> None:
        """更新一键操作按钮状态"""
        try:
            # 检查是否有服务在切换状态
            any_transitioning = any(
                getattr(self, f"{service}_status").property("status") in ["starting", "stopping"]
                for service in ['screen_lock', 'client', 'device', 'task', 'request']
            )

            # 检查是否所有服务都在运行
            all_running = all(
                getattr(self, f"{service}_status").property("status") == "running"
                for service in ['screen_lock', 'client', 'device', 'task', 'request']
            )

            # 检查是否所有服务都在停止中
            all_stopping = all(
                getattr(self, f"{service}_status").property("status") == "stopping"
                for service in ['screen_lock', 'client', 'device', 'task', 'request']
            )

            # 如果有服务正在切换状态且不是所有服务都在停止中，禁用一键操作按钮
            self.toggle_all_button.setEnabled(not any_transitioning or all_stopping)

            # 根据服务状态更新按钮文本
            if all_running:
                self.toggle_all_button.setText("一键停止")
            else:
                self.toggle_all_button.setText("一键启动")

        except Exception as e:
            logger.error(f"更新一键操作按钮状态失败: {str(e)}")

    def _handle_service_toggle(self, service_type: str, state: bool) -> None:
        """处理服务开关"""
        try:
            # 防止重复触发
            checkbox = getattr(self, f"{service_type}_checkbox")
            current_status = self.perf_manager.get_service_status(service_type)
            if state == (current_status == ServiceStatus.RUNNING):
                return

            # 禁用选框，防止重复点击
            checkbox.setEnabled(False)

            # 立即设置复选框状态，阻止信号触发
            checkbox.blockSignals(True)
            checkbox.setChecked(state)
            checkbox.blockSignals(False)

            # 更新状态文本
            status_label = getattr(self, f"{service_type}_status")
            status_label.setText(f"状态: {'启动中' if state else '停止中'}")
            status_label.setProperty("status", "starting" if state else "stopping")
            status_label.style().unpolish(status_label)
            status_label.style().polish(status_label)

            # 更新一键操作按钮状态
            self._update_toggle_all_button_state()

            # 创建异步任务
            loop = asyncio.get_event_loop()
            if loop and not loop.is_closed():
                if state:
                    loop.create_task(self._start_service(service_type))
                else:
                    loop.create_task(self._stop_service(service_type))
        except Exception as e:
            logger.error(f"切换服务状态失败: {str(e)}")

    def _handle_toggle_all(self) -> None:
        """处理一键开启/关闭所有服务"""
        try:
            # 检查是否所有服务都在运行
            all_running = all(
                getattr(self, f"{service}_status").property("status") == "running"
                for service in ['screen_lock', 'client', 'device', 'task', 'request']
            )

            # 禁用所有复选框和一键操作按钮
            self.toggle_all_button.setEnabled(False)
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.setEnabled(False)

            # 创建异步任务
            loop = asyncio.get_event_loop()
            if loop and not loop.is_closed():
                if not all_running:
                    # 启动所有服务 - 使用 start_all_services
                    loop.create_task(self._start_all_services())
                else:
                    # 停止所有服务 - 按相反顺序停止
                    loop.create_task(self._stop_all_services())

        except Exception as e:
            logger.error(f"切换所有服务失败: {str(e)}")
            # 发生错误时重新启用按钮
            self.toggle_all_button.setEnabled(True)
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.setEnabled(True)

    async def _start_service(self, service_type: str) -> None:
        """启动服务"""
        try:
            # 启动服务
            success = await self.perf_manager.start_service(service_type)

            if not success:
                # 只在失败时更新状态为失败
                self._update_service_status(service_type, ServiceStatus.FAILED)

            # 重新启用复选框
            checkbox = getattr(self, f"{service_type}_checkbox")
            checkbox.setEnabled(True)

        except Exception as e:
            logger.error(f"启动服务失败: {str(e)}")
            self._update_service_status(service_type, ServiceStatus.FAILED)
            # 重新启用复选框
            checkbox = getattr(self, f"{service_type}_checkbox")
            checkbox.setEnabled(True)

    async def _stop_service(self, service_type: str) -> None:
        """停止服务"""
        try:
            # 禁用对应的复选框
            checkbox = getattr(self, f"{service_type}_checkbox")
            checkbox.setEnabled(False)

            # 更新状态文本为"停止中"
            status_label = getattr(self, f"{service_type}_status")
            status_label.setText("状态: 停止中")

            # 更新一键操作按钮状态
            self._update_toggle_all_button_state()

            # 停止服务
            success = await self.perf_manager.stop_service(service_type)

            if not success:
                self._update_service_status(service_type, ServiceStatus.FAILED)
                checkbox.setEnabled(True)
                checkbox.setChecked(True)

            # 重新启用复选框
            checkbox.setEnabled(True)

        except Exception as e:
            logger.error(f"停止服务失败: {str(e)}")
            self._update_service_status(service_type, ServiceStatus.FAILED)
            checkbox.setEnabled(True)
            checkbox.setChecked(True)

    def reload(self) -> None:
        """重新加载性能自动化窗口状态"""
        try:
            logger.info("重新加载性能自动化窗口...")

            # 重置所有复选框状态
            for service in ['screen_lock', 'client', 'device', 'task']:
                checkbox = getattr(self, f"{service}_checkbox", None)
                if checkbox:
                    checkbox.setChecked(False)
                    checkbox.setEnabled(True)

            # 重置所有状态标签
            for service in ['screen_lock', 'client', 'device', 'task']:
                status_label = getattr(self, f"{service}_status", None)
                if status_label:
                    status_label.setText("状态: 已停止")
                    status_label.setProperty("status", "stopped")
                    status_label.style().unpolish(status_label)
                    status_label.style().polish(status_label)

            # 更新总体状态指示器
            if hasattr(self, 'running_stats'):
                self.running_stats.setText("运行中: 0")
            if hasattr(self, 'stopped_stats'):
                self.stopped_stats.setText("已停止: 4")

            # 如果有正在运行的服务，尝试停止它们
            loop = asyncio.get_event_loop()
            if loop and not loop.is_closed():
                for service in ['screen_lock', 'client', 'device', 'task']:
                    loop.create_task(self._stop_service(service))

            # 重新初始化UI
            self.hide()
            self.setup_ui()
            self.setup_connections()
            self._start_status_update()
            self.show()

            logger.info("性能自动化窗口重新加载完成")

        except Exception as e:
            logger.error(f"重新加载性能自动化窗口失败: {str(e)}")

    def _show_serial_config(self) -> None:
        """显示串口配置对话框"""
        try:
            dialog = SerialDialog(self)
            dialog.exec()

        except Exception as e:
            logger.error(f"显示串口配置对话框失败: {str(e)}")
            logger.exception(e)

    async def _start_all_services(self) -> None:
        """启动所有服务"""
        try:
            # 先更新所有服务的UI状态为启动中
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                status_label = getattr(self, f"{service}_status")
                status_label.setText("状态: 启动中")
                status_label.setProperty("status", "starting")
                status_label.style().unpolish(status_label)
                status_label.style().polish(status_label)
                
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.blockSignals(True)
                checkbox.setChecked(True)
                checkbox.blockSignals(False)

            # 启动所有服务
            success = await self.perf_manager.start_all_services()

            if not success:
                # 如果启动失败，更新所有服务状态为失败
                for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                    self._update_service_status(service, ServiceStatus.FAILED)

            # 重新启用所有复选框和一键操作按钮
            self.toggle_all_button.setEnabled(True)
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.setEnabled(True)

        except Exception as e:
            logger.error(f"启动所有服务失败: {str(e)}")
            # 发生错误时重新启用所有按钮
            self.toggle_all_button.setEnabled(True)
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.setEnabled(True)
                self._update_service_status(service, ServiceStatus.FAILED)

    async def _stop_all_services(self) -> None:
        """停止所有服务"""
        try:
            # 先更新所有服务的UI状态为停止中
            for service in ['request', 'task', 'device', 'client', 'screen_lock']:
                status_label = getattr(self, f"{service}_status")
                status_label.setText("状态: 停止中")
                status_label.setProperty("status", "stopping")
                status_label.style().unpolish(status_label)
                status_label.style().polish(status_label)
                
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.blockSignals(True)
                checkbox.setChecked(False)
                checkbox.blockSignals(False)

            # 停止所有服务
            success = await self.perf_manager.stop_all_services()

            if not success:
                # 如果停止失败，更新所有服务状态为失败
                for service in ['request', 'task', 'device', 'client', 'screen_lock']:
                    self._update_service_status(service, ServiceStatus.FAILED)

            # 重新启用所有复选框和一键操作按钮
            self.toggle_all_button.setEnabled(True)
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.setEnabled(True)

        except Exception as e:
            logger.error(f"停止所有服务失败: {str(e)}")
            # 发生错误时重新启用所有按钮
            self.toggle_all_button.setEnabled(True)
            for service in ['screen_lock', 'client', 'device', 'task', 'request']:
                checkbox = getattr(self, f"{service}_checkbox")
                checkbox.setEnabled(True)
                self._update_service_status(service, ServiceStatus.FAILED)