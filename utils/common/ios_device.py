"""
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-11 14:39:44
FilePath: /global_rtc_client/utils/perf/ios_device.py
Description: iOS设备管理器，包含设备信息收集和控制功能
"""

# 标准库导入
import os
import json
import re
import asyncio
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict, Union, Set

# 本地导入
from config.constants import (
    AppInstallMode,
    DeviceConnectType,
    DeviceState,
    PlatformType,
)
from core.auth_manager import auth_manager
from core.settings_manager import settings_manager
from utils.common.log_utils import get_logger
from utils.common.network_utils import network_utils

logger = get_logger(__name__)

# 常量定义
DEFAULT_INSTALL_TIMEOUT = 300  # 安装超时时间（秒）
DEFAULT_UNINSTALL_TIMEOUT = 30  # 卸载超时时间（秒）
MIN_REPACK_SIZE = 100 * 1024 * 1024  # 最小重打包文件大小（100MB）

# 系统应用前缀，用于判断是否为第三方应用
SYSTEM_BUNDLE_PREFIXES = (
    "com.apple.", "com.bytedance."
)

class PackageType(Enum):
    """应用类型枚举"""
    ALL = "all"       # 所有应用
    SYSTEM = "system" # 系统应用
    USER = "user"     # 用户应用
    THIRD = "third"   # 第三方应用

@dataclass
class IOSDeviceInfo:
    """iOS设备信息数据类
    
    Attributes:
        name: 设备名称
        udid: 设备唯一标识符
        model: 设备型号
        sys_version: 系统版本
        brand: 设备品牌
        resolution: 屏幕分辨率
        connect_type: 连接类型列表 [1: USB, 2: WiFi]
        ip: IP地址，可选
        sys_type: 系统类型，默认为iOS
        serial_port: 串口号，可选
        state: 设备状态，默认为在线
        is_occupied: 是否被占用，默认为0
        user: 使用者，可选
        owner: 所有者，可选
    """
    # 必填字段
    name: str
    udid: str
    model: str
    sys_version: str
    brand: str
    resolution: str
    connect_type: list[int]
    
    # 可选字段
    ip: Optional[str] = None
    serial_port: Optional[str] = None
    user: Optional[str] = None
    owner: Optional[str] = None
    
    # 状态字段
    sys_type: int = PlatformType.IOS.value
    state: int = DeviceState.ONLINE.value
    is_occupied: int = 0


class IOSDevice:
    """iOS设备管理器，包含设备信息收集和控制功能"""

    def __init__(self):
        """初始化iOS设备管理器"""
        try:
            self._init_paths()
            self._init_user()
            self._initialized_devices = set()  # 添加一个集合来记录已初始化的设备
        except Exception as e:
            logger.error(f"初始化iOS设备管理器失败: {str(e)}")
            raise

    def _init_paths(self) -> None:
        """初始化所需的路径"""
        # 获取bdc路径
        self.bdc_path = self._get_bdc_path()
        
        # 获取Python路径
        self.python_path = self._get_python_path()
        
        # 加载串口配置路径
        data_dir = settings_manager.get('storage.paths.base.data', 'data')
        serial_file = settings_manager.get('storage.paths.files.serial', 'serial.json')
        self.serial_config_path = os.path.join(data_dir, serial_file)

    def _init_user(self) -> None:
        """初始化用户相关信息"""
        self.operator = ""
        current_user = auth_manager.get_current_user()
        if current_user:
            self.operator = current_user.get('username', '')
            
        # 串口配置
        self.serial_baud_rate = settings_manager.get('serial.baud_rate', 9600)
        self.serial_timeout = settings_manager.get('serial.timeout', 0.5)
        self.serial_retry_count = settings_manager.get('serial.retry.count', 3)
        self.serial_retry_interval = settings_manager.get('serial.retry.interval', 5)

        # 重打包配置
        self.repack_timeout = settings_manager.get('app.repack.timeout', 300)
        self.repack_retry_count = settings_manager.get('app.repack.retry.count', 3)
        self.repack_retry_interval = settings_manager.get('app.repack.retry.interval', 5)

        # 安装配置
        self.install_timeout = settings_manager.get('app.install.timeout', DEFAULT_INSTALL_TIMEOUT)
        self.install_retry_count = settings_manager.get('app.install.retry.count', 3)
        self.install_retry_interval = settings_manager.get('app.install.retry.interval', 5)

    def _get_bdc_path(self) -> str:
        """获取bdc工具路径"""
        try:
            # 首先检查环境变量
            if 'BDC_PATH' in os.environ:
                return os.environ['BDC_PATH']

            # 然后检查默认安装路径
            default_paths = '/usr/local/bin/bdc'

            if os.path.exists(default_paths):
                return default_paths

            logger.warning("未找到bdc工具，使用默认命令'bdc'")
            return "bdc"

        except Exception as e:
            logger.error(f"获取bdc路径失败: {str(e)}")
            return "bdc"

    def _get_python_path(self) -> str:
        """获取Python解释器路径
        
        Returns:
            str: Python解释器路径
        """
        try:
            import sys
            python_path = sys.executable
            return python_path
        except Exception as e:
            logger.error(f"获取Python路径失败: {str(e)}")
            return "python"

    async def get_ios_devices(self) -> List[IOSDeviceInfo]:
        """获取iOS设备列表"""
        try:
            # 获取设备列表
            process = await asyncio.create_subprocess_exec(
                self.bdc_path, 'list-devices',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            devices_output = stdout.decode()
            
            # 解析设备信息
            devices = []
            for line in devices_output.splitlines():
                if not line.strip() or '|' not in line:
                    continue
                
                # 解析设备信息
                details = [item.strip() for item in line.split('|')]
                if len(details) < 10:
                    logger.warning(f"[iOS] 设备信息格式不正确: {line}")
                    continue
                
                # 获取 UDID
                udid = details[1] if len(details) > 1 else None
                if not udid:
                    logger.warning("[iOS] 设备UDID为空")
                    continue
                    
                # 过滤掉 Mac 电脑的 UDID
                if re.match(r'^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$', udid, re.IGNORECASE):
                    continue

                # 只在设备首次出现时执行ui_test_init
                if udid not in self._initialized_devices:
                    if await self.ui_test_init(udid):
                        self._initialized_devices.add(udid)

                # 并行获取设备的分辨率、连接类型和IP地址
                resolution, connect_type, ip = await asyncio.gather(
                    self.get_resolution_async(udid),
                    self.get_connect_type_async(udid),
                    self.get_device_ip_async(udid)
                )

                # 创建设备信息对象
                device_info = IOSDeviceInfo(
                    # 必填字段
                    name=details[0] if len(details) > 0 else "未知",
                    udid=udid,
                    model=details[2] if len(details) > 2 else "未知",
                    sys_version=details[3] if len(details) > 3 else "未知",
                    brand=f"Apple {details[6]}" if len(details) > 6 else "Apple",
                    resolution=resolution,
                    connect_type=connect_type,
                    sys_type=PlatformType.IOS.value,
                    state=DeviceState.ONLINE.value,
                                        
                    # 可选字段
                    ip=ip,
                    user=self.operator,
                    owner=self.operator
                )
                devices.append(device_info)

            return devices

        except Exception as e:
            logger.error(f"获取iOS设备列表失败: {str(e)}")
            return []

    async def get_resolution_async(self, udid: str) -> str:
        """异步获取设备分辨率"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.bdc_path, 'screen-resolution', '-u', udid, '-o', 'json',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            if stdout:
                resolution = json.loads(stdout.decode())["screen_resolution"].replace("*", "x")
                return resolution
            return "unknown"
        except Exception as e:
            logger.warning(f"获取设备 {udid} 分辨率失败: {str(e)}")
            return "unknown"

    async def get_device_ip_async(self, udid: str) -> str:
        """异步获取设备IP地址"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.bdc_path, 'get-ip', '-u', udid,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            # 检查是否有错误输出
            if stderr:
                error_msg = stderr.decode().strip()
                logger.warning(f"获取设备 {udid} IP地址时出现错误: {error_msg}")
                return ""
                
            # 获取IP地址
            ip = stdout.decode().strip()
            
            # 验证IP地址格式
            if not ip or "error" in ip.lower():
                return ""
                
            return ip
            
        except Exception as e:
            logger.warning(f"获取设备 {udid} IP地址失败: {str(e)}")
            return ""

    async def get_connect_type_async(self, udid: str) -> list[int]:
        """异步获取设备连接类型"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.bdc_path, 'get-connect-mode', '-u', udid,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            connect_mode = stdout.decode().strip().lower()
            
            connect_types = set()
            if connect_mode == "usb":
                connect_types.add(DeviceConnectType.USB.value)
            elif connect_mode == "network":
                connect_types.add(DeviceConnectType.WIFI.value)
            
            if not connect_types:
                connect_types.add(DeviceConnectType.USB.value)
            
            return list(connect_types)
        except Exception as e:
            logger.error(f"获取设备 {udid} 连接类型失败: {str(e)}")
            return [DeviceConnectType.USB.value]

    async def ui_test_init(self, device_udid: str) -> bool:
        """异步初始化UI测试环境"""
        try:
            # 获取xcconfig
            shoots_ios_dir = os.path.expanduser("~/.shoots/ios")
            xcconfig_path = os.path.join(shoots_ios_dir, f"{device_udid}.xcconfig")
            if not os.path.exists(xcconfig_path):
                logger.warning(f"[iOS] xcconfig文件不存在: {xcconfig_path}")
                return False

            # 读取xcconfig内容
            with open(xcconfig_path, 'r') as f:
                xcconfig_content = f.read()

            # 解析PROVISIONING_PROFILE的值
            provisioning_profile = None
            for line in xcconfig_content.splitlines():
                if line.startswith('PROVISIONING_PROFILE ='):
                    provisioning_profile = line.split('=')[1].strip().replace(' ', '_').replace('(', '').replace(')', '') + ".mobileprovision"
                    break

            if not provisioning_profile:
                logger.warning(f"[iOS] 未找到证书文件名，请检查xcconfig文件: {xcconfig_path}")
                return False

            # 设置默认值
            provision_path = os.path.join(shoots_ios_dir, provisioning_profile)

            # 下载证书
            if not os.path.exists(provision_path):
                if not network_utils.download_file_sync(f"https://tosv.boe.byted.org/obj/global-rtc-test-platform/certificates/{device_udid}/embedded.mobileprovision", provision_path):
                    logger.error(f"[iOS] 下载证书失败，URL: https://tosv.boe.byted.org/obj/global-rtc-test-platform/certificates/{device_udid}/embedded.mobileprovision")
                    return False

            # 执行初始化
            ui_test_cmd = [self.bdc_path, 'ui-test', '-u', device_udid, '-m', provision_path]
            process = await asyncio.create_subprocess_exec(
                *ui_test_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            output = stdout.decode()
            return "prepare success" in output or "already authorize success" in output

        except Exception as e:
            logger.warning(f"[iOS] 初始化UI测试环境失败: {str(e)}")
            return False
        
    async def repack_app(self, app_path: str, save_dir: str, **kwargs) -> Optional[str]:
        """重打包IPA文件"""
        try:
            if not os.path.exists(app_path) or not app_path.endswith('.ipa'):
                raise ValueError(f"[iOS] 无效的IPA文件: {app_path}")

            # 构建重打包文件路径
            app_name = os.path.basename(app_path)
            base_name = os.path.splitext(app_name)[0]
            repack_path = os.path.join(save_dir, f"{base_name}-repacked-signed.ipa")

            # 检查重打包文件是否存在且有效
            if os.path.exists(repack_path) and os.path.getsize(repack_path) > MIN_REPACK_SIZE:
                logger.info("[iOS] 重打包文件已存在且有效")
                return repack_path

            # 获取证书
            provision_path = os.path.join(save_dir, "musically.mobileprovision")
            if not os.path.exists(provision_path):
                repackage_cert = kwargs.get('repackage_cert')
                if not repackage_cert:
                    raise ValueError("[iOS] 证书URL为空")

                logger.info("[iOS] 开始下载证书")
                if not await network_utils.download_file(repackage_cert, provision_path):
                    raise Exception("[iOS] 下载证书失败")

            # 执行重打包命令
            cmd = [
                self.python_path, "-m", "shoots_ios", "repack",
                app_path, "--sign-exclude", "AwemeBroadcastExtension.appex",
                "--plist-update", '["autotest","CHANNEL_NAME"]',
                "--provision", provision_path
            ]
            logger.debug(f"[iOS] 开始执行重打包, 重打包命令: {' '.join(cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            stdout = stdout.decode() if stdout else ""
            stderr = stderr.decode() if stderr else ""

            # 检查重打包文件是否存在
            if os.path.exists(repack_path):
                if os.path.getsize(repack_path) > MIN_REPACK_SIZE:
                    logger.info("[iOS] 重打包成功")
                    return repack_path
                else:
                    raise Exception("[iOS] 重打包文件验证失败，文件大小不足")
            else:
                # 尝试从输出中获取实际的输出路径
                for line in stdout.splitlines():
                    if "target is:" in line:
                        actual_path = line.split("target is:")[-1].strip()
                        if os.path.exists(actual_path):
                            logger.info("[iOS] 使用替代输出路径重打包成功")
                            return actual_path
                raise Exception(f"[iOS] 重打包文件不存在")

        except Exception as e:
            logger.error(f"[iOS] 重打包应用失败: {str(e)}")
            return None

    async def install_app(self, device_udid: Union[str, list[str]], app_path: str, **kwargs) -> bool:
        """支持单设备和多设备安装
        
        Args:
            device_udid: 设备ID或设备ID列表
            app_path: 应用路径
            **kwargs: 额外参数
                install_mode: 安装模式
                repackage_cert: 重打包证书
        """
        try:
            if not app_path:
                logger.warning("[iOS] IPA路径为空，无法安装应用")
                return False

            device_udids = [device_udid] if isinstance(device_udid, str) else device_udid
            success_count = 0
            install_mode = kwargs.get("install_mode", AppInstallMode.UNINSTALL_INSTALL.value)

            for udid in device_udids:
                retry_count = 0
                while retry_count < self.install_retry_count:
                    try:
                        if not await self._check_device_connection(udid):
                            logger.error(f"[iOS] 设备 {udid} 未连接或未授权")
                            break

                        if install_mode == AppInstallMode.NOT_INSTALL.value:
                            success = True
                        elif install_mode == AppInstallMode.UNINSTALL_INSTALL.value:
                            # 卸载已有应用
                            bundle_ids = ["com.zhiliaoapp.musically", "com.zhiliaoapp.musically.ep"]
                            for bundle_id in bundle_ids:
                                if await self._uninstall_app(udid, bundle_id):
                                    break
                            success = await self._execute_single_device_install(udid, app_path)
                        elif install_mode == AppInstallMode.OVERRIDE_INSTALL.value:
                            success = await self._execute_single_device_install(udid, app_path)
                        else:
                            logger.error(f"[iOS] 不支持的安装模式: {install_mode}")
                            return False

                        if success:
                            success_count += 1
                            logger.info(f"[iOS] 设备 {udid} 安装成功")
                            break

                        logger.error(f"[iOS] 安装失败，开始重试 ({retry_count + 1}/{self.install_retry_count})")
                        retry_count += 1
                        if retry_count < self.install_retry_count:
                            await asyncio.sleep(self.install_retry_interval)

                    except Exception as e:
                        logger.error(f"[iOS] 安装失败: {str(e)}")
                        retry_count += 1
                        if retry_count < self.install_retry_count:
                            await asyncio.sleep(self.install_retry_interval)

            return success_count == len(device_udids)

        except Exception as e:
            logger.error(f"[iOS] 安装应用失败: {str(e)}")
            return False

    async def _execute_single_device_install(self, device_udid: str, app_path: str) -> bool:
        """在单个设备上执行应用安装
        
        Args:
            device_udid: 设备ID
            app_path: 应用路径
        """
        try:
            install_cmd = [self.bdc_path, "install", app_path, "-u", device_udid]
            install_result = await asyncio.create_subprocess_exec(
                *install_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await install_result.communicate()
            stdout = stdout.decode() if stdout else ""
            stderr = stderr.decode() if stderr else ""

            # 检查安装结果
            if "Install successful" in stdout:
                logger.info("[iOS] 安装完成")
                return True
            elif stderr:
                logger.error(f"[iOS] 安装失败: {stderr}")
            return False

        except Exception as e:
            logger.error(f"[iOS] 安装应用失败: {str(e)}")
            return False

    async def _check_device_connection(self, device_udid: str) -> bool:
        """检查iOS设备连接状态
        
        Args:
            device_udid: 设备ID
            
        Returns:
            bool: 设备是否已连接并可用
        """
        try:
            get_devices_cmd = [self.bdc_path, "list-devices"]
            get_devices_result = await asyncio.create_subprocess_exec(
                *get_devices_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await get_devices_result.communicate()
            devices_output = stdout.decode()

            if not devices_output:
                return False

            for line in devices_output.splitlines():
                if device_udid in line:
                    return True

            logger.warning(f"[iOS] 设备 {device_udid} 未连接")
            return False

        except Exception as e:
            logger.error(f"[iOS] 检查设备连接状态异常: {str(e)}")
            return False

    async def _uninstall_app(self, device_udid: str, bundle_id: str) -> bool:
        """卸载应用
        
        Args:
            device_udid: 设备ID
            bundle_id: 应用包名
            
        Returns:
            bool: 是否卸载成功
        """
        try:
            uninstall_cmd = [self.bdc_path, "uninstall", "-u", device_udid, "-b", bundle_id]
            uninstall_result = await asyncio.create_subprocess_exec(
                *uninstall_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await uninstall_result.communicate()
            success = "Success" in (stdout.decode() if stdout else "")
            return success

        except Exception as e:
            logger.error(f"[iOS] 卸载应用失败: {str(e)}")
            return False

    async def get_installed_packages(self, device_udid: str, package_type: PackageType = PackageType.ALL) -> List[Dict[str, str]]:
        """获取已安装的应用列表
        
        Args:
            device_udid: 设备ID
            package_type: 应用类型，可选值：ALL(所有)、SYSTEM(系统)、USER(用户)、THIRD(第三方)
            
        Returns:
            List[Dict[str, str]]: 应用信息列表，每个字典包含 bundle_id 和 name 字段
        """
        try:
            if not await self._check_device_connection(device_udid):
                logger.error(f"[iOS] 设备 {device_udid} 未连接或未授权")
                return []
                
            # 根据类型构建命令
            cmd = [self.bdc_path, "list-apps", "-u", device_udid]
            if package_type == PackageType.SYSTEM:
                cmd.extend(["-t", "system"])
            elif package_type == PackageType.USER:
                cmd.extend(["-t", "user"])
            elif package_type == PackageType.THIRD:
                cmd.extend(["-t", "user"])  # iOS中user即为第三方应用
                
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"[iOS] 获取应用列表失败: {stderr.decode() if stderr else ''}")
                return []
                
            # 解析应用列表
            apps_output = stdout.decode() if stdout else ""
            apps_info = []
            current_app = {}
            
            for line in apps_output.splitlines():
                if "BundleID:" in line:
                    if current_app:  # 保存上一个应用信息
                        if package_type == PackageType.THIRD:
                            # 对于第三方应用类型，过滤掉系统应用
                            if not any(current_app["bundle_id"].startswith(prefix) for prefix in SYSTEM_BUNDLE_PREFIXES):
                                apps_info.append(current_app)
                        else:
                            apps_info.append(current_app)
                    current_app = {"bundle_id": line.split("BundleID:")[1].strip()}
                elif "DisplayName:" in line and current_app:
                    current_app["name"] = line.split("DisplayName:")[1].strip()
            
            # 添加最后一个应用
            if current_app:
                if package_type == PackageType.THIRD:
                    if not any(current_app["bundle_id"].startswith(prefix) for prefix in SYSTEM_BUNDLE_PREFIXES):
                        apps_info.append(current_app)
                else:
                    apps_info.append(current_app)
            
            return apps_info
            
        except Exception as e:
            logger.error(f"[iOS] 获取已安装应用列表失败: {str(e)}")
            return []

    async def uninstall_third_party_apps(self, device_udid: str, whitelist_packages: Set[str] = None) -> bool:
        """卸载除白名单以外的所有第三方应用
        
        Args:
            device_udid: 设备ID
            whitelist_packages: 白名单包名集合，这些应用不会被卸载
            
        Returns:
            bool: 是否卸载成功
        """
        try:
            # 初始化白名单
            if whitelist_packages is None:
                whitelist_packages = set()
                
            # 检查设备连接
            if not await self._check_device_connection(device_udid):
                logger.error(f"[iOS] 设备 {device_udid} 未连接或未授权")
                return False
                
            # 获取第三方应用列表
            apps_info = await self.get_installed_packages(device_udid, PackageType.THIRD)
            
            # 过滤白名单应用
            bundle_ids = [app['bundle_id'] for app in apps_info if app['bundle_id'] not in whitelist_packages]
            
            if not bundle_ids:
                logger.info(f"[iOS] 设备 {device_udid} 没有需要卸载的第三方应用")
                return True
                
            # 卸载应用
            success_count = 0
            failed_packages = []
            total_count = len(bundle_ids)
            
            logger.info(f"[iOS] 开始卸载 {total_count} 个第三方应用")
            
            for bundle_id in bundle_ids:
                try:
                    if await self._uninstall_app(device_udid, bundle_id):
                        success_count += 1
                    else:
                        failed_packages.append(bundle_id)
                except Exception as e:
                    logger.error(f"[iOS] 卸载 {bundle_id} 失败: {str(e)}")
                    failed_packages.append(bundle_id)
            
            # 输出卸载结果
            if success_count == total_count:
                logger.info(f"[iOS] 所有第三方应用卸载成功，总共 {total_count} 个")
                return True
            else:
                logger.warning(f"[iOS] 第三方应用卸载完成，成功 {success_count}/{total_count}")
                logger.warning(f"[iOS] 卸载失败的应用: {failed_packages}")
                return False
                
        except Exception as e:
            logger.error(f"[iOS] 卸载第三方应用失败: {str(e)}")
            return False

    async def push_mapping_files(self, device_udid: str, jenkins_build_result_url: str,
                           package_name: str, save_dir: str) -> bool:
        """推送映射文件到设备

        Args:
            device_udid: 设备ID
            jenkins_build_result_url: Jenkins构建结果URL
            package_name: 包名
            save_dir: 保存目录

        Returns:
            bool: 是否成功
        """
        logger.info("[iOS] iOS设备不支持推送映射文件")
        return True

    async def get_battery_level(self, udid: str) -> int:
        """获取设备电量
        
        Args:
            udid: 设备的唯一标识符
            
        Returns:
            int: 电量百分比，失败返回-1
        """
        try:
            get_battery_info_cmd = [self.bdc_path, "get-batteryinfo", "-u", udid]
            get_battery_info_result = await asyncio.create_subprocess_exec(
                *get_battery_info_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await get_battery_info_result.communicate()

            if stdout:
                for line in stdout.decode().splitlines():
                    if "BatteryCurrentCapacity:" in line:
                        power_str = line.split("BatteryCurrentCapacity:")[1].strip().split("%")[0]
                        power_level = int(power_str)
                        return power_level

            raise ValueError("[iOS] 未找到电量信息")

        except Exception as e:
            logger.error(f"[iOS] 获取电量失败: {str(e)}")
            return -1

    def clear_initialized_devices(self):
        """清理已初始化设备的记录"""
        self._initialized_devices.clear()
        logger.info("[iOS] 已清理设备初始化记录")

    async def kill_bdc_service(self) -> bool:
        """终止bdc服务进程"""
        try:
            # 使用多个命令确保彻底终止服务
            await asyncio.create_subprocess_exec(
                ["pkill", "bdc"],
                stdout=asyncio.subprocess.DEVNULL,
                stderr=asyncio.subprocess.DEVNULL,
                task_name="ios-bdc-kill"
            )
            await asyncio.create_subprocess_exec(
                ["pkill", "bdc-server"],
                stdout=asyncio.subprocess.DEVNULL,
                stderr=asyncio.subprocess.DEVNULL,
                task_name="ios-bdc-server-kill"
            )
            # 清理已初始化设备的记录
            self.clear_initialized_devices()
            logger.info("[iOS] 终止服务进程成功")
            return True
        except Exception as e:
            logger.error(f"[iOS] 终止服务进程失败: {str(e)}")
            return False

    async def reboot_device(self, device_udid: str):
        """异步重启设备
        
        Args:
            device_udid: 设备UDID
        """
        try:
            logger.info(f"[iOS] 开始重启设备: {device_udid}")
            
            # 使用异步子进程执行重启命令
            process = await asyncio.create_subprocess_exec(
                self.bdc_path, 'reboot', '-u', device_udid,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                start_new_session=True  # 在新会话中启动进程
            )
            
            # 不等待进程完成，直接返回
            logger.info(f"[iOS] 设备 {device_udid} 开始重启")
                
        except Exception as e:
            logger.error(f"[iOS] 重启设备失败: {str(e)}")

ios_device = IOSDevice() 