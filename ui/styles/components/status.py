'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-11 13:17:56
FilePath: /global_rtc_client/ui/styles/components/status.py
Description: 
'''
"""状态样式定义"""
from ..base.colors import Colors
from ..base.gradients import Gradients

class StatusStyle:
    @classmethod
    def get_verify_status_style(cls, status_type: str = "info") -> str:
        """获取验证状态标签样式"""
        styles = {
            "info": f"""
                QLabel {{
                    color: {Colors.INFO};
                    font-size: 14px;
                    padding: 12px;
                    background: {Colors.SURFACE_LIGHT};
                    border-radius: 8px;
                    margin: 8px;
                }}
            """,
            "error": f"""
                QLabel {{
                    color: {Colors.ERROR};
                    font-size: 14px;
                    padding: 12px;
                    background: {Colors.SURFACE_LIGHT};
                    border-radius: 8px;
                    margin: 8px;
                }}
            """,
            "success": f"""
                QLabel {{
                    color: {Colors.SUCCESS};
                    font-size: 14px;
                    padding: 12px;
                    background: {Colors.SURFACE_LIGHT};
                    border-radius: 8px;
                    margin: 8px;
                }}
            """
        }
        return styles.get(status_type, styles["info"]) 