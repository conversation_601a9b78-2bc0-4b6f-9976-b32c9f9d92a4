"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-09 00:25:00
FilePath: /global_rtc_client/backend/openapi/perf_api.py
Description: 性能相关的OpenAPI实现
"""
from typing_extensions import dataclass_transform
import aiohttp
import asyncio
import functools
from typing import List, Dict, Optional, Any, Callable, TypeVar
from config.constants import ClientState
from core.settings_manager import settings_manager
from utils.common.log_utils import get_logger

logger = get_logger(__name__)

perf_api_router = 'perf_ui_auto'
business_api_router = 'business'

# 类型提示
T = TypeVar('T')

def handle_api_exceptions(func: Callable[..., T]) -> Callable[..., Optional[T]]:
    """统一的API异常处理装饰器"""
    @functools.wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Optional[T]:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"{func.__name__} 调用失败: {str(e)}", exc_info=True)
            return None
    return wrapper

def process_response(response: Dict, error_msg: str = "API调用失败") -> Optional[Dict]:
    """统一的响应处理方法"""
    if not response:
        logger.error(f"{error_msg}: 响应为空")
        return None
        
    if response.get('code') != 200:
        logger.error(f"{error_msg}: {response.get('msg', '未知错误')}")
        return None
        
    return response.get('data')

class PerfAPI:
    """性能API实现"""

    def __init__(self):
        """初始化"""
        current_env = settings_manager.get('api.current_env', 'local')
        self.base_url = settings_manager.get(f'api.gstest.{current_env}')
        self.timeout = settings_manager.get('api.default_timeout', 30)
        self.retry_count = settings_manager.get('api.retry_count', 3)
        self.retry_interval = settings_manager.get('api.retry_interval', 5)

    # HTTP 状态码常量
    RETRY_STATUS_CODES = {408, 502, 503, 504}

    async def _make_request(self, method: str, url: str, headers: Dict, **kwargs) -> Dict:
        """执行单次请求"""
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.request(
                method, url, headers=headers, timeout=self.timeout, **kwargs
            ) as response:
                if response.status == 200:
                    return await response.json()
                
                error_text = await response.text()
                logger.error(f"请求失败: 状态码={response.status}, 响应内容={error_text}")
                return {'code': response.status, 'msg': error_text}

    def _should_retry(self, response: Dict) -> bool:
        """判断是否需要重试"""
        return response.get('code') in self.RETRY_STATUS_CODES

    async def _handle_retry(self, retry_count: int, error_msg: str) -> None:
        """处理重试等待"""
        if retry_count < self.retry_count:
            wait_time = self.retry_interval * (2 ** retry_count)
            logger.warning(f"请求失败，{wait_time}秒后进行第{retry_count + 1}次重试")
            await asyncio.sleep(wait_time)

    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """发送请求
        
        Args:
            method: 请求方法
            endpoint: 接口路径
            **kwargs: 其他参数
            
        Returns:
            Dict: 响应数据
            
        Raises:
            PerfError: 请求失败
        """
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        headers = kwargs.pop('headers', {}) or {'Content-Type': 'application/json'}
        
        for retry_count in range(self.retry_count):
            try:
                result = await self._make_request(method, url, headers, **kwargs)
                
                if not self._should_retry(result):
                    return result
                    
                await self._handle_retry(retry_count, result.get('msg', ''))
                
            except (asyncio.TimeoutError, Exception) as e:
                error_msg = str(e)
                logger.error(f"请求{'超时' if isinstance(e, asyncio.TimeoutError) else '异常'}: {error_msg}")
                
                if retry_count + 1 == self.retry_count:
                    logger.error(f"请求失败，已达到最大重试次数 {self.retry_count}")
                else:
                    await self._handle_retry(retry_count, error_msg)

    # 客户端相关接口
    @handle_api_exceptions
    async def get_client_detail(self, client_id: Optional[int] = None, mac_address: Optional[str] = None) -> Optional[Dict]:
        """获取客户端详情"""
        # 构建请求参数
        params = {k: v for k, v in {
            'client_id': client_id,
            'mac_address': mac_address
        }.items() if v is not None}
        
        if not params:
            logger.error("client_id 和 mac_address 至少需要提供一个")
            return None
            
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_client/get_client_detail',
            params=params
        )
        
        data = process_response(result, "获取客户端详情失败")
        if data:
            logger.info(f"成功获取客户端详情: ID={data.get('id')}")
        return data

    @handle_api_exceptions
    async def register_perf_client(self, client_info: dict[str, Any]) -> tuple[bool, str]:
        """注册性能测试客户端"""
        result = await self._request(
            'POST',
            f'/{perf_api_router}/perf_client/register_perf_client',
            json=client_info
        )
        
        success = result.get('code') == 200
        msg = result.get('msg', '')

        if success:
            logger.info(f"客户端注册成功")
        else:
            logger.error(f"客户端注册失败: {msg}")
        
        return success, msg

    @handle_api_exceptions
    async def get_client_list(self) -> List[Dict]:
        """获取客户端列表"""
        result = await self._request('GET', f'/{perf_api_router}/perf_client/client_list')
        data = process_response(result, "获取客户端列表失败")
        items = data.get('items', []) if data else []
        return items

    @handle_api_exceptions
    async def update_client_info(self, client_id: int, **kwargs) -> tuple[bool, str]:
        """更新性能测试客户端信息"""
        # 构建请求数据
        data = {
            'id': client_id,
            **kwargs
        }
        
        result = await self._request(
            'PUT',
            f'/{perf_api_router}/perf_client/update_client',
            json=data
        )
        
        success = result.get('code') == 200
        msg = result.get('msg', '')
        
        if success:
            state = kwargs.get('state')
            if state is not None:
                logger.info(f"客户端 {client_id} 状态更新为: {ClientState.get_name(state)}")
            else:
                logger.info(f"客户端 {client_id} 信息更新成功")
        else:
            logger.error(f"更新客户端 {client_id} 信息失败: {msg}")
        
        return success, msg

    # 设备相关接口
    @handle_api_exceptions
    async def upload_device_info(self, client_id: int, device_list: list) -> bool:
        """上传设备信息"""
        device_data = [{
            "name": device.name,
            "udid": device.udid,
            "model": device.model,
            "sys_type": device.sys_type,
            "sys_version": device.sys_version,
            "brand": device.brand,
            "resolution": device.resolution,
            "connect_type": device.connect_type,
            "state": device.state,
            "ip": device.ip,
            "user": device.user,
            "owner": device.owner,
            "serial_port": device.serial_port
        } for device in device_list]

        result = await self._request(
            'POST',
            f'/{perf_api_router}/perf_device/upload_perf_device',
            params={'client_id': client_id},
            json=device_data
        )
        
        success = result.get('code') == 200
        if not success:
            logger.error(f"上传设备信息失败: {result.get('msg', '未知错误')}")
        
        return success

    @handle_api_exceptions
    async def get_device_list(self, sys_type: Optional[int] = None,
                            is_occupied: Optional[int] = None,
                            client_id: Optional[int] = None) -> List[Dict]:
        """获取设备列表"""
        params = {
            'page': 1,
            'page_size': 100,
            **{k: v for k, v in {
                'sys_type': sys_type,
                'is_occupied': is_occupied,
                'client_id': client_id
            }.items() if v is not None}
        }

        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_device/device_list',
            params=params
        )
        
        data = process_response(result, "获取设备列表失败")
        items = data.get('items', []) if data else []
        return items

    @handle_api_exceptions
    async def update_device_occupied(self, device_id: int, is_occupied: int) -> bool:
        """更新设备占用状态"""
        result = await self._request(
            'PUT',
            f'/{perf_api_router}/perf_device/update_device_occupied',
            params={
                'device_id': device_id,
                'is_occupied': is_occupied
            }
        )
        
        success = result.get('code') == 200
        if success:
            logger.debug(f"设备 {device_id} 占用状态更新为: {is_occupied}")
        else:
            logger.error(f"更新设备 {device_id} 占用状态失败")
        return success

    # 账号相关接口
    @handle_api_exceptions
    async def get_account_list(self, business_id: Optional[int] = None,
                             is_occupied: Optional[int] = None,
                             account_type: Optional[int] = None,
                             page: int = 1,
                             page_size: int = 100) -> List[Dict]:
        """获取账号列表"""
        # 构建请求数据
        request_data = {
            'page': page,
            'page_size': page_size,
            'business_id': business_id,
            'is_occupied': is_occupied,
            'account_type': account_type
        }

        result = await self._request(
            'POST',
            f'/{perf_api_router}/perf_account/account_list',
            json=request_data
        )
        
        data = process_response(result, "获取账号列表失败")
        items = data.get('items', []) if data else []
        return items

    def _get_default_business_id(self) -> Optional[int]:
        """从配置中获取默认业务ID"""
        if not hasattr(settings_manager, 'config'):
            return None
            
        config = settings_manager.config
        if not isinstance(config, dict):
            return None
            
        user_config = config.get('user', {})
        if not isinstance(user_config, dict):
            return None
            
        return user_config.get('business_id')

    @handle_api_exceptions
    async def get_account_detail(self, account_id: int) -> Optional[Dict]:
        """获取账号详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_account/account_detail',
            params={'account_id': account_id}
        )
        
        data = process_response(result, f"获取账号 {account_id} 详情失败")
        if data:
            logger.debug(f"成功获取账号详情: ID={account_id}")
        else:
            logger.error(f"获取账号 {account_id} 详情失败")
        return data

    @handle_api_exceptions
    async def update_account_occupied(self, account_id: int, is_occupied: int) -> bool:
        """更新账号占用状态"""
        result = await self._request(
            'PUT',
            f'/{perf_api_router}/perf_account/update_account_occupied',
            params={
                'account_id': account_id,
                'is_occupied': is_occupied
            }
        )
        
        success = result.get('code') == 200
        if success:
            logger.debug(f"账号 {account_id} 占用状态更新为: {is_occupied}")
        else:
            logger.error(f"更新账号 {account_id} 占用状态失败")
        return success

    # 任务相关接口
    @handle_api_exceptions
    async def get_task_list(self, business_id: Optional[int] = None,
                          client_id: Optional[int] = None,
                          status: Optional[int] = None) -> List[Dict]:
        """获取任务列表"""
        params = {k: v for k, v in {
            'business_id': business_id or self._get_default_business_id(),
            'client_id': client_id,
            'status': status
        }.items() if v is not None}

        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_task/task_list',
            params=params
        )
        
        data = process_response(result, "获取任务列表失败")
        items = data.get('items', []) if data else []
        return items

    @handle_api_exceptions
    async def get_task_detail_by_id(self, task_id: int) -> Optional[Dict]:
        """获取任务详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_task/task_detail',
            params={'task_id': task_id}
        )

        data = process_response(result, f"获取任务 {task_id} 详情失败")
        if data:
            logger.info(f"成功获取任务详情: 任务ID={task_id}")
        return data

    @handle_api_exceptions
    async def update_task_status_by_id(self, task_id: int, task_status: int,
                                     start_time: Optional[str] = None,
                                     end_time: Optional[str] = None) -> bool:
        """更新任务状态"""
        data = {
            'id': task_id,
            'status': task_status,
            **{k: v for k, v in {
                'start_time': start_time,
                'end_time': end_time
            }.items() if v is not None}
        }

        result = await self._request(
            'POST',
            f'/{perf_api_router}/perf_task/update_task_status_by_id',
            json=data
        )
        
        success = result.get('code') == 200
        if not success:
            logger.error(f"更新任务 {task_id} 状态失败")
        return success

    @handle_api_exceptions
    async def update_sub_task_status_by_id(self, sub_task_id: int, status: Optional[int] = None,
                                         start_time: Optional[str] = None,
                                         end_time: Optional[str] = None,
                                         error_code: Optional[int] = None,
                                         error_title: Optional[str] = None,
                                         error_detail: Optional[str] = None) -> bool:
        """更新子任务状态"""
        data = {
            'id': sub_task_id,
            **{k: v for k, v in {
                'status': status,
                'start_time': start_time,
                'end_time': end_time,
                'error_code': error_code,
                'error_title': error_title,
                'error_detail': error_detail
            }.items() if v is not None}
        }

        result = await self._request(
            'POST',
            f'/{perf_api_router}/perf_task/update_sub_task_status_by_id',
            json=data
        )
        
        success = result.get('code') == 200
        if success:
            status_msg = f"子任务 {sub_task_id} "
            if status is not None:
                status_msg += f"状态更新为: {status}"
            if error_code is not None:
                status_msg += f", 错误码: {error_code}"
            if error_title:
                status_msg += f", 错误标题: {error_title}"
            logger.info(status_msg)
        else:
            logger.error(f"更新子任务 {sub_task_id} 状态失败")
        return success

    # 任务队列相关接口
    @handle_api_exceptions
    async def get_head_sub_task(self, client_id: int) -> Optional[Dict]:
        """获取队头子任务"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_task_queue/get_head_sub_task',
            params={'client_id': client_id}
        )
        
        data = process_response(result, f"获取客户端 {client_id} 的队头子任务失败")
        if data:
            logger.info(f"获取到队头子任务: 子任务ID={data.get('id')}")
        return data

    @handle_api_exceptions
    async def remove_head_sub_task(self, client_id: int) -> bool:
        """移除队头子任务"""
        result = await self._request(
            'DELETE',
            f'/{perf_api_router}/perf_task_queue/remove_head_sub_task',
            params={'client_id': client_id}
        )
        
        success = result.get('code') == 200
        if success:
            logger.info(f"成功移除客户端 {client_id} 的队头子任务")
        else:
            logger.error(f"移除客户端 {client_id} 的队头子任务失败")
        return success

    @handle_api_exceptions
    async def move_to_tail(self, client_id: int) -> bool:
        """将任务移动到队尾"""
        result = await self._request(
            'PUT',
            f'/{perf_api_router}/perf_task_queue/move_to_tail',
            params={'client_id': client_id}
        )
        
        success = result.get('code') == 200
        if success:
            logger.info(f"客户端 {client_id} 的任务已移至队尾")
        else:
            logger.error(f"移动客户端 {client_id} 的任务至队尾失败")
        return success

    @handle_api_exceptions
    async def clear_queue(self, client_id: int) -> bool:
        """清空队列"""
        result = await self._request(
            'DELETE',
            f'/{perf_api_router}/perf_task_queue/clear',
            params={'client_id': client_id}
        )
        
        success = result.get('code') == 200
        if success:
            logger.info(f"成功清空客户端 {client_id} 的任务队列")
        else:
            logger.error(f"清空客户端 {client_id} 的任务队列失败")
        return success

    # 用例相关接口
    @handle_api_exceptions
    async def get_case_detail(self, case_id: int) -> Optional[Dict]:
        """获取用例详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_case/case_detail',
            params={'case_id': case_id}
        )
        
        data = process_response(result, f"获取用例 {case_id} 详情失败")
        return data.get('case') if data else None

    @handle_api_exceptions
    async def get_app_group_detail(self, group_id: int) -> Optional[Dict]:
        """获取应用组详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_app/app_group_detail',
            params={'app_group_id': group_id}
        )
        
        return process_response(result, f"获取应用组 {group_id} 详情失败")

    # 性能数据相关接口
    @handle_api_exceptions
    async def upload_perf_data(self, **kwargs) -> Optional[Dict]:
        """上传性能测试数据"""
        result = await self._request(
            method='PUT',
            endpoint=f'/{perf_api_router}/perf_data/upload_perf_data',
            json=kwargs
        )
        
        return process_response(result, "上传性能数据失败")

    @handle_api_exceptions
    async def upload_sub_task_case_run_detail(self, **kwargs) -> Optional[Dict]:
        """上传子任务用例运行详情"""

        result = await self._request(
            'POST',
            f'/{perf_api_router}/perf_task/upload_sub_task_case_run_detail',
            json=kwargs
        )
        
        return process_response(result, "上传子任务用例运行详情失败")

    @handle_api_exceptions
    async def get_sub_task_case_run_detail(self, sub_task_id: int) -> Optional[Dict]:
        """获取子任务用例执行详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_task/get_sub_task_case_run_detail',
            params={'sub_task_id': sub_task_id}
        )
        
        data = process_response(result, f"获取子任务 {sub_task_id} 用例执行详情失败")
        if data:
            logger.info(f"成功获取子任务 {sub_task_id} 用例执行详情")
        return data

    @handle_api_exceptions
    async def get_case_execution_stats(self, sub_task_id: int) -> Optional[Dict]:
        """获取子任务用例执行统计信息"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_task/get_case_execution_stats',
            params={'sub_task_id': sub_task_id}
        )
        
        data = process_response(result, f"获取子任务 {sub_task_id} 用例执行统计失败")
        if data:
            logger.debug(f"成功获取子任务 {sub_task_id} 用例执行统计信息")
        return data

    # 业务相关接口
    @handle_api_exceptions
    async def get_business_list(self) -> List[Dict]:
        """获取业务列表"""
        result = await self._request('GET', f'/{business_api_router}/fetch_all')
        
        data = process_response(result, "获取业务列表失败")
        if data:
            business_list = data.get('items', [])
            return business_list
        return []

    # 性能测试请求队列相关接口
    @handle_api_exceptions
    async def get_all_perf_requests(self, client_id: int) -> List[Dict]:
        """获取性能测试请求队列所有请求"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_request_queue/all',
            params={'client_id': client_id}
        )
        
        data = process_response(result, "获取性能测试请求队列失败")
        if data and isinstance(data, dict):
            requests = data.get('requests', [])
            return requests
        return []

    @handle_api_exceptions
    async def clear_perf_request_queue(self, client_id: int) -> bool:
        """清空性能测试请求队列"""
        result = await self._request(
            'DELETE',
            f'/{perf_api_router}/perf_request_queue/clear',
            params={'client_id': client_id}
        )
        
        success = result.get('code') == 200
        if success:
            logger.info(f"成功清空客户端 {client_id} 的性能测试请求队列")
        else:
            logger.error(f"清空客户端 {client_id} 的性能测试请求队列失败")
        return success

    # 实验相关接口
    @handle_api_exceptions
    async def get_experiment_detail(self, experiment_id: int) -> Optional[Dict]:
        """获取实验详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/experiment/detail?id={experiment_id}'
        )
        data = process_response(result, f"获取实验 {experiment_id} 详情失败")
        if data:
            logger.info(f"成功获取实验详情: 实验ID={experiment_id}")
        return data
    
    # 配置相关接口
    @handle_api_exceptions
    async def get_config_detail(self, config_id: int) -> Optional[Dict]:
        """获取配置详情"""
        result = await self._request(
            'GET',
            f'/{perf_api_router}/perf_config/config_detail',
            params={'config_id': config_id}
        )

        data = process_response(result, f"获取配置 {config_id} 详情失败")
        if data:
            logger.info(f"成功获取配置详情: 配置ID={config_id}")
        return data

perf_api = PerfAPI()