'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-11 15:30:47
FilePath: /global_rtc_client/ui/components/msg_box.py
Description: 消息对话框
'''
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QFrame, QWidget, QApplication
)
from PyQt6.QtCore import Qt

from ui.styles.containers.dialog import DialogStyle
from utils.common.log_utils import get_logger

logger = get_logger(__name__)

class MsgBox(QDialog):
    """现代风格的消息对话框"""
    
    def __init__(self, title: str, message: str, type: str = "info", parent=None, show_cancel: bool = False):
        """初始化消息对话框
        
        Args:
            title: 标题
            message: 消息内容
            type: 消息类型，可选值：info, warning, error, confirm
            parent: 父窗口
            show_cancel: 是否显示取消按钮，当type为confirm时默认为True
        """
        super().__init__(parent)
        # 设置无边框和背景透明
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        self.type = type
        # 如果是confirm类型，默认显示取消按钮
        self.show_cancel = show_cancel or type == "confirm"
        self.setup_ui(title, message)
        
        # 设置窗口显示位置为屏幕中心
        self.center_on_screen()
        
    def center_on_screen(self):
        """将对话框居中显示"""
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)
        
    def show(self) -> None:
        """重写show方法，确保窗口总是显示在最前面"""
        super().show()
        self.raise_()
        self.activateWindow()
        
    def setup_ui(self, title: str, message: str):
        """设置UI"""
        # 设置固定大小
        self.setFixedSize(420, 180)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(0)
        
        # 内容容器
        content_frame = QFrame(self)
        content_frame.setObjectName("container")
        
        # 内容布局
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 16)
        content_layout.setSpacing(8)
        
        # 标题
        title_label = QLabel(title)
        title_label.setObjectName("titleLabel")
        content_layout.addWidget(title_label)
        
        # 消息
        message_label = QLabel(message)
        message_label.setObjectName("messageLabel")
        message_label.setWordWrap(True)
        content_layout.addWidget(message_label)
        
        content_layout.addStretch(1)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(12)
        button_layout.addStretch()
        
        if self.show_cancel:
            # 取消按钮
            cancel_button = QPushButton("取消")
            cancel_button.setObjectName("cancelButton")
            cancel_button.setFixedSize(96, 36)
            cancel_button.setCursor(Qt.CursorShape.PointingHandCursor)
            cancel_button.clicked.connect(self.reject)
            button_layout.addWidget(cancel_button)
        
        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.setObjectName("okButton")
        ok_button.setFixedSize(96, 36)
        ok_button.setCursor(Qt.CursorShape.PointingHandCursor)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)
        
        content_layout.addLayout(button_layout)
        main_layout.addWidget(content_frame)
        
        # 应用样式
        self.setStyleSheet(DialogStyle.get_message_box_style(self.type))
        
    def closeEvent(self, event):
        """重写关闭事件，确保正确清理资源"""
        super().closeEvent(event)
        self.deleteLater()  # 确保对话框被销毁
        