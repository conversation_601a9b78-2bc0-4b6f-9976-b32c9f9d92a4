'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-11 14:34:42
FilePath: /global_rtc_client/utils/common/network_utils.py
Description: 网络工具模块,提供HTTP请求和文件下载功能
'''
import os
import ssl
import time
from enum import Enum
from typing import Optional, Dict, Any, Set
import aiohttp
import requests
import asyncio
from dataclasses import dataclass
from utils.common.log_utils import get_logger
import aiofiles
import sys

logger = get_logger(__name__)

@dataclass
class TimeoutConfig:
    """超时配置"""
    total: int = 600        # 总超时时间(秒)
    connect: int = 60       # 连接超时时间(秒)
    sock_read: int = 300    # Socket读取超时时间(秒)
    sock_connect: int = 60  # Socket连接超时时间(秒)

@dataclass
class DownloadConfig:
    """下载配置"""
    chunk_size: int = 1024 * 1024      # 1MB
    write_buffer: int = 8 * 1024 * 1024 # 8MB
    retry_count: int = 3
    retry_delay: int = 5

class RequestMethod(Enum):
    """HTTP请求方法"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"

def format_size(size_bytes: float) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f}{unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f}PB"


def format_speed(speed_bytes: float) -> str:
    """格式化速度"""
    return f"{format_size(speed_bytes)}/s"


def format_time(seconds: float) -> str:
    """格式化时间"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

# 全局变量，用于跟踪所有下载任务
_active_downloads: Set[asyncio.Task] = set()


class DownloadProgressBar:
    """下载进度条显示类"""
    
    # ANSI 颜色代码
    COLORS = {
        'green': '\033[38;5;82m',    # 亮绿色
        'blue': '\033[38;5;39m',     # 亮蓝色
        'yellow': '\033[38;5;220m',  # 亮黄色
        'red': '\033[38;5;196m',     # 亮红色
        'bold': '\033[1m',           # 加粗
        'end': '\033[0m',            
        'gray': '\033[38;5;240m',    # 深灰色
        'light_gray': '\033[38;5;250m'  # 浅灰色
    }
    
    # ANSI 控制代码
    CONTROLS = {
        'clear_line': '\033[K',      # 清除当前行
        'clear_screen_down': '\033[J',  # 清除从光标到屏幕底部的内容
        'up_line': '\033[1A',        # 光标上移一行
        'down_line': '\033[1B',      # 光标下移一行
        'save_pos': '\033[s',        # 保存光标位置
        'restore_pos': '\033[u',     # 恢复光标位置
        'hide_cursor': '\033[?25l',  # 隐藏光标
        'show_cursor': '\033[?25h',  # 显示光标
        'move_to_start': '\033[G'    # 移动到行首
    }
    
    # 进度条字符
    PROGRESS_CHARS = {
        'start': '│',      # 使用竖线作为边框
        'end': '│',
        'fill': '█',       # 实心方块
        'remain': '░',     # 浅色方块
        'separator': '•'    # 圆点分隔符
    }
    
    def __init__(self, total_size: int, width: int = 35, filename_width: int = 30):
        self.total_size = total_size
        self.width = width
        self.filename_width = filename_width
        self.is_first_update = True
        self.last_progress = 0
        self.last_update_time = time.time()
        self.update_interval = 0.1  # 最小更新间隔(秒)
        self.is_tty = sys.stdout.isatty()
        if self.is_tty:
            print(self.CONTROLS['hide_cursor'], end='')
        
    def __del__(self):
        if self.is_tty:
            print(self.CONTROLS['show_cursor'], end='')

    def format_progress(
        self,
        filename: str,
        downloaded_size: int,
        speed: float,
        eta: float,
        is_complete: bool = False
    ) -> str:
        """格式化完整的进度信息"""
        current_time = time.time()
        
        # 控制更新频率，避免刷新过快
        if not is_complete and current_time - self.last_update_time < self.update_interval:
            return ""
            
        # 计算进度
        progress = 100.0 if is_complete else (downloaded_size / self.total_size * 100 if self.total_size else 0)
        
        # 如果进度变化太小且未完成，跳过更新
        if not is_complete and abs(progress - self.last_progress) < 0.1:
            return ""
            
        self.last_progress = progress
        self.last_update_time = current_time
        
        # 格式化进度条
        bar = self._format_bar(progress)
        
        # 格式化文件名和其他数值
        formatted_filename = self._format_filename(os.path.basename(filename))
        progress_str = f"{progress:5.1f}%"
        size_str = (f"{format_size(downloaded_size)}/"
                   f"{format_size(self.total_size)}")
        speed_str = format_speed(speed)
        eta_str = "完成" if is_complete else (format_time(eta) if eta > 0 else "计算中")
        
        # 使用圆点作为分隔符
        sep = f" {self.COLORS['gray']}{self.PROGRESS_CHARS['separator']}{self.COLORS['end']} "
        
        # 构建进度信息
        progress_line = (
            f"{self.COLORS['bold']}{formatted_filename}{self.COLORS['end']} "
            f"{bar} "
            f"{self.COLORS['blue']}{progress_str}{self.COLORS['end']}"
            f"{sep}{size_str}"
            f"{sep}{self.COLORS['yellow']}{speed_str}{self.COLORS['end']}"
            f"{sep}{self.COLORS['green']}{eta_str}{self.COLORS['end']}"
        )

        if self.is_tty:
            # 用 \r 回到行首刷新
            output = f"\r{progress_line}"
            if is_complete:
                output = f"\r{progress_line}" + self.CONTROLS['show_cursor'] + '\n'
            return output
        else:
            # 非终端环境，直接输出文本
            return f"{progress_line}\n" if is_complete else f"{progress_line}"

    def complete(self) -> None:
        """完成下载时的清理操作"""
        if self.is_tty:
            print(self.CONTROLS['show_cursor'], end='')

    def _format_bar(self, progress: float) -> str:
        """格式化进度条"""
        filled_width = int(progress / 100 * self.width)
        color = self._get_color(progress)
        
        # 构建更美观的进度条
        bar = (
            f"{self.COLORS['light_gray']}{self.PROGRESS_CHARS['start']}"
            f"{color}"
            f"{self.PROGRESS_CHARS['fill'] * filled_width}"
        )
        
        if filled_width < self.width:
            remain_width = self.width - filled_width
            bar += f"{self.COLORS['gray']}{self.PROGRESS_CHARS['remain'] * remain_width}"
        
        bar += f"{self.COLORS['light_gray']}{self.PROGRESS_CHARS['end']}{self.COLORS['end']}"
        return bar
        
    def _format_filename(self, filename: str) -> str:
        """格式化文件名显示
        
        如果文件名超过限制长度，保留开头和结尾，中间用...替代
        """
        if len(filename) <= self.filename_width:
            return filename.ljust(self.filename_width)
            
        # 保留的开头和结尾长度
        head_len = (self.filename_width - 3) // 2
        tail_len = self.filename_width - 3 - head_len
        
        return f"{filename[:head_len]}...{filename[-tail_len:]}"
        
    def _get_color(self, progress: float) -> str:
        """根据进度返回颜色"""
        if progress >= 100:
            return self.COLORS['green']
        elif progress >= 80:
            return self.COLORS['blue']
        elif progress >= 40:
            return self.COLORS['yellow']
        else:
            return self.COLORS['red']

class NetworkUtils:
    """网络工具类,提供同步和异步的HTTP请求方法"""
    
    def __init__(self, timeout_config: Optional[TimeoutConfig] = None):
        """初始化网络工具类"""
        self.timeout_config = timeout_config or TimeoutConfig()
        self.download_config = DownloadConfig()
        self.ssl_context = self._create_ssl_context()
        self.progress_update_interval = 1.0  # 设置为1秒更新一次
        # 添加重试配置
        self.max_connection_retries = 3
        self.connection_retry_delay = 5
        
    def _create_ssl_context(self) -> ssl.SSLContext:
        """创建SSL上下文"""
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        return context

    def _create_session_config(self, timeout: Optional[int] = None) -> Dict:
        """创建会话配置"""
        return {
            'timeout': aiohttp.ClientTimeout(
                total=timeout or self.timeout_config.total,
                connect=self.timeout_config.connect,
                sock_read=self.timeout_config.sock_read,
                sock_connect=self.timeout_config.sock_connect
            ),
            'connector': aiohttp.TCPConnector(
                limit=0,
                force_close=True,
                enable_cleanup_closed=True,
                ssl=False,
                use_dns_cache=True,
                ttl_dns_cache=300
            ),
            'trust_env': True,
            'headers': {
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'User-Agent': 'Mozilla/5.0'
            }
        }

    @staticmethod
    def send_request(method: str, url: str, **kwargs) -> requests.Response:
        """发送同步HTTP请求"""
        try:
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            raise

    async def async_request(
            self,
            method: str,
            url: str,
            headers: Optional[Dict] = None,
            params: Optional[Dict] = None,
            json: Optional[Dict] = None,
            verify_ssl: bool = False
    ) -> Any:
        """发送异步HTTP请求"""
        ssl_context = True if verify_ssl else self.ssl_context
        session_config = self._create_session_config()
        
        try:
            async with aiohttp.ClientSession(**session_config) as session:
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=json,
                    ssl=ssl_context
                ) as response:
                    response.raise_for_status()
                    return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"请求失败: {str(e)}")
            raise

    async def download_file(self, url: str, save_path: str, timeout: Optional[int] = None) -> Optional[str]:
        """下载文件
        
        Args:
            url: 文件URL
            save_path: 保存路径
            timeout: 总超时时间(秒)
            
        Returns:
            str: 保存路径, 失败返回 None
        """
        retry_count = 0
        downloaded_size = 0
        total_timeout = timeout or self.timeout_config.total

        while retry_count <= self.download_config.retry_count:
            try:
                # 验证URL
                if not self._validate_url(url):
                    return None

                # 创建保存目录
                os.makedirs(os.path.dirname(save_path), exist_ok=True)

                # 准备下载
                session_config = self._create_session_config(total_timeout)
                if downloaded_size > 0:
                    session_config['headers']['Range'] = f'bytes={downloaded_size}-'

                # 开始下载
                download_task = asyncio.current_task()
                _active_downloads.add(download_task)

                try:
                    return await self._perform_download(
                        url, 
                        save_path, 
                        session_config, 
                        downloaded_size
                    )
                except asyncio.CancelledError:
                    logger.info(f"下载任务被取消: {url}")
                    self._cleanup_file(save_path)
                    raise
                finally:
                    _active_downloads.discard(download_task)

            except Exception as e:
                retry_count = await self._handle_download_error(
                    e, retry_count, total_timeout, save_path
                )
                if retry_count is None:  # 达到最大重试次数
                    return None
                continue

        return None

    def _validate_url(self, url: str) -> bool:
        """验证URL是否有效"""
        if not url or not isinstance(url, str) or not url.startswith(('http://', 'https://')):
            logger.error(f"无效的URL: {url}")
            return False
        return True

    async def _perform_download(
        self, 
        url: str, 
        save_path: str, 
        session_config: Dict,
        downloaded_size: int
    ) -> Optional[str]:
        """执行文件下载"""
        connection_retries = 0
        while connection_retries <= self.max_connection_retries:
            try:
                async with aiohttp.ClientSession(**session_config) as session:
                    async with session.get(url, ssl=False) as response:
                        if response.status not in (200, 206):
                            response_text = await response.text()
                            logger.error(f"下载请求失败: HTTP {response.status}, 响应: {response_text}")
                            return None

                        # 验证服务器是否支持断点续传
                        supports_resume = 'bytes' in response.headers.get('Accept-Ranges', '')
                        content_length = int(response.headers.get('Content-Length', 0))
                        
                        if downloaded_size > 0 and not supports_resume:
                            logger.warning("服务器不支持断点续传，将从头开始下载")
                            downloaded_size = 0

                        return await self._save_download_content(
                            response, 
                            save_path, 
                            downloaded_size,
                            content_length,
                            supports_resume
                        )
                        
            except (aiohttp.ClientError, ConnectionResetError) as e:
                connection_retries += 1
                if connection_retries <= self.max_connection_retries:
                    logger.warning(f"连接错误: {str(e)}, 将在{self.connection_retry_delay}秒后进行第{connection_retries}次重试")
                    await asyncio.sleep(self.connection_retry_delay)
                    continue
                else:
                    logger.error(f"连接重试次数已达上限: {str(e)}")
                    return None
                    
            except Exception as e:
                logger.error(f"下载过程中发生未预期的错误: {str(e)}")
                return None

        return None

    async def _save_download_content(
        self, 
        response: aiohttp.ClientResponse, 
        save_path: str,
        downloaded_size: int,
        content_length: int,
        supports_resume: bool
    ) -> Optional[str]:
        """保存下载内容到文件"""
        if content_length == 0:
            logger.warning("无法获取文件大小")
            content_length = downloaded_size  # 使用已下载大小作为备选

        # 创建进度条实例
        progress_bar = DownloadProgressBar(content_length)
        
        stats = {
            'downloaded_size': downloaded_size,
            'start_time': time.time(),
            'last_progress_time': time.time(),
            'last_downloaded_size': downloaded_size
        }

        file_mode = 'ab' if downloaded_size > 0 and supports_resume else 'wb'
        temp_file_path = save_path + '.tmp'
        
        try:
            async with aiofiles.open(temp_file_path, file_mode, buffering=self.download_config.write_buffer) as file:
                async for chunk in response.content.iter_chunked(self.download_config.chunk_size):
                    if not chunk:
                        continue

                    await file.write(chunk)
                    stats['downloaded_size'] += len(chunk)

                    # 更新进度显示
                    current_time = time.time()
                    if current_time - stats['last_progress_time'] >= self.progress_update_interval:
                        await self._update_download_progress(
                            progress_bar,
                            save_path,
                            stats['downloaded_size'],
                            stats['last_downloaded_size'],
                            content_length,
                            current_time,
                            stats['last_progress_time'],
                            is_complete=False
                        )
                        stats['last_progress_time'] = current_time
                        stats['last_downloaded_size'] = stats['downloaded_size']

                # 下载完成时强制显示100%进度
                await self._update_download_progress(
                    progress_bar,
                    save_path,
                    content_length,
                    stats['last_downloaded_size'],
                    content_length,
                    time.time(),
                    stats['last_progress_time'],
                    is_complete=True
                )
                progress_bar.complete()

            # 验证下载
            if await self._verify_download(temp_file_path, content_length):
                # 下载验证成功，将临时文件重命名为目标文件
                if os.path.exists(save_path):
                    os.remove(save_path)
                os.rename(temp_file_path, save_path)
                
                # 输出下载完成信息
                self._log_download_complete(save_path, stats)
                return save_path
            else:
                # 验证失败，删除临时文件
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                return None

        except Exception as e:
            logger.error(f"保存文件时发生错误: {str(e)}")
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
            return None

    async def _verify_download(self, save_path: str, total_size: int) -> bool:
        """验证下载文件的完整性
        
        Args:
            save_path: 文件保存路径
            total_size: 预期的文件总大小
            
        Returns:
            bool: 验证是否成功
        """
        try:
            if not os.path.exists(save_path):
                logger.error("下载后文件不存在")
                return False

            actual_size = os.path.getsize(save_path)
            if total_size > 0 and actual_size != total_size:
                logger.error(f"文件大小不匹配: 期望 {total_size} 字节, 实际 {actual_size} 字节")
                return False

            # 验证文件是否可以打开和读取
            with open(save_path, 'rb') as f:
                # 读取文件头部
                try:
                    header = f.read(8192)
                    if not header:
                        logger.error("无法读取文件头部")
                        return False
                except Exception as e:
                    logger.error(f"读取文件头部时发生错误: {str(e)}")
                    return False

                # 读取文件尾部
                try:
                    if actual_size > 16384:  # 如果文件大于16KB
                        f.seek(max(0, actual_size - 8192))  # 确保不会出现负偏移
                        footer = f.read()
                        if not footer:
                            logger.error("无法读取文件尾部")
                            return False
                except Exception as e:
                    logger.error(f"读取文件尾部时发生错误: {str(e)}")
                    return False

            return True

        except OSError as e:
            logger.error(f"文件系统操作错误: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"文件验证时发生未预期的错误: {str(e)}")
            return False

    def _log_download_complete(self, save_path: str, stats: Dict) -> None:
        """记录下载完成信息"""
        total_time = time.time() - stats['start_time']
        actual_size = os.path.getsize(save_path)
        avg_speed = actual_size / total_time if total_time > 0 else 0
        print()
        logger.info(
            f"文件大小: {format_size(actual_size)} "
            f"总耗时: {format_time(total_time)} "
            f"平均速度: {format_speed(avg_speed)}"
        )

    async def _handle_download_error(
        self, 
        error: Exception, 
        retry_count: int,
        total_timeout: int,
        save_path: str
    ) -> Optional[int]:
        """处理下载错误"""
        error_msg = str(error)
        if isinstance(error, asyncio.TimeoutError):
            error_msg = f"下载超时 (>{total_timeout}秒)"
        elif isinstance(error, aiohttp.ClientError):
            error_msg = f"HTTP请求失败: {error_msg}"
        
        logger.error(error_msg)
        
        if retry_count < self.download_config.retry_count:
            retry_count += 1
            await asyncio.sleep(self.download_config.retry_delay)
            return retry_count
            
        return None

    async def _update_download_progress(
        self,
        progress_bar: DownloadProgressBar,
        save_path: str,
        downloaded_size: int,
        last_downloaded_size: int,
        total_size: int,
        current_time: float,
        last_progress_time: float,
        is_complete: bool = False
    ) -> None:
        """更新下载进度显示"""
        # 计算速度和预计剩余时间
        time_diff = current_time - last_progress_time
        if time_diff > 0:
            instant_speed = (downloaded_size - last_downloaded_size) / time_diff
            eta_seconds = 0 if is_complete else (
                (total_size - downloaded_size) / instant_speed 
                if instant_speed > 0 and total_size > 0 else 0
            )
            
            # 使用进度条类格式化显示
            progress_line = progress_bar.format_progress(
                filename=os.path.basename(save_path),
                downloaded_size=downloaded_size,
                speed=instant_speed,
                eta=eta_seconds,
                is_complete=is_complete
            )
            
            # 输出进度信息
            print(progress_line, end='', flush=True)
            
            # 如果下载完成，调用完成处理
            if is_complete:
                progress_bar.complete()

    def _cleanup_file(self, file_path: str) -> None:
        """清理文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除文件: {file_path}")
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")

    async def cancel_all_downloads(self) -> bool:
        """取消所有下载任务"""
        try:
            if not _active_downloads:
                return True

            logger.info(f"正在取消 {len(_active_downloads)} 个下载任务")
            for task in _active_downloads:
                if not task.done():
                    task.cancel()
            
            if _active_downloads:
                await asyncio.gather(*_active_downloads, return_exceptions=True)
            
            _active_downloads.clear()
            logger.info("所有下载任务已取消")
            return True
            
        except Exception as e:
            logger.error(f"取消下载任务时发生错误: {str(e)}")
            return False

network_utils = NetworkUtils()
