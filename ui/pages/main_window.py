"""
主窗口
"""
import os
import aiohttp
import asyncio
from typing import Optional
from PyQt6.QtCore import Qt, QSize, QPoint, QMetaObject, Q_ARG, pyqtSlot, QTimer
from PyQt6.QtGui import QPixmap, QIcon, QImage, QAction, QColor, QPainter, QBitmap
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QStackedWidget,
    QFrame, QMenu, QMessageBox, QApplication
)

from core.auth_manager import auth_manager
from ui.pages.auth_window import AuthWindow
from ui.pages.perf_window import PerfWindow
from ui.pages.settings_window import SettingsWindow
from ui.styles import (
    ButtonStyle, 
    MenuStyle,
    WindowStyle,
)
from utils.common.log_utils import get_logger
from core.main_manager import main_manager
from core.perf_manager import perf_manager
from ui.components.msg_box import MsgBox
from ui.components.icon_engine import create_colored_icon

logger = get_logger(__name__)


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        
        # 使用导入的单例
        self.auth_manager = auth_manager
        self.main_manager = main_manager
        self.perf_manager = perf_manager

        # 初始化窗口和对话框
        self.perf_window = PerfWindow()
        self.settings_window = SettingsWindow()

        # 初始化UI控件
        self.breadcrumb_label = QLabel("性能")
        self.search_input = QLineEdit()
        self.avatar_button = QPushButton()
        self.version_label = QLabel()  # 添加版本标签
        
        # 初始化菜单项
        self.user_menu = None
        self.user_info_action = None
        self.dept_info_action = None
        self.settings_action = None
        self.restart_action = None
        self.logout_action = None

        # 初始化堆叠窗口部件
        self.stacked_widget = QStackedWidget()

        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.Window)  # 使用系统窗口装饰
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)  # 允许半透明

        # 设置窗口尺寸和限制
        self.setMinimumSize(1024, 768)  # 设置最小尺寸
        self.resize(1280, 800)  # 设置推荐默认尺寸

        # 设置UI
        self.setup_menubar()  # 先设置菜单栏
        self.setup_ui()
        self.setup_menu()

        # 连接头像按钮点击事件
        self.avatar_button.clicked.connect(self.show_user_menu)

        # 应用现代风格
        self.setStyleSheet(WindowStyle.get_main_window_base_style())

        # 设置窗口标题
        self.setWindowTitle("Global RTC Client")

        # 添加属性来控制重绘
        self._resize_timer = None

        # 优化窗口绘制
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_OpaquePaintEvent)

        # 添加窗口状态追踪
        self._maximized = False
        self._shadow_enabled = True

        self.auth_window = None  # 添加登录窗口引用

        # 初始化页面状态
        self.stacked_widget.addWidget(self.perf_window)  # 添加性能自动化页面
        self.stacked_widget.addWidget(self.settings_window)  # 添加设置页面
        self.stacked_widget.setCurrentWidget(self.perf_window)  # 默认显示性能自动化页面

    def setup_menubar(self) -> None:
        """设置菜单栏"""
        menubar = self.menuBar()
        menubar.setStyleSheet(MenuStyle.get_menu_bar_style())
        # 确保菜单栏可见
        menubar.setVisible(True)

        # 创建帮助菜单
        self.help_menu = menubar.addMenu("帮助")
        
        # 添加分隔线
        self.help_menu.addSeparator()

    def _show_help_dialog(self) -> None:
        """显示帮助对话框"""
        try:
            from ui.dialogs.help_dialog import HelpDialog
            dialog = HelpDialog(self)
            dialog.exec()
        except Exception as e:
            logger.error(f"显示帮助对话框失败: {str(e)}")
            self._show_message("错误", "显示帮助对话框失败", "error")

    def setup_ui(self) -> None:
        """设置UI组件"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建顶部工具栏
        toolbar = QWidget()
        toolbar.setFixedHeight(48)
        toolbar.setStyleSheet(WindowStyle.get_toolbar_style())
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(0, 0, 24, 0)
        toolbar_layout.setSpacing(16)

        # 左侧Logo和版本信息容器
        left_container = QWidget()
        left_layout = QHBoxLayout(left_container)
        left_layout.setContentsMargins(24, 0, 0, 0)
        left_layout.setSpacing(8)

        # Logo
        logo_label = QLabel()
        logo_label.setFixedSize(200, 32)
        logo_icon = QIcon("ui/icons/logo/app_logo.svg")
        logo_label.setPixmap(logo_icon.pixmap(200, 32))
        left_layout.addWidget(logo_label)

        # 版本标签
        self.version_label = QLabel(f"当前版本: {self.main_manager.current_commit_id[:8]}")
        self.version_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 12px;
            }
        """)
        left_layout.addWidget(self.version_label)
        
        toolbar_layout.addWidget(left_container)
        toolbar_layout.addStretch()  # 添加弹性空间
        
        # 右侧按钮区域
        right_buttons = QWidget()
        right_layout = QHBoxLayout(right_buttons)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(8)

        # 重启应用按钮
        restart_button = QPushButton()
        restart_button.setFixedSize(36, 36)
        restart_button.setCursor(Qt.CursorShape.PointingHandCursor)
        restart_button.setIcon(create_colored_icon("ui/icons/actions/restart.svg"))
        restart_button.setIconSize(QSize(20, 20))
        restart_button.setToolTip("重启应用")
        restart_button.setStyleSheet(ButtonStyle.get_toolbar_button_style())
        restart_button.clicked.connect(self._handle_restart_action)
        right_layout.addWidget(restart_button)

        # 帮助按钮
        help_button = QPushButton()
        help_button.setFixedSize(36, 36)
        help_button.setCursor(Qt.CursorShape.PointingHandCursor)
        help_button.setIcon(create_colored_icon("ui/icons/navigation/help.svg"))
        help_button.setIconSize(QSize(20, 20))
        help_button.setToolTip("帮助")
        help_button.setStyleSheet(ButtonStyle.get_toolbar_button_style())
        help_button.clicked.connect(self._show_help_dialog)
        right_layout.addWidget(help_button)

        # 文档按钮
        docs_button = QPushButton()
        docs_button.setFixedSize(36, 36)
        docs_button.setCursor(Qt.CursorShape.PointingHandCursor)
        docs_button.setIcon(create_colored_icon("ui/icons/navigation/docs.svg"))
        docs_button.setIconSize(QSize(20, 20))
        docs_button.setToolTip("文档")
        docs_button.setStyleSheet(ButtonStyle.get_toolbar_button_style())
        docs_button.clicked.connect(self._show_docs_dialog)
        right_layout.addWidget(docs_button)

        # 更新按钮
        update_button = QPushButton()
        update_button.setFixedSize(36, 36)
        update_button.setCursor(Qt.CursorShape.PointingHandCursor)
        update_button.setIcon(create_colored_icon("ui/icons/actions/update.svg"))
        update_button.setIconSize(QSize(20, 20))
        update_button.setToolTip("检查更新")
        update_button.setStyleSheet(ButtonStyle.get_toolbar_button_style())
        update_button.clicked.connect(self._handle_update_action)
        right_layout.addWidget(update_button)

        # 用户头像按钮
        self.avatar_button = QPushButton()
        self.avatar_button.setFixedSize(36, 36)
        self.avatar_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.avatar_button.setStyleSheet(
            ButtonStyle.get_avatar_button_style() +
            ButtonStyle.get_avatar_size_style(36)
        )
        right_layout.addWidget(self.avatar_button)

        toolbar_layout.addWidget(right_buttons)
        layout.addWidget(toolbar)

        # 主内容区域
        content = QWidget()
        content_layout = QHBoxLayout(content)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 左侧菜单
        self.menu_widget = QWidget()
        self.menu_widget.setStyleSheet(MenuStyle.get_menu_widget_style())
        menu_layout = QVBoxLayout(self.menu_widget)
        menu_layout.setContentsMargins(0, 0, 0, 0)
        menu_layout.setSpacing(2)

        # 添加菜单按钮
        self.perf_btn = QPushButton("性能")
        self.perf_btn.setCheckable(True)
        self.perf_btn.setChecked(True)
        self.perf_btn.clicked.connect(lambda: self.switch_page(self.perf_window))
        self.perf_btn.setIcon(create_colored_icon("ui/icons/navigation/perf.svg"))
        self.perf_btn.setIconSize(QSize(18, 18))
        menu_layout.addWidget(self.perf_btn)

        # 添加设置按钮到左侧菜单
        self.settings_btn = QPushButton("设置")
        self.settings_btn.setCheckable(True)
        self.settings_btn.clicked.connect(lambda: self.switch_page(self.settings_window))
        self.settings_btn.setIcon(create_colored_icon("ui/icons/navigation/settings.svg"))
        self.settings_btn.setIconSize(QSize(18, 18))
        menu_layout.addWidget(self.settings_btn)

        menu_layout.addStretch()  # 添加弹性空间

        content_layout.addWidget(self.menu_widget)

        # 右侧内容
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet(MenuStyle.get_stacked_widget_style())
        content_layout.addWidget(self.stacked_widget)

        layout.addWidget(content)

    def setup_menu(self) -> None:
        """设置用户菜单"""
        self.user_menu = QMenu(self)
        self.user_menu.setStyleSheet(MenuStyle.get_user_menu_style())

        # 创建所有菜单项并保存引用
        self.menu_actions = []

        # 用户信息（不可点击）
        self.user_info_action = QAction("用户: ", self)
        self.user_info_action.setEnabled(False)
        self.menu_actions.append(self.user_info_action)

        # 部门信息（不可点击）
        self.dept_info_action = QAction("部门: ", self)
        self.dept_info_action.setEnabled(False)
        self.menu_actions.append(self.dept_info_action)

        # 分隔线
        separator = QAction(self)
        separator.setSeparator(True)
        self.menu_actions.append(separator)

        # 退出登录
        self.logout_action = QAction("退出登录", self)
        self.logout_action.triggered.connect(self.logout)
        self.menu_actions.append(self.logout_action)

        # 添所有菜单项
        for action in self.menu_actions:
            self.user_menu.addAction(action)

    def _handle_restart_action(self) -> None:
        """处理重启动作"""
        try:
            # 先隐藏主窗口
            self.hide()
            
            # 创建并保存重启提示对话框的引用
            self.restart_dialog = MsgBox(
                "系统重启",
                "正在重启应用程序，请稍候...",
                "info",
                None,  # 不设置父窗口，确保独立显示
                False  # 不显示取消按钮
            )
            self.restart_dialog.show()  # 使用 show 而不是 exec，保持非模态
            
            # 使用 QTimer 延迟执行重启操作，确保UI更新
            QTimer.singleShot(100, self._schedule_restart)
                
        except Exception as e:
            logger.error(f"重启应用失败: {str(e)}")
            self._show_message("错误", f"重启应用失败: {str(e)}", "error")

    def _schedule_restart(self) -> None:
        """调度重启操作"""
        try:
            if self.main_manager:
                # 连接重启完成信号
                self.main_manager.restart_requested.connect(self._handle_restart_complete)
                
                # 创建事件循环
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.main_manager._async_restart())
                else:
                    loop.run_until_complete(self.main_manager._async_restart())
            
        except Exception as e:
            logger.error(f"调度重启操作失败: {str(e)}")
            if hasattr(self, 'restart_dialog'):
                self.restart_dialog.close()
            self._show_message("错误", "重启失败", "error")

    def _handle_restart_complete(self):
        """处理重启完成"""
        try:
            # 关闭所有窗口
            if self.perf_window:
                self.perf_window.close()

            # 关闭重启提示对话框
            if hasattr(self, 'restart_dialog'):
                self.restart_dialog.close()
                delattr(self, 'restart_dialog')

            # 退出应用
            QApplication.quit()
            
        except Exception as e:
            logger.error(f"处理重启完成失败: {str(e)}")
            if hasattr(self, 'restart_dialog'):
                self.restart_dialog.close()
            self._show_message("错误", "重启失败", "error")

    def logout(self) -> None:
        """登出"""
        try:
            # 创建事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(self._async_logout())
            else:
                loop.run_until_complete(self._async_logout())
            
        except Exception as e:
            logger.error(f"登出失败: {str(e)}")
            self._show_message("错误", "登出失败", "error")

    async def _async_logout(self) -> None:
        """异步执行登出"""
        try:
            if self.auth_manager:
                # 先关闭所有子窗口
                if self.perf_window:
                    self.perf_window.close()

                # 执行登出
                success = await self.auth_manager.async_logout()
                if success:
                    # 隐藏主窗口
                    self.hide()

                    # 显示登录窗口
                    self._show_auth_window()
                else:
                    logger.error("登出失败")
                    self._show_message("错误", "登出失败", "error")

        except Exception as e:
            logger.error(f"异步登出失败: {str(e)}")
            self._show_message("错误", "登出失败", "error")

    def _show_auth_window(self):
        """显示登录窗口"""
        try:
            # 创建新的登录窗口
            if not self.auth_window:
                self.auth_window = AuthWindow()  # 不传入 self 参数
                self.auth_window.auth_success.connect(self._on_auth_success)
            
            # 显示登录窗口
            self.auth_window.show()
            
            # 居中显示
            self.center_window(self)
            
        except Exception as e:
            logger.error(f"显示登录窗口失败: {str(e)}")
            self._show_message("错误", "显示登录窗口失败", "error")

    def _on_auth_success(self):
        """认证成功处理"""
        try:
            # 隐藏认证窗口
            if self.auth_window:
                self.auth_window.hide()
                self.auth_window = None

            # 获取当前用户信息
            user_info = self.auth_manager.get_current_user()
            if not user_info:
                logger.error("无法获取当前用户信息")
                return

            # 先清理旧的窗口
            if hasattr(self, 'perf_window') and self.perf_window:
                self.perf_window.deleteLater()

            # 清理堆叠窗口部件中的所有页面
            if hasattr(self, 'stacked_widget'):
                while self.stacked_widget.count():
                    widget = self.stacked_widget.widget(0)
                    self.stacked_widget.removeWidget(widget)
                    if widget:
                        widget.deleteLater()

            # 重新创建子窗口
            self.perf_window = PerfWindow()
            
            # 添加到堆叠窗口
            self.stacked_widget.addWidget(self.perf_window)
            
            # 设置默认显示页面
            self.stacked_widget.setCurrentWidget(self.perf_window)

            # 更新用户信息显示
            self.update_user_info()

            # 显示主窗口
            self.show()

        except Exception as e:
            logger.error(f"处理认证成功事件失败: {str(e)}")
            logger.exception(e)  # 打印完整堆栈信息

    def closeEvent(self, event) -> None:
        """关闭事件处理"""
        event.accept()

    async def _load_avatar(self, url: str) -> Optional[QPixmap]:
        """
        加载头像图片
        
        Args:
            url: 头像URL
            
        Returns:
            QPixmap: 头像图片
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, ssl=False) as response:
                    if response.status == 200:
                        data = await response.read()
                        image = QImage()
                        image.loadFromData(data)
                        if not image.isNull():
                            # 将图成圆形
                            pixmap = QPixmap.fromImage(image)
                            pixmap = pixmap.scaled(
                                36, 36,
                                Qt.AspectRatioMode.KeepAspectRatio,
                                Qt.TransformationMode.SmoothTransformation
                            )
                            return pixmap
        except Exception as e:
            logger.error(f"加载头像失败: {str(e)}")
        return None

    def showEvent(self, event) -> None:
        """显示事件"""
        super().showEvent(event)
        self.update_user_avatar()
        
        # 连接版本更新信号
        self.main_manager.version_updated.connect(self._on_version_updated)

    def show_user_menu(self) -> None:
        """显示用户菜单"""
        try:
            # 更新用户信息
            current_user = auth_manager.get_current_user()
            if current_user:
                # 更新用户名
                self.user_info_action.setText(f"用户: {current_user.get('name')} ({current_user.get('username')})")

                # 更新部门信息
                department = current_user.get('department', '')
                if isinstance(department, dict):
                    dept_name = department.get('name', '')
                else:
                    dept_name = department
                self.dept_info_action.setText(f"部门: {dept_name}")

                # 更新菜单项可见性
                self.user_info_action.setVisible(True)
                self.dept_info_action.setVisible(True)

                # 计算菜单显示位置
                button_pos = self.avatar_button.mapToGlobal(self.avatar_button.rect().bottomLeft())
                menu_x = button_pos.x()
                menu_y = button_pos.y() + 5  # 添加一些垂直间距

                # 显示菜单
                self.user_menu.popup(QPoint(menu_x, menu_y))
            else:
                # 如果没有用户信息，隐藏菜单项
                self.user_info_action.setVisible(False)
                self.dept_info_action.setVisible(False)

        except Exception as e:
            logger.error(f"显示用户菜单失败: {str(e)}")

    def switch_page(self, page: QWidget) -> None:
        """切换页面"""
        try:
            # 更新面包屑
            if page == self.perf_window:
                self.breadcrumb_label.setText("性能")
                # 更新按钮选中状态
                self.perf_btn.setChecked(True)
                self.settings_btn.setChecked(False)
            elif page == self.settings_window:
                self.breadcrumb_label.setText("设置")
                # 更新按钮选中状态
                self.perf_btn.setChecked(False)
                self.settings_btn.setChecked(True)
            
            # 切换页面
            self.stacked_widget.setCurrentWidget(page)
            
        except Exception as e:
            logger.error(f"切换页面失败: {str(e)}")
            self._show_message("错误", f"切换页面失败: {str(e)}", "error")

    def setup_connections(self) -> None:
        """设置信号连接"""
        if auth_manager:
            # 连接认证成功信号
            auth_manager.auth_success.connect(self.update_user_info)
            # 连接登出信号
            auth_manager.logout_success.connect(self._handle_logout)
            # 连接重启信号
            auth_manager.restart_requested.connect(self._handle_restart)

    def _handle_logout(self) -> None:
        """处理登出成功"""
        try:
            # 隐藏主窗口
            self.hide()
            
            # 清理当前窗口状态
            if self.perf_window:
                self.perf_window.close()
            
            # 显示登录窗口
            self._show_auth_window()

        except Exception as e:
            logger.error(f"处理登出事件失败: {str(e)}")

    def _handle_restart(self) -> None:
        """处理重启请求"""
        try:
            logger.info("正在准备重启应用...")
            # 关闭所有窗口
            if self.perf_window:
                self.perf_window.close()
            # 关闭主窗口
            self.close()
            # 退应用，使用特殊的退出码
            from PyQt6.QtWidgets import QApplication
            QApplication.exit(42)
        except Exception as e:
            logger.error(f"处理重启请求失败: {str(e)}")
            QApplication.exit(1)  # 发生错误时使用错误码退出

    def show_settings_menu(self) -> None:
        """显示设置菜单"""
        try:
            settings_menu = QMenu(self)
            settings_menu.setStyleSheet(WindowStyle.get_settings_menu_style())

            # 添加设置菜单项
            version_action = QAction("版本: 1.0.0", self)
            version_action.setEnabled(False)
            settings_menu.addAction(version_action)

            # 显示菜单
            pos = self.sender().mapToGlobal(
                self.sender().rect().bottomRight()
            )
            # 左偏移菜单宽度，使其右对齐
            pos.setX(pos.x() - settings_menu.sizeHint().width())
            # 向上偏移一点，避免遮挡按钮
            pos.setY(pos.y() + 5)

            # 使用 exec 显示菜单
            settings_menu.exec(pos)

        except Exception as e:
            logger.error(f"显示设置菜单失败: {str(e)}")

    def setup_user_menu(self) -> None:
        """设置用户菜单"""
        self.user_menu = QMenu(self)
        self.user_menu.setStyleSheet(MenuStyle.get_user_menu_style())

        # 用户信息（不可点击）
        self.user_info_action = QAction("用户: ", self)
        self.user_info_action.setEnabled(False)
        self.user_menu.addAction(self.user_info_action)

        # 部门信息（不可击）
        self.dept_info_action = QAction("部门: ", self)
        self.dept_info_action.setEnabled(False)
        self.user_menu.addAction(self.dept_info_action)

        self.user_menu.addSeparator()

        # 退出登录
        self.logout_action = QAction("退出登录", self)
        self.logout_action.triggered.connect(self.logout)
        self.user_menu.addAction(self.logout_action)

    def update_user_info(self) -> None:
        """更新用户信息显示"""
        try:
            current_user = auth_manager.get_current_user()
            if current_user:
                # 获取用户名和头像URL
                username = current_user.get('username', '')
                avatar_url = current_user.get('avatar_url', '')

                if avatar_url:
                    # 先尝试加载本地缓存的头像
                    avatar_path = f"data/avatars/{username}.png"
                    if os.path.exists(avatar_path):
                        with open(avatar_path, 'rb') as f:
                            avatar_data = f.read()
                            self.set_avatar(avatar_data)
                    else:
                        # 本地没有缓存时从服务器获取
                        self._load_and_set_avatar(avatar_url, username)
                else:
                    # 如果没有头像URL，显示无权限
                    self.avatar_button.setText("无权限")

                # 更新头像按钮提示
                tooltip = f"{current_user.get('name', '')} ({current_user.get('username', '')})"
                if current_user.get('department'):
                    if isinstance(current_user['department'], dict):
                        tooltip += f"\n{current_user['department'].get('name', '')}"
                    else:
                        tooltip += f"\n{current_user['department']}"
                self.avatar_button.setToolTip(tooltip)

        except Exception as e:
            logger.error(f"更新用户信息显示失败: {str(e)}")

    @pyqtSlot(object)
    def set_avatar(self, avatar_data: bytes) -> None:
        """设置用户头像
        
        Args:
            avatar_data: 头像图片数据
        """
        try:
            if not avatar_data:
                return

            # 从字节数据创建 QPixmap
            pixmap = QPixmap()
            pixmap.loadFromData(avatar_data)

            if pixmap.isNull():
                logger.error("无效的头像数据")
                return

            # 缩放头像到合适的大小
            size = 40  # 头像按钮的大小
            scaled_pixmap = pixmap.scaled(
                size, size,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # 创建圆形遮罩
            mask = QBitmap(scaled_pixmap.size())
            mask.fill(Qt.GlobalColor.transparent)
            painter = QPainter(mask)
            painter.setBrush(Qt.BrushStyle.SolidPattern)
            painter.drawEllipse(0, 0, size, size)
            painter.end()

            # 应用遮罩
            scaled_pixmap.setMask(mask)

            # 设置头像按的图标
            self.avatar_button.setIcon(QIcon(scaled_pixmap))
            self.avatar_button.setIconSize(QSize(size, size))

            # 使用 ModernStyle 中定义的头像样式
            self.avatar_button.setStyleSheet(
                ButtonStyle.get_avatar_button_style() +
                ButtonStyle.get_avatar_size_style(size)
            )

        except Exception as e:
            logger.error(f"设置头像失败: {str(e)}")

    def update_user_avatar(self) -> None:
        """更新用户头像"""
        try:
            user = auth_manager.get_current_user()  # 使用 get_current_user() 方法
            if user:
                username = user.get('username', '')
                avatar_path = f"data/avatars/{username}.png"  # 本地头像保存路径
                
                # 先尝试加载本地缓存的头像
                if os.path.exists(avatar_path):
                    with open(avatar_path, 'rb') as f:
                        avatar_data = f.read()
                        self.set_avatar(avatar_data)
                else:
                    # 本地没有缓存时从服务器获取
                    if avatar_url := user.get('avatar_url'):
                        self._load_and_set_avatar(avatar_url, username)
                    else:
                        # 没有头像时显示用户名首字母
                        name = user.get('name', '')
                        if name:
                            self.avatar_button.setText(name[0].upper())
                        else:
                            self.avatar_button.setText('U')

                # 更新头像按钮提示
                tooltip = f"{user.get('name', '')} ({user.get('username', '')})"
                if user.get('department'):
                    tooltip += f"\n{user.get('department')}"
                self.avatar_button.setToolTip(tooltip)

        except Exception as e:
            logger.error(f"更新用户头像失败: {str(e)}")

    def _load_and_set_avatar(self, avatar_url: str, username: str) -> None:
        """加载并设置头像
        
        Args:
            avatar_url: 头像URL
            username: 用户名，用于本地缓存
        """
        try:
            # 避免重复加载同一个头像
            if hasattr(self, '_current_avatar_url') and self._current_avatar_url == avatar_url:
                return
            self._current_avatar_url = avatar_url

            # 创建异步任务加载头像
            async def load_avatar():
                try:
                    # 获取头像数据
                    avatar_data = await auth_manager.auth_api.get_user_avatar(
                        avatar_url,
                        auth_manager._token
                    )
                    if avatar_data:
                        # 确保avatars目录存在
                        os.makedirs('data/avatars', exist_ok=True)
                        
                        # 保存头像到本地
                        avatar_path = f"data/avatars/{username}.png"
                        with open(avatar_path, 'wb') as f:
                            f.write(avatar_data)
                        
                        # 在主线程中更新UI
                        QMetaObject.invokeMethod(
                            self,
                            "set_avatar",
                            Qt.ConnectionType.QueuedConnection,
                            Q_ARG(object, avatar_data)
                        )
                except Exception as e:
                    logger.error(f"加载头像失败: {str(e)}")

            # 获取事件循环并创建任务
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(load_avatar())
            else:
                loop.run_until_complete(load_avatar())

        except Exception as e:
            logger.error(f"加载头像失败: {str(e)}")

    def init_window_state(self) -> None:
        """初始化窗口状态"""
        try:
            # 确保页面已添加到堆叠窗口
            if self.stacked_widget:
                if self.perf_window not in [self.stacked_widget.widget(i) for i in
                                                 range(self.stacked_widget.count())]:
                    self.stacked_widget.addWidget(self.perf_window)

                # 设置默认显示页面
                self.stacked_widget.setCurrentWidget(self.perf_window)
                if self.perf_btn:
                    self.perf_btn.setChecked(True)

            # 重置窗口大小和位置（如果需要）
            if not self.isMaximized():
                self.resize(1280, 800)  # 设置默认窗口大小
                self.center_window(self)  # 传入 self 作为要居中的窗口

        except Exception as e:
            logger.error(f"初始化窗口状态失败: {str(e)}")

    def center_window(self, window) -> None:
        """将窗口居中显示
        
        Args:
            window: 要居中显示的窗口
        """
        try:
            screen = QApplication.primaryScreen().geometry()
            size = window.geometry()  # 使用传入的窗口
            x = (screen.width() - size.width()) // 2
            y = (screen.height() - size.height()) // 2
            window.move(x, y)
        except Exception as e:
            logger.error(f"居中窗失败: {str(e)}")

    def _show_message(self, title: str, message: str, type: str = "info") -> None:
        """显示消息对话框
        
        Args:
            title: 标题
            message: 消息内容
            type: 消息类型，可选值：info, success, warning, error
        """
        dialog = MsgBox(title, message, type, self)
        dialog.exec()

    def resizeEvent(self, event):
        """处理窗口大小变化事件"""
        super().resizeEvent(event)

    def _show_docs_dialog(self) -> None:
        """显示文档对话框"""
        try:
            from ui.dialogs.docs_dialog import DocsDialog
            dialog = DocsDialog(self)
            dialog.exec()
        except Exception as e:
            logger.error(f"显示文档对话框失败: {str(e)}")
            self._show_message("错误", "显示文档对话框失败", "error")

    def _handle_update_action(self) -> None:
        """处理更新动作"""
        try:
            # 检查是否有更新
            has_update, latest_commit = self.main_manager.check_for_updates()
            
            if not has_update:
                self._show_message("更新检查", "当前已是最新版本", "info")
                return
                
            # 显示确认对话框
            reply = MsgBox(
                "更新确认",
                f"发现新版本 {latest_commit[:7]}\n是否现在更新？",
                "info",
                self,
                True
            ).exec()
            
            if reply == QMessageBox.StandardButton.Yes:
                # 创建事件循环
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.main_manager.update_and_restart(self))
                else:
                    loop.run_until_complete(self.main_manager.update_and_restart(self))
            
        except Exception as e:
            logger.error(f"处理更新操作失败: {str(e)}")
            self._show_message("错误", "更新失败", "error")

    def _on_version_updated(self, new_commit_id: str) -> None:
        """处理版本更新事件"""
        self.version_label.setText(f"当前版本: {new_commit_id[:7]}")
