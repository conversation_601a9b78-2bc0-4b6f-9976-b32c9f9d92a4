"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-11 20:03:47
FilePath: /global_rtc_client/ui/core/main_manager.py
Description: 主管理模块：处理应用级别的操作
"""
import os
import asyncio
import subprocess
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal, Qt
from PyQt6.QtWidgets import QApplication, QProgressDialog
from core.auth_manager import auth_manager
from core.perf_manager import perf_manager
from utils.common.file_utils import ensure_dir
from utils.common.log_utils import get_logger
from core.settings_manager import settings_manager
from ui.components.msg_box import MsgBox


logger = get_logger(__name__)

class MainManager(QObject):
    """主管理器"""

    # 定义信号
    restart_requested = pyqtSignal()  # 重启请求信号
    version_updated = pyqtSignal(str)  # 版本更新信号，参数为新的commit ID

    def __init__(self):
        super().__init__()
        try:            
            # 使用新的路径配置
            self.data_dir = Path(settings_manager.get('storage.paths.base.data', 'data'))
            ensure_dir(str(self.data_dir))
            
            # 连接认证管理器的重启信号到异步重启方法
            auth_manager.restart_requested.connect(lambda: asyncio.run(self._async_restart()))

            # 添加对应用退出信号的处理
            app = QApplication.instance()
            if app:
                app.aboutToQuit.connect(self._handle_app_quit)

            # 创建必要的目录
            for base_dir in settings_manager.get('storage.paths.base', {}).values():
                os.makedirs(base_dir, exist_ok=True)

            # 重启对话框引用
            self._restart_dialog = None
            
            # 获取当前版本
            self.current_commit_id = self._get_current_commit_id()

        except Exception as e:
            logger.error(f"初始化主管理器失败: {str(e)}")
            raise

    def _get_current_commit_id(self) -> str:
        """获取当前commit ID"""
        try:
            rev_parse_cmd = ['git', 'rev-parse', 'HEAD']
            rev_parse_result = subprocess.run(rev_parse_cmd, shell=False, capture_output=True, text=True, check=True)
            return rev_parse_result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"获取commit ID失败: {e.stderr}")
            return "unknown"

    def check_for_updates(self) -> tuple[bool, str]:
        """检查是否有更新可用
        
        Returns:
            tuple[bool, str]: (是否有更新, 最新的commit ID)
        """
        try:
            # 获取当前分支名
            branch_result = subprocess.run(['git', 'rev-parse', '--abbrev-ref', 'HEAD'], shell=False, capture_output=True, text=True, check=True)
            current_branch = branch_result.stdout.strip()
            
            # 获取远程最新状态
            fetch_result = subprocess.run(['git', 'fetch'], shell=False, capture_output=True, text=True, check=True)
            if fetch_result.returncode != 0:
                logger.error(f"获取远程最新状态失败: {fetch_result.stderr}")
                return False, ""
            
            # 获取远程分支最新commit ID
            remote_result = subprocess.run(['git', 'rev-parse', f'origin/{current_branch}'], shell=False, capture_output=True, text=True, check=True)
            latest_commit = remote_result.stdout.strip()
            
            # 比较当前commit ID和最新commit ID
            return latest_commit != self.current_commit_id, latest_commit
        except subprocess.CalledProcessError as e:
            logger.error(f"检查更新失败: {e.stderr}")
            return False, ""

    async def update_and_restart(self, parent=None) -> None:
        """更新应用并重启
        
        Args:
            parent: 父窗口，用于显示进度对话框
        """
        # 显示更新进度对话框
        progress_dialog = QProgressDialog("正在更新...", None, 0, 0, parent)
        progress_dialog.setWindowTitle("更新中")
        progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        progress_dialog.show()
        
        try:
            # 停止所有服务
            await perf_manager.stop_all_services()
            
            # 执行git pull
            try:
                pull_result = subprocess.run(['git', 'pull'], shell=False, capture_output=True, text=True, check=True)
                if pull_result.returncode != 0:
                    logger.error(f"更新失败: {pull_result.stderr}")
            except subprocess.CalledProcessError as e:
                logger.error(f"执行git pull失败: {e.stderr}")
            
            # 获取新的commit ID
            new_commit_id = self._get_current_commit_id()
            self.current_commit_id = new_commit_id
            self.version_updated.emit(new_commit_id)
            
            # 更新成功后重启应用
            progress_dialog.close()
            
            # 显示成功消息并立即重启
            msg_box = MsgBox(
                "更新完成",
                "更新已完成，应用将重启以应用更新",
                "success",
                parent
            )
            msg_box.show()  # 使用 show 而不是 exec，避免阻塞
            
            # 等待一小段时间让用户看到消息
            await asyncio.sleep(1)
            
            # 重启应用
            await self._async_restart()
            
        except Exception as e:
            progress_dialog.close()
            logger.error(f"更新失败: {str(e)}")
            msg_box = MsgBox("更新失败", str(e), "error", parent)
            msg_box.exec()

    async def _async_restart(self) -> None:
        """异步执行重启"""
        try:
            logger.info("准备重启应用...")
            
            # 显示重启对话框
            self._restart_dialog = MsgBox(
                "系统重启",
                "正在重启应用程序，请稍候...",
                "info",
                None,  # 不设置父窗口，确保独立显示
                False  # 不显示取消按钮
            )
            self._restart_dialog.show()  # 使用 show 而不是 exec，保持非模态
            
            # 清理资源并等待完成
            await perf_manager.cleanup_all_resources()
            
            # 等待一小段时间确保所有资源都被清理
            await asyncio.sleep(1)
            
            # 启动新进程
            import subprocess, sys, os
            if getattr(sys, 'frozen', False):
                cmd = [sys.executable]
            else:
                cmd = [sys.executable] + sys.argv
            
            # 使用 start_new_session 确保新进程独立运行
            subprocess.Popen(
                cmd,
                start_new_session=True,
                env=os.environ.copy()
            )
            
            logger.info("应用重启完成，准备退出当前进程")
            
            # 关闭重启对话框
            if self._restart_dialog:
                self._restart_dialog.close()
                self._restart_dialog = None
            
            # 发送重启完成信号
            self.restart_requested.emit()
            
            # 等待一小段时间确保新进程已启动
            await asyncio.sleep(0.5)
            
            # 退出当前进程
            QApplication.instance().quit()
            
        except Exception as e:
            logger.error(f"重启应用失败: {str(e)}")
            
            # 显示错误对话框
            if self._restart_dialog:
                self._restart_dialog.close()
                self._restart_dialog = None
                
            error_dialog = MsgBox(
                "重启失败",
                f"重启应用失败: {str(e)}",
                "error",
                None
            )
            error_dialog.exec()
            raise

    def _handle_app_quit(self):
        """处理应用退出事件 - 这是清理资源的正确位置"""
        try:
            logger.info("应用准备退出，开始清理资源...")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(perf_manager.cleanup_all_resources())
                logger.info("退出清理完成")
            finally:
                loop.close()
        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")

main_manager = MainManager()