"""标签样式定义"""
from ..base.colors import Colors

class LabelStyle:
    @classmethod
    def get_title_label_style(cls) -> str:
        """获取标题标签样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 16px;
                font-weight: 500;
            }}
        """

    @classmethod
    def get_screen_label_style(cls) -> str:
        """获取视频源选择标签样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                line-height: 36px;
                padding: 0;
                margin: 0;
            }}
        """

    @classmethod
    def get_upload_file_label_style(cls) -> str:
        """获取上传文件标签样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_SECONDARY};
                font-size: 14px;
            }}
        """

    @classmethod
    def get_selected_file_label_style(cls) -> str:
        """获取已选择文件标签样式"""
        return f"""
            QLabel {{
                color: {Colors.PRIMARY};
                font-size: 14px;
                font-weight: 500;
            }}
        """

    @classmethod
    def get_perf_window_title_style(cls) -> str:
        """获取性能窗口标题样式"""
        return f"""
            color: {Colors.TEXT_PRIMARY};
            font-size: 18px;
            font-weight: 600;
        """

    @classmethod
    def get_stats_label_style(cls, status_type: str = "stopped") -> str:
        """获取状态标签样式"""
        styles = {
            "running": f"""
                color: white;
                font-size: 13px;
                padding: 4px 12px;
                background: {Colors.SUCCESS};
                border-radius: 12px;
                font-weight: 500;
            """,
            "stopped": f"""
                color: {Colors.TEXT_SECONDARY};
                font-size: 13px;
                padding: 4px 12px;
                background: {Colors.SURFACE_LIGHT};
                border: 1px solid {Colors.BORDER};
                border-radius: 12px;
                font-weight: 500;
            """,
            "error": f"""
                color: white;
                font-size: 13px;
                padding: 4px 12px;
                background: {Colors.ERROR};
                border-radius: 12px;
                font-weight: 500;
            """
        }
        return styles.get(status_type, styles["stopped"]) 

    @classmethod
    def get_service_name_label_style(cls) -> str:
        """获取服务名称标签样式"""
        return f"""
            color: {Colors.TEXT_PRIMARY};
            font-size: 16px;
            font-weight: 500;
        """

    @classmethod
    def get_auth_title_style(cls) -> str:
        """获取认证窗口标题样式"""
        return f"""
            QLabel#titleLabel {{
                font-size: 28px;
                font-weight: 600;
                color: {Colors.TEXT_PRIMARY};
                background: transparent;
            }}
        """

    @classmethod
    def get_auth_subtitle_style(cls) -> str:
        """获取认证窗口副标题样式"""
        return f"""
            QLabel {{
                font-size: 16px;
                color: {Colors.TEXT_SECONDARY};
                background: transparent;
            }}
        """

    @classmethod
    def get_auth_label_style(cls) -> str:
        """获取认证窗口表单标签样式"""
        return f"""
            QLabel {{
                font-size: 14px;
                font-weight: 500;
                color: {Colors.TEXT_PRIMARY};
                background: transparent;
            }}
        """

    @classmethod
    def get_auth_copyright_style(cls) -> str:
        """获取认证窗口版权信息样式"""
        return f"""
            QLabel {{
                font-size: 12px;
                color: {Colors.TEXT_SECONDARY};
                background: transparent;
                margin-top: 16px;
            }}
        """

    @classmethod
    def get_radio_button_style(cls) -> str:
        """获取单选按钮样式"""
        return f"""
            QRadioButton {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 14px;
                spacing: 8px;
                padding: 8px 12px;
            }}
            QRadioButton::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid {Colors.BORDER};
            }}
            QRadioButton::indicator:checked {{
                background: {Colors.PRIMARY};
                border: 2px solid {Colors.PRIMARY};
            }}
            QRadioButton::indicator:unchecked {{
                background: white;
            }}
            QRadioButton:hover {{
                color: {Colors.PRIMARY};
            }}
        """

    @classmethod
    def get_main_window_title_style(cls) -> str:
        """获取主窗口标题样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-size: 20px;
                font-weight: 600;
            }}
        """

    @classmethod
    def get_perf_tips_style(cls) -> str:
        return """
            QLabel {
                color: #666666;
                font-size: 12px;
            }
        """

    @classmethod
    def get_dialog_title_style(cls) -> str:
        """获取对话框标题样式"""
        return f"""
            font-size: 20px;
            font-weight: 600;
            color: {Colors.TEXT_PRIMARY};
        """

    @classmethod
    def get_dialog_content_style(cls) -> str:
        """获取对话框内容样式"""
        return f"""
            font-size: 14px;
            line-height: 1.6;
            color: {Colors.TEXT_SECONDARY};
        """