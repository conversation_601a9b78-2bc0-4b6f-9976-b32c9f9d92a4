'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-11 16:52:59
FilePath: /global_rtc_client/utils/perf/perf_parser.py
Description:
'''
import json
from typing import Dict, Optional

from utils.common.file_utils import find_files_recursively
from utils.common.log_utils import get_logger
from config.constants import PerfToolType

logger = get_logger(__name__)


class PerfParser:
    """ 性能数据解析器 """

    def _get_perf_file_patterns(self, tool_type: Optional[int] = None) -> list:
        """根据性能工具类型获取文件匹配模式列表

        Args:
            tool_type: 性能工具类型，None表示查找所有类型

        Returns:
            list: 文件匹配模式列表
        """
        if tool_type == PerfToolType.DS:
            return ["*ds_avg_perf_data.json", "*trace_avg_data.json", "*byteio_avg_data.json"]
        elif tool_type == PerfToolType.GAMEPERF:
            return ["*gameperf_avg_perf_data.json", "*trace_avg_data.json", "*byteio_avg_data.json"]
        else:
            # 如果没有指定工具类型，查找所有可能的数据源
            return ["*ds_avg_perf_data.json", "*gameperf_avg_perf_data.json", "*trace_avg_data.json", "*byteio_avg_data.json"]

    def _get_perf_file_pattern(self, tool_type: Optional[int] = None) -> str:
        """根据性能工具类型获取文件匹配模式（保持向后兼容）

        Args:
            tool_type: 性能工具类型，None表示查找所有类型

        Returns:
            str: 文件匹配模式
        """
        if tool_type == PerfToolType.DS:
            return "*ds_avg_perf_data.json"
        elif tool_type == PerfToolType.GAMEPERF:
            return "*gameperf_avg_perf_data.json"

    def get_tool_data_dir(self, base_data_dir: str, tool_type: int) -> str:
        """根据性能工具类型获取数据目录路径

        Args:
            base_data_dir: 基础数据目录
            tool_type: 性能工具类型

        Returns:
            str: 工具对应的数据目录路径
        """
        import os
        if tool_type == PerfToolType.DS:
            return os.path.join(base_data_dir, "ds")
        elif tool_type == PerfToolType.GAMEPERF:
            return os.path.join(base_data_dir, "gameperf")
        else:
            return base_data_dir

    def calculate_total_avg_perf_data(self, case_dir: str, tool_type: Optional[int] = None) -> Dict[str, float]:
        """计算用例多次执行的性能数据平均值

        Args:
            case_dir: 用例结果保存的总文件夹路径
            tool_type: 性能工具类型，None表示查找所有类型

        Returns:
            Dict[str, float]: 性能指标平均值字典

        Raises:
            ValueError: 未找到性能数据文件时抛出
        """
        tool_name = PerfToolType.get_name(tool_type) if tool_type else "所有工具"
        logger.debug(f"开始计算性能数据平均值，目录: {case_dir}，工具类型: {tool_name}")

        try:
            # 根据工具类型查找对应的性能数据文件
            file_pattern = self._get_perf_file_pattern(tool_type)
            json_files = find_files_recursively(case_dir, file_pattern)
            logger.debug(f"使用文件模式: {file_pattern}，找到文件: {json_files}")
            if not json_files:
                error_msg = f"在目录 {case_dir} 中未找到{tool_name}的性能数据文件（模式: {file_pattern}）"
                logger.error(error_msg)
                raise ValueError(error_msg)

            logger.info(f"找到 {len(json_files)} 个{tool_name}性能数据文件: {json_files}")
            
            # 使用生成器读取数据，减少内存占用
            def read_perf_data():
                for json_file in json_files:
                    try:
                        with open(json_file, "r") as f:
                            data = json.load(f)
                            yield data
                    except json.JSONDecodeError as e:
                        logger.warning(f"解析文件 {json_file} 失败: {str(e)}")
                    except Exception as e:
                        logger.warning(f"读取文件 {json_file} 失败: {str(e)}")
            
            # 使用 defaultdict 简化数据收集
            from collections import defaultdict
            value_sums = defaultdict(float)
            value_counts = defaultdict(int)
            
            # 收集数据
            for data in read_perf_data():
                logger.debug(f"读取到性能数据文件内容: {data}")
                for key, value in data.items():
                    try:
                        numeric_value = float(value)
                        value_sums[key] += numeric_value
                        value_counts[key] += 1
                    except (TypeError, ValueError):
                        logger.warning(f"忽略非数值型数据: {key}={value}")
            
            # 计算平均值
            total_avg_perf_data = {
                key: round(value_sums[key] / value_counts[key], 2)
                for key in value_sums
                if value_counts[key] > 0
            }
            
            logger.info(f"性能数据平均值: {total_avg_perf_data}")
            return total_avg_perf_data

        except Exception as e:
            logger.error(f"计算性能数据平均值失败: {str(e)}", exc_info=True)
            raise

    def calculate_aggregated_avg_perf_data(self, case_dir: str, tool_type: Optional[int] = None) -> Dict[str, float]:
        """计算用例多次执行的聚合性能数据平均值（支持多数据源）

        Args:
            case_dir: 用例结果保存的总文件夹路径
            tool_type: 性能工具类型，None表示查找所有类型

        Returns:
            Dict[str, float]: 聚合后的性能指标平均值字典

        Raises:
            ValueError: 未找到性能数据文件时抛出
        """
        tool_name = PerfToolType.get_name(tool_type) if tool_type else "所有工具"
        logger.debug(f"开始计算聚合性能数据平均值，目录: {case_dir}，工具类型: {tool_name}")

        try:
            # 根据工具类型获取所有可能的文件模式
            file_patterns = self._get_perf_file_patterns(tool_type)
            all_json_files = []

            # 查找所有匹配的文件
            for pattern in file_patterns:
                json_files = find_files_recursively(case_dir, pattern)
                all_json_files.extend(json_files)

            logger.debug(f"使用文件模式: {file_patterns}，找到文件: {all_json_files}")

            if not all_json_files:
                error_msg = f"在目录 {case_dir} 中未找到{tool_name}的性能数据文件（模式: {file_patterns}）"
                logger.error(error_msg)
                raise ValueError(error_msg)

            logger.info(f"找到 {len(all_json_files)} 个{tool_name}性能数据文件: {all_json_files}")

            # 使用生成器读取数据，减少内存占用
            def read_perf_data():
                for json_file in all_json_files:
                    try:
                        with open(json_file, "r") as f:
                            data = json.load(f)
                            yield data
                    except json.JSONDecodeError as e:
                        logger.warning(f"解析文件 {json_file} 失败: {str(e)}")
                    except Exception as e:
                        logger.warning(f"读取文件 {json_file} 失败: {str(e)}")

            # 使用 defaultdict 简化数据收集
            from collections import defaultdict
            value_sums = defaultdict(float)
            value_counts = defaultdict(int)

            # 收集数据
            for data in read_perf_data():
                logger.debug(f"读取到性能数据文件内容: {data}")
                for key, value in data.items():
                    try:
                        numeric_value = float(value)
                        value_sums[key] += numeric_value
                        value_counts[key] += 1
                    except (TypeError, ValueError):
                        logger.warning(f"忽略非数值型数据: {key}={value}")

            # 计算平均值
            total_avg_perf_data = {
                key: round(value_sums[key] / value_counts[key], 2)
                for key in value_sums
                if value_counts[key] > 0
            }

            logger.info(f"聚合性能数据平均值: {total_avg_perf_data}")
            return total_avg_perf_data

        except Exception as e:
            logger.error(f"计算聚合性能数据平均值失败: {str(e)}", exc_info=True)
            raise

perf_parser = PerfParser()