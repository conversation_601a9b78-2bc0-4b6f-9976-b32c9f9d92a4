"""
设置页面
"""
import os
import asyncio
from typing import Dict, Any
from pathlib import Path
from PyQt6.QtCore import Qt, QSize, QPoint, QMetaObject, Q_ARG, pyqtSlot, QTimer
from PyQt6.QtGui import QIcon
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QLineEdit, QPushButton, QFrame, QScrollArea,
    QFormLayout, QSpinBox, QComboBox, QCheckBox,
    QStackedWidget, QDialog, QFileDialog, QApplication
)

from core.settings_manager import settings_manager
from ui.styles import (
    Colors,
    ButtonStyle,
    LabelStyle,
    WindowStyle,
    FrameStyle,
    InputStyle
)
from ui.styles.containers.settings import SettingsStyle
from ui.components.msg_box import MsgBox
from utils.common.log_utils import get_logger
from ui.components.icon_engine import create_colored_icon
from core.perf_manager import perf_manager

logger = get_logger(__name__)

class NoWheelSpinBox(QSpinBox):
    """禁用滚轮的数字输入框"""
    def wheelEvent(self, event):
        event.ignore()

class SettingsWindow(QWidget):
    """设置页面"""

    def __init__(self):
        super().__init__()
        self.config = self._load_config()
        self.current_section = "basic"  # 当前选中的部分
        self.setup_ui()
        self.setStyleSheet(SettingsStyle.get_container_style())
        
        # 确保在组件完全创建后设置初始状态
        QMetaObject.invokeMethod(self, "_init_section", 
                               Qt.ConnectionType.QueuedConnection)

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        return settings_manager.config

    @pyqtSlot()
    def _init_section(self):
        """初始化选中状态"""
        self._scroll_to_section("basic")

    def setup_ui(self):
        """设置UI"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 左侧导航
        nav_container = QWidget()
        nav_container.setStyleSheet(SettingsStyle.get_nav_container_style())
        nav_layout = QVBoxLayout(nav_container)
        nav_layout.setContentsMargins(16, 24, 16, 24)
        nav_layout.setSpacing(8)  # 增加导航按钮间距

        # 导航按钮
        self.nav_buttons = {}
        sections = [
            ("api", "API设置", "ui/icons/settings/api.svg"),
            ("storage", "存储设置", "ui/icons/settings/storage.svg"),
            ("service", "服务设置", "ui/icons/settings/service.svg"),
            ("repo", "仓库设置", "ui/icons/settings/repo.svg"),
            ("device", "设备设置", "ui/icons/settings/device.svg"),
            ("app", "应用设置", "ui/icons/settings/app.svg"),
            ("case", "用例设置", "ui/icons/settings/case.svg"),
            ("serial", "串口设置", "ui/icons/settings/serial.svg")
        ]
        
        for section_id, section_name, icon_path in sections:
            btn = QPushButton(section_name)
            btn.setFixedHeight(40)
            btn.setIcon(create_colored_icon(icon_path))
            btn.setIconSize(QSize(16, 16))
            btn.setCheckable(True)  # 设置按钮可选中
            btn.setStyleSheet(SettingsStyle.get_nav_button_style(selected=section_id == "api"))  # 设置初始样式
            btn.clicked.connect(lambda checked, s=section_id: self._scroll_to_section(s))
            self.nav_buttons[section_id] = btn
            nav_layout.addWidget(btn)
        
        nav_layout.addStretch()
        main_layout.addWidget(nav_container)

        # 右侧内容区
        right_container = QWidget()
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)

        # 内容区域
        content_container = QWidget()
        content_container.setStyleSheet(SettingsStyle.get_content_container_style())
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(32, 0, 32, 32)
        content_layout.setSpacing(0)

        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setFrameShape(QFrame.Shape.NoFrame)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll.verticalScrollBar().valueChanged.connect(self._update_nav_selection)  # 添加滚动监听
        content_layout.addWidget(scroll)

        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(24)  # 增加各部分之间的间距
        scroll_layout.setContentsMargins(0, 0, 24, 24)

        # 添加各个设置部分
        self.section_widgets = {}
        
        # API设置
        api_widget = self._create_api_settings()
        scroll_layout.addWidget(api_widget)
        self.section_widgets["api"] = api_widget

        # 存储设置
        storage_widget = self._create_storage_settings()
        scroll_layout.addWidget(storage_widget)
        self.section_widgets["storage"] = storage_widget

        # 服务设置
        service_widget = self._create_service_settings()
        scroll_layout.addWidget(service_widget)
        self.section_widgets["service"] = service_widget

        # 仓库设置
        repo_widget = self._create_repo_settings()
        scroll_layout.addWidget(repo_widget)
        self.section_widgets["repo"] = repo_widget

        # 设备设置
        device_widget = self._create_device_settings()
        scroll_layout.addWidget(device_widget)
        self.section_widgets["device"] = device_widget

        # 应用设置
        app_widget = self._create_app_settings()
        scroll_layout.addWidget(app_widget)
        self.section_widgets["app"] = app_widget

        # 用例设置
        case_widget = self._create_case_settings()
        scroll_layout.addWidget(case_widget)
        self.section_widgets["case"] = case_widget

        # 串口设置
        serial_widget = self._create_serial_settings()
        scroll_layout.addWidget(serial_widget)
        self.section_widgets["serial"] = serial_widget

        scroll_layout.addStretch()  # 添加弹性空间
        scroll.setWidget(scroll_content)
        right_layout.addWidget(content_container)

        # 底部按钮容器
        button_container = QWidget()
        button_container.setStyleSheet(SettingsStyle.get_button_container_style())
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.addStretch()

        # 重置按钮
        reset_button = QPushButton("重置")
        reset_button.setFixedSize(120, 40)
        reset_button.setStyleSheet(ButtonStyle.get_settings_reset_button_style())
        reset_button.setCursor(Qt.CursorShape.PointingHandCursor)
        reset_button.clicked.connect(self._reset_settings)
        button_layout.addWidget(reset_button)

        # 保存按钮
        save_button = QPushButton("保存")
        save_button.setFixedSize(120, 40)
        save_button.setStyleSheet(ButtonStyle.get_settings_save_button_style())
        save_button.setCursor(Qt.CursorShape.PointingHandCursor)
        save_button.clicked.connect(self._save_settings)
        button_layout.addWidget(save_button)

        right_layout.addWidget(button_container)
        main_layout.addWidget(right_container)

        # 保存滚动区域引用
        self.scroll_area = scroll

    def _scroll_to_section(self, section_id: str):
        """滚动到指定部分"""
        # 更新按钮选中状态
        for sid, btn in self.nav_buttons.items():
            btn.setStyleSheet(SettingsStyle.get_nav_button_style(selected=sid == section_id))
            btn.setChecked(sid == section_id)
        
        # 滚动到对应部分
        if section_id in self.section_widgets:
            widget = self.section_widgets[section_id]
            self.scroll_area.ensureWidgetVisible(widget)
        
        self.current_section = section_id

    def _create_settings_group(self, title: str) -> tuple[QFrame, QFormLayout]:
        """创建设置组"""
        group = QFrame()
        group.setStyleSheet(SettingsStyle.get_section_container_style())
        layout = QVBoxLayout(group)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        title_label = QLabel(title)
        title_label.setStyleSheet(SettingsStyle.get_group_title_style())
        layout.addWidget(title_label)

        # 添加分割线
        divider = QFrame()
        divider.setFrameShape(QFrame.Shape.HLine)
        divider.setStyleSheet(SettingsStyle.get_divider_style())
        layout.addWidget(divider)

        form_layout = QFormLayout()
        form_layout.setSpacing(24)  # 增加表单项之间的垂直间距
        form_layout.setContentsMargins(0, 8, 0, 8)  # 添加上下内边距
        form_layout.setHorizontalSpacing(32)  # 增加标签和字段之间的水平间距
        form_layout.setLabelAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 设置标签左对齐
        form_layout.setFormAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 设置表单左对齐
        form_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        layout.addLayout(form_layout)

        return group, form_layout

    def _create_form_field_container(self, widget) -> QWidget:
        """创建表单字段容器"""
        container = QWidget()
        container.setStyleSheet(SettingsStyle.get_form_field_container_style())
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        layout.setSpacing(8)

        # 根据组件类型设置固定宽度
        if isinstance(widget, (QLineEdit, QComboBox)):
            widget.setFixedWidth(400)  # 文本输入框和下拉框使用较宽的宽度
        elif isinstance(widget, QSpinBox):
            widget.setFixedWidth(200)  # 数字输入框使用较窄的宽度
        elif isinstance(widget, QCheckBox):
            widget.setFixedWidth(40)   # 复选框使用最小的宽度

        layout.addWidget(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignLeft)  # 设置左对齐
        return container

    def _create_form_label(self, text: str) -> QLabel:
        """创建表单标签"""
        label = QLabel(text)
        label.setStyleSheet(SettingsStyle.get_form_label_style())
        label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)  # 设置左对齐
        return label

    def _create_api_settings(self) -> QWidget:
        """创建API设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # API配置组
        group, form = self._create_settings_group("API配置")
        form.setVerticalSpacing(16)  # 减小表单项之间的垂直间距

        # 超时时间
        timeout = NoWheelSpinBox()
        timeout.setStyleSheet(InputStyle.get_spin_box_style())
        timeout.setRange(1, 300)
        timeout.setValue(self.config.get("api", {}).get("default_timeout", 30))
        timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(timeout))
        settings_manager.register_widget("api.default_timeout", timeout)

        # 重试次数
        retry_count = NoWheelSpinBox()
        retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        retry_count.setRange(0, 10)
        retry_count.setValue(self.config.get("api", {}).get("retry_count", 3))
        retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("重试次数:"), self._create_form_field_container(retry_count))
        settings_manager.register_widget("api.retry_count", retry_count)

        # 重试间隔
        retry_interval = NoWheelSpinBox()
        retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        retry_interval.setRange(1, 60)
        retry_interval.setValue(self.config.get("api", {}).get("retry_interval", 5))
        retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("重试间隔(秒):"), self._create_form_field_container(retry_interval))
        settings_manager.register_widget("api.retry_interval", retry_interval)

        # 当前环境
        env_combo = QComboBox()
        env_combo.setStyleSheet(InputStyle.get_combo_box_style())
        env_combo.addItems(["local", "boe", "prod"])
        env_combo.setCurrentText(self.config.get("api", {}).get("current_env", "prod"))
        form.addRow(self._create_form_label("当前环境:"), self._create_form_field_container(env_combo))
        settings_manager.register_widget("api.current_env", env_combo)

        layout.addWidget(group)

        # 认证配置组
        auth_group, auth_form = self._create_settings_group("认证配置")
        auth_form.setVerticalSpacing(16)  # 减小表单项之间的垂直间距

        # API密钥
        api_key = QLineEdit(self.config.get("api", {}).get("auth", {}).get("api_key", ""))
        api_key.setStyleSheet(InputStyle.get_line_edit_style())
        api_key.setPlaceholderText("请输入API密钥")
        api_key.setEchoMode(QLineEdit.EchoMode.Password)  # 设置为密文显示
        auth_form.addRow(self._create_form_label("API密钥:"), self._create_form_field_container(api_key))
        settings_manager.register_widget("api.auth.api_key", api_key)

        # JWT URL
        jwt_url = QLineEdit(self.config.get("api", {}).get("auth", {}).get("jwt_url", ""))
        jwt_url.setStyleSheet(InputStyle.get_line_edit_style())
        jwt_url.setPlaceholderText("请输入JWT URL")
        auth_form.addRow(self._create_form_label("JWT URL:"), self._create_form_field_container(jwt_url))
        settings_manager.register_widget("api.auth.jwt_url", jwt_url)

        # User URL
        user_url = QLineEdit(self.config.get("api", {}).get("auth", {}).get("user_url", ""))
        user_url.setStyleSheet(InputStyle.get_line_edit_style())
        user_url.setPlaceholderText("请输入User URL")
        auth_form.addRow(self._create_form_label("User URL:"), self._create_form_field_container(user_url))
        settings_manager.register_widget("api.auth.user_url", user_url)

        layout.addWidget(auth_group)

        # GSTest配置组
        gstest_group, gstest_form = self._create_settings_group("GSTest配置")
        gstest_form.setVerticalSpacing(16)

        # Local环境
        local_url = QLineEdit(self.config.get("api", {}).get("gstest", {}).get("local", ""))
        local_url.setStyleSheet(InputStyle.get_line_edit_style())
        local_url.setPlaceholderText("请输入Local环境URL")
        gstest_form.addRow(self._create_form_label("Local环境:"), self._create_form_field_container(local_url))
        settings_manager.register_widget("api.gstest.local", local_url)

        # BOE环境
        boe_url = QLineEdit(self.config.get("api", {}).get("gstest", {}).get("boe", ""))
        boe_url.setStyleSheet(InputStyle.get_line_edit_style())
        boe_url.setPlaceholderText("请输入BOE环境URL")
        boe_url.setEchoMode(QLineEdit.EchoMode.Password)  # 设置为密文显示
        gstest_form.addRow(self._create_form_label("BOE环境:"), self._create_form_field_container(boe_url))
        settings_manager.register_widget("api.gstest.boe", boe_url)

        # Prod环境
        prod_url = QLineEdit(self.config.get("api", {}).get("gstest", {}).get("prod", ""))
        prod_url.setStyleSheet(InputStyle.get_line_edit_style())
        prod_url.setPlaceholderText("请输入Prod环境URL")
        prod_url.setEchoMode(QLineEdit.EchoMode.Password)  # 设置为密文显示
        gstest_form.addRow(self._create_form_label("Prod环境:"), self._create_form_field_container(prod_url))
        settings_manager.register_widget("api.gstest.prod", prod_url)

        layout.addWidget(gstest_group)
        return content

    def _create_storage_settings(self) -> QWidget:
        """创建存储设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # TOS配置组
        group, form = self._create_settings_group("TOS配置")

        # Access Key
        access_key = QLineEdit(self.config.get("storage", {}).get("tos", {}).get("access_key", ""))
        access_key.setStyleSheet(InputStyle.get_line_edit_style())
        access_key.setPlaceholderText("请输入Access Key")
        access_key.setEchoMode(QLineEdit.EchoMode.Password)
        form.addRow(self._create_form_label("Access Key:"), self._create_form_field_container(access_key))
        settings_manager.register_widget("storage.tos.access_key", access_key)

        # Secret Key
        secret_key = QLineEdit(self.config.get("storage", {}).get("tos", {}).get("secret_key", ""))
        secret_key.setStyleSheet(InputStyle.get_line_edit_style())
        secret_key.setPlaceholderText("请输入Secret Key")
        secret_key.setEchoMode(QLineEdit.EchoMode.Password)
        form.addRow(self._create_form_label("Secret Key:"), self._create_form_field_container(secret_key))
        settings_manager.register_widget("storage.tos.secret_key", secret_key)

        # Endpoint
        endpoint = QLineEdit(self.config.get("storage", {}).get("tos", {}).get("endpoint", ""))
        endpoint.setStyleSheet(InputStyle.get_line_edit_style())
        endpoint.setPlaceholderText("请输入Endpoint")
        form.addRow(self._create_form_label("Endpoint:"), self._create_form_field_container(endpoint))
        settings_manager.register_widget("storage.tos.endpoint", endpoint)

        # Bucket
        bucket = QLineEdit(self.config.get("storage", {}).get("tos", {}).get("bucket", ""))
        bucket.setStyleSheet(InputStyle.get_line_edit_style())
        bucket.setPlaceholderText("请输入Bucket名称")
        form.addRow(self._create_form_label("Bucket:"), self._create_form_field_container(bucket))
        settings_manager.register_widget("storage.tos.bucket", bucket)

        # 超时设置
        timeout = NoWheelSpinBox()
        timeout.setStyleSheet(InputStyle.get_spin_box_style())
        timeout.setRange(1, 300)
        timeout.setValue(self.config.get("storage", {}).get("tos", {}).get("timeout", 60))
        timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(timeout))
        settings_manager.register_widget("storage.tos.timeout", timeout)

        # 连接超时
        connect_timeout = NoWheelSpinBox()
        connect_timeout.setStyleSheet(InputStyle.get_spin_box_style())
        connect_timeout.setRange(1, 300)
        connect_timeout.setValue(self.config.get("storage", {}).get("tos", {}).get("connect_timeout", 60))
        connect_timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        connect_timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("连接超时(秒):"), self._create_form_field_container(connect_timeout))
        settings_manager.register_widget("storage.tos.connect_timeout", connect_timeout)

        # 连接池大小
        pool_size = NoWheelSpinBox()
        pool_size.setStyleSheet(InputStyle.get_spin_box_style())
        pool_size.setRange(1, 100)
        pool_size.setValue(self.config.get("storage", {}).get("tos", {}).get("connection_pool_size", 10))
        pool_size.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        pool_size.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("连接池大小:"), self._create_form_field_container(pool_size))
        settings_manager.register_widget("storage.tos.connection_pool_size", pool_size)

        layout.addWidget(group)

        # 路径配置组
        path_group, path_form = self._create_settings_group("路径配置")

        # 基础路径
        base_paths = self.config.get("storage", {}).get("paths", {}).get("base", {})
        for key, value in base_paths.items():
            path_input = QLineEdit(value)
            path_input.setStyleSheet(InputStyle.get_line_edit_style())
            path_input.setPlaceholderText(f"请输入{key}路径")
            path_form.addRow(self._create_form_label(f"{key}路径:"), self._create_form_field_container(path_input))
            settings_manager.register_widget(f"storage.paths.base.{key}", path_input)

        # 文件配置
        files = self.config.get("storage", {}).get("paths", {}).get("files", {})
        for key, value in files.items():
            file_input = QLineEdit(value)
            file_input.setStyleSheet(InputStyle.get_line_edit_style())
            file_input.setPlaceholderText(f"请输入{key}文件名")
            path_form.addRow(self._create_form_label(f"{key}文件:"), self._create_form_field_container(file_input))
            settings_manager.register_widget(f"storage.paths.files.{key}", file_input)

        # 任务路径
        task_paths = self.config.get("storage", {}).get("paths", {}).get("task", {})
        for key, value in task_paths.items():
            task_input = QLineEdit(value)
            task_input.setStyleSheet(InputStyle.get_line_edit_style())
            task_input.setPlaceholderText(f"请输入任务{key}路径")
            path_form.addRow(self._create_form_label(f"任务{key}:"), self._create_form_field_container(task_input))
            settings_manager.register_widget(f"storage.paths.task.{key}", task_input)

        layout.addWidget(path_group)
        return content

    def _create_service_settings(self) -> QWidget:
        """创建服务设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 轮询配置组
        group, form = self._create_settings_group("轮询配置")

        # 设备轮询
        device_poll = NoWheelSpinBox()
        device_poll.setStyleSheet(InputStyle.get_spin_box_style())
        device_poll.setRange(1, 3600)
        device_poll.setValue(self.config.get("poll_interval", {}).get("device", 60))
        device_poll.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        device_poll.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("设备轮询(秒):"), self._create_form_field_container(device_poll))
        settings_manager.register_widget("poll_interval.device", device_poll)

        # 任务轮询
        task_poll = NoWheelSpinBox()
        task_poll.setStyleSheet(InputStyle.get_spin_box_style())
        task_poll.setRange(1, 3600)
        task_poll.setValue(self.config.get("poll_interval", {}).get("task", 5))
        task_poll.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        task_poll.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("任务轮询(秒):"), self._create_form_field_container(task_poll))
        settings_manager.register_widget("poll_interval.task", task_poll)

        # 请求轮询
        request_poll = NoWheelSpinBox()
        request_poll.setStyleSheet(InputStyle.get_spin_box_style())
        request_poll.setRange(1, 3600)
        request_poll.setValue(self.config.get("poll_interval", {}).get("request", 5))
        request_poll.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        request_poll.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("请求轮询(秒):"), self._create_form_field_container(request_poll))
        settings_manager.register_widget("poll_interval.request", request_poll)

        layout.addWidget(group)
        return content

    def _create_device_settings(self) -> QWidget:
        """创建设备设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 设备配置组
        group, form = self._create_settings_group("设备配置")

        # ADB路径
        adb_path = QLineEdit(self.config.get("device", {}).get("adb_path", ""))
        adb_path.setStyleSheet(InputStyle.get_line_edit_style())
        adb_path.setPlaceholderText("请输入ADB路径")
        form.addRow(self._create_form_label("ADB路径:"), self._create_form_field_container(adb_path))
        settings_manager.register_widget("device.adb_path", adb_path)

        # BDC路径
        bdc_path = QLineEdit(self.config.get("device", {}).get("bdc_path", ""))
        bdc_path.setStyleSheet(InputStyle.get_line_edit_style())
        bdc_path.setPlaceholderText("请输入BDC路径")
        form.addRow(self._create_form_label("BDC路径:"), self._create_form_field_container(bdc_path))
        settings_manager.register_widget("device.bdc_path", bdc_path)

        layout.addWidget(group)

        # 电源管理组
        power_group, power_form = self._create_settings_group("电源管理")

        # 检查间隔
        check_interval = NoWheelSpinBox()
        check_interval.setStyleSheet(InputStyle.get_spin_box_style())
        check_interval.setRange(1, 600)
        check_interval.setValue(self.config.get("device", {}).get("power", {}).get("check_interval", 180))
        check_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        check_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        power_form.addRow(self._create_form_label("检查间隔(秒):"), self._create_form_field_container(check_interval))
        settings_manager.register_widget("device.power.check_interval", check_interval)

        # 最大等待时间
        max_wait = NoWheelSpinBox()
        max_wait.setStyleSheet(InputStyle.get_spin_box_style())
        max_wait.setRange(1, 3600)
        max_wait.setValue(self.config.get("device", {}).get("power", {}).get("max_wait_time", 1800))
        max_wait.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        max_wait.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        power_form.addRow(self._create_form_label("最大等待时间(秒):"), self._create_form_field_container(max_wait))
        settings_manager.register_widget("device.power.max_wait_time", max_wait)

        layout.addWidget(power_group)
        return content

    def _create_perf_settings(self) -> QWidget:
        """创建性能设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 性能数据组
        group, form = self._create_settings_group("性能数据")

        # 保存间隔
        save_interval = NoWheelSpinBox()
        save_interval.setStyleSheet(InputStyle.get_spin_box_style())
        save_interval.setRange(1, 300)
        save_interval.setValue(self.config.get("case", {}).get("perf_data", {}).get("save_interval", 60))
        save_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        save_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("保存间隔(秒):"), self._create_form_field_container(save_interval))
        settings_manager.register_widget("case.perf_data.save_interval", save_interval)

        layout.addWidget(group)
        return content

    def _create_repo_settings(self) -> QWidget:
        """创建仓库设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 仓库配置组
        group, form = self._create_settings_group("仓库配置")

        # 应用仓库地址
        app_url = QLineEdit(self.config.get("repo", {}).get("app_url", ""))
        app_url.setStyleSheet(InputStyle.get_line_edit_style())
        app_url.setPlaceholderText("请输入应用仓库地址")
        form.addRow(self._create_form_label("应用仓库:"), self._create_form_field_container(app_url))
        settings_manager.register_widget("repo.app_url", app_url)

        # 性能用例仓库地址
        perf_case_url = QLineEdit(self.config.get("repo", {}).get("perf_case_url", ""))
        perf_case_url.setStyleSheet(InputStyle.get_line_edit_style())
        perf_case_url.setPlaceholderText("请输入性能用例仓库地址")
        form.addRow(self._create_form_label("性能用例仓库:"), self._create_form_field_container(perf_case_url))
        settings_manager.register_widget("repo.perf_case_url", perf_case_url)

        # 上传重试次数
        upload_retry_count = NoWheelSpinBox()
        upload_retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        upload_retry_count.setRange(0, 10)
        upload_retry_count.setValue(self.config.get("repo", {}).get("upload_retry", {}).get("count", 3))
        upload_retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        upload_retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("上传重试次数:"), self._create_form_field_container(upload_retry_count))
        settings_manager.register_widget("repo.upload_retry.count", upload_retry_count)

        # 上传重试间隔
        upload_retry_interval = NoWheelSpinBox()
        upload_retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        upload_retry_interval.setRange(1, 60)
        upload_retry_interval.setValue(self.config.get("repo", {}).get("upload_retry", {}).get("interval", 5))
        upload_retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        upload_retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("上传重试间隔(秒):"), self._create_form_field_container(upload_retry_interval))
        settings_manager.register_widget("repo.upload_retry.interval", upload_retry_interval)

        layout.addWidget(group)
        return content

    def _create_app_settings(self) -> QWidget:
        """创建应用设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 下载配置组
        download_group, download_form = self._create_settings_group("下载配置")

        # 分块大小
        chunk_size = NoWheelSpinBox()
        chunk_size.setStyleSheet(InputStyle.get_spin_box_style())
        chunk_size.setRange(1024, 65536)
        chunk_size.setValue(self.config.get("app", {}).get("download", {}).get("chunk_size", 8192))
        chunk_size.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        chunk_size.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        download_form.addRow(self._create_form_label("分块大小(字节):"), self._create_form_field_container(chunk_size))
        settings_manager.register_widget("app.download.chunk_size", chunk_size)

        # 超时时间
        timeout = NoWheelSpinBox()
        timeout.setStyleSheet(InputStyle.get_spin_box_style())
        timeout.setRange(1, 3600)
        timeout.setValue(self.config.get("app", {}).get("download", {}).get("timeout", 1800))
        timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        download_form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(timeout))
        settings_manager.register_widget("app.download.timeout", timeout)

        # 重试次数
        retry_count = NoWheelSpinBox()
        retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        retry_count.setRange(0, 10)
        retry_count.setValue(self.config.get("app", {}).get("download", {}).get("retry", {}).get("count", 3))
        retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        download_form.addRow(self._create_form_label("重试次数:"), self._create_form_field_container(retry_count))
        settings_manager.register_widget("app.download.retry.count", retry_count)

        # 重试间隔
        retry_interval = NoWheelSpinBox()
        retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        retry_interval.setRange(1, 60)
        retry_interval.setValue(self.config.get("app", {}).get("download", {}).get("retry", {}).get("interval", 5))
        retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        download_form.addRow(self._create_form_label("重试间隔(秒):"), self._create_form_field_container(retry_interval))
        settings_manager.register_widget("app.download.retry.interval", retry_interval)

        layout.addWidget(download_group)

        # 安装配置组
        install_group, install_form = self._create_settings_group("安装配置")

        # 超时时间
        install_timeout = NoWheelSpinBox()
        install_timeout.setStyleSheet(InputStyle.get_spin_box_style())
        install_timeout.setRange(1, 900)
        install_timeout.setValue(self.config.get("app", {}).get("install", {}).get("timeout", 300))
        install_timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        install_timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        install_form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(install_timeout))
        settings_manager.register_widget("app.install.timeout", install_timeout)

        # 重试次数
        install_retry_count = NoWheelSpinBox()
        install_retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        install_retry_count.setRange(0, 10)
        install_retry_count.setValue(self.config.get("app", {}).get("install", {}).get("retry", {}).get("count", 3))
        install_retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        install_retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        install_form.addRow(self._create_form_label("重试次数:"), self._create_form_field_container(install_retry_count))
        settings_manager.register_widget("app.install.retry.count", install_retry_count)

        # 重试间隔
        install_retry_interval = NoWheelSpinBox()
        install_retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        install_retry_interval.setRange(1, 60)
        install_retry_interval.setValue(self.config.get("app", {}).get("install", {}).get("retry", {}).get("interval", 5))
        install_retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        install_retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        install_form.addRow(self._create_form_label("重试间隔(秒):"), self._create_form_field_container(install_retry_interval))
        settings_manager.register_widget("app.install.retry.interval", install_retry_interval)

        layout.addWidget(install_group)

        # 重打包配置组
        repack_group, repack_form = self._create_settings_group("重打包配置")

        # 超时时间
        repack_timeout = NoWheelSpinBox()
        repack_timeout.setStyleSheet(InputStyle.get_spin_box_style())
        repack_timeout.setRange(1, 900)
        repack_timeout.setValue(self.config.get("app", {}).get("repack", {}).get("timeout", 300))
        repack_timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        repack_timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        repack_form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(repack_timeout))
        settings_manager.register_widget("app.repack.timeout", repack_timeout)

        # 重试次数
        repack_retry_count = NoWheelSpinBox()
        repack_retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        repack_retry_count.setRange(0, 10)
        repack_retry_count.setValue(self.config.get("app", {}).get("repack", {}).get("retry", {}).get("count", 3))
        repack_retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        repack_retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        repack_form.addRow(self._create_form_label("重试次数:"), self._create_form_field_container(repack_retry_count))
        settings_manager.register_widget("app.repack.retry.count", repack_retry_count)

        # 重试间隔
        repack_retry_interval = NoWheelSpinBox()
        repack_retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        repack_retry_interval.setRange(1, 60)
        repack_retry_interval.setValue(self.config.get("app", {}).get("repack", {}).get("retry", {}).get("interval", 5))
        repack_retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        repack_retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        repack_form.addRow(self._create_form_label("重试间隔(秒):"), self._create_form_field_container(repack_retry_interval))
        settings_manager.register_widget("app.repack.retry.interval", repack_retry_interval)

        layout.addWidget(repack_group)
        return content

    def _create_case_settings(self) -> QWidget:
        """创建用例设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 运行配置组
        run_group, run_form = self._create_settings_group("运行配置")

        # 超时时间
        run_timeout = NoWheelSpinBox()
        run_timeout.setStyleSheet(InputStyle.get_spin_box_style())
        run_timeout.setRange(1, 7200)
        run_timeout.setValue(self.config.get("case", {}).get("run", {}).get("timeout", 3600))
        run_timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        run_timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        run_form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(run_timeout))
        settings_manager.register_widget("case.run.timeout", run_timeout)

        # 重试次数
        run_retry_count = NoWheelSpinBox()
        run_retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        run_retry_count.setRange(0, 10)
        run_retry_count.setValue(self.config.get("case", {}).get("run", {}).get("retry", {}).get("count", 3))
        run_retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        run_retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        run_form.addRow(self._create_form_label("重试次数:"), self._create_form_field_container(run_retry_count))
        settings_manager.register_widget("case.run.retry.count", run_retry_count)

        # 重试间隔
        run_retry_interval = NoWheelSpinBox()
        run_retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        run_retry_interval.setRange(1, 60)
        run_retry_interval.setValue(self.config.get("case", {}).get("run", {}).get("retry", {}).get("interval", 5))
        run_retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        run_retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        run_form.addRow(self._create_form_label("重试间隔(秒):"), self._create_form_field_container(run_retry_interval))
        settings_manager.register_widget("case.run.retry.interval", run_retry_interval)

        layout.addWidget(run_group)



        # 性能数据组
        perf_data_group, perf_data_form = self._create_settings_group("性能数据")

        # 保存间隔
        save_interval = NoWheelSpinBox()
        save_interval.setStyleSheet(InputStyle.get_spin_box_style())
        save_interval.setRange(1, 300)
        save_interval.setValue(self.config.get("case", {}).get("perf_data", {}).get("save_interval", 60))
        save_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        save_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        perf_data_form.addRow(self._create_form_label("保存间隔(秒):"), self._create_form_field_container(save_interval))
        settings_manager.register_widget("case.perf_data.save_interval", save_interval)

        # 上传重试次数
        upload_retry_count = NoWheelSpinBox()
        upload_retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        upload_retry_count.setRange(0, 10)
        upload_retry_count.setValue(self.config.get("case", {}).get("perf_data", {}).get("upload_retry", {}).get("count", 3))
        upload_retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        upload_retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        perf_data_form.addRow(self._create_form_label("上传重试次数:"), self._create_form_field_container(upload_retry_count))
        settings_manager.register_widget("case.perf_data.upload_retry.count", upload_retry_count)

        # 上传重试间隔
        upload_retry_interval = NoWheelSpinBox()
        upload_retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        upload_retry_interval.setRange(1, 60)
        upload_retry_interval.setValue(self.config.get("case", {}).get("perf_data", {}).get("upload_retry", {}).get("interval", 5))
        upload_retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        upload_retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        perf_data_form.addRow(self._create_form_label("上传重试间隔(秒):"), self._create_form_field_container(upload_retry_interval))
        settings_manager.register_widget("case.perf_data.upload_retry.interval", upload_retry_interval)

        layout.addWidget(perf_data_group)

        # 电池监控组
        battery_group, battery_form = self._create_settings_group("电池监控")

        # 检查间隔
        check_interval = NoWheelSpinBox()
        check_interval.setStyleSheet(InputStyle.get_spin_box_style())
        check_interval.setRange(1, 600)
        check_interval.setValue(self.config.get("case", {}).get("battery", {}).get("check_interval", 180))
        check_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        check_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        battery_form.addRow(self._create_form_label("检查间隔(秒):"), self._create_form_field_container(check_interval))
        settings_manager.register_widget("case.battery.check_interval", check_interval)

        # 检查重试次数
        check_retries = NoWheelSpinBox()
        check_retries.setStyleSheet(InputStyle.get_spin_box_style())
        check_retries.setRange(1, 20)
        check_retries.setValue(self.config.get("case", {}).get("battery", {}).get("check_retries", 10))
        check_retries.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        check_retries.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        battery_form.addRow(self._create_form_label("检查重试次数:"), self._create_form_field_container(check_retries))
        settings_manager.register_widget("case.battery.check_retries", check_retries)

        layout.addWidget(battery_group)
        return content

    def _create_serial_settings(self) -> QWidget:
        """创建串口设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 串口配置组
        group, form = self._create_settings_group("串口配置")

        # 波特率设置
        baud_rate = NoWheelSpinBox()
        baud_rate.setStyleSheet(InputStyle.get_spin_box_style())
        baud_rate.setRange(1200, 115200)
        baud_rate.setValue(self.config.get("serial", {}).get("baud_rate", 9600))
        baud_rate.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        baud_rate.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("波特率:"), self._create_form_field_container(baud_rate))
        settings_manager.register_widget("serial.baud_rate", baud_rate)

        # 超时设置
        timeout = NoWheelSpinBox()
        timeout.setStyleSheet(InputStyle.get_spin_box_style())
        timeout.setRange(1, 10)
        timeout.setValue(int(self.config.get("serial", {}).get("timeout", 0.5) * 10))
        timeout.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        timeout.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("超时时间(秒):"), self._create_form_field_container(timeout))
        settings_manager.register_widget("serial.timeout", timeout)

        # 重试次数
        retry_count = NoWheelSpinBox()
        retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        retry_count.setRange(0, 10)
        retry_count.setValue(self.config.get("serial", {}).get("retry", {}).get("count", 3))
        retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("重试次数:"), self._create_form_field_container(retry_count))
        settings_manager.register_widget("serial.retry.count", retry_count)

        # 重试间隔
        retry_interval = NoWheelSpinBox()
        retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        retry_interval.setRange(1, 60)
        retry_interval.setValue(self.config.get("serial", {}).get("retry", {}).get("interval", 5))
        retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("重试间隔(秒):"), self._create_form_field_container(retry_interval))
        settings_manager.register_widget("serial.retry.interval", retry_interval)

        layout.addWidget(group)

        # 设备配置组
        device_group, device_form = self._create_settings_group("设备配置")

        # Linux设备模式
        linux_patterns = QLineEdit()
        linux_patterns.setStyleSheet(InputStyle.get_line_edit_style())
        linux_patterns.setPlaceholderText("请输入Linux设备模式")
        patterns = self.config.get("serial", {}).get("device", {}).get("patterns", {}).get("linux", "/dev/ttyCH341*")
        # 确保 patterns 是字符串类型
        if isinstance(patterns, list):
            patterns = patterns[0] if patterns else "/dev/ttyCH341*"
        linux_patterns.setText(patterns)
        device_form.addRow(self._create_form_label("Linux设备模式:"), self._create_form_field_container(linux_patterns))
        settings_manager.register_widget("serial.device.patterns.linux", linux_patterns)

        # Darwin设备模式
        darwin_patterns = QLineEdit()
        darwin_patterns.setStyleSheet(InputStyle.get_line_edit_style())
        darwin_patterns.setPlaceholderText("请输入Darwin设备模式")
        patterns = self.config.get("serial", {}).get("device", {}).get("patterns", {}).get("darwin", "/dev/cu.wchusbserial*")
        # 确保 patterns 是字符串类型
        if isinstance(patterns, list):
            patterns = patterns[0] if patterns else "/dev/cu.wchusbserial*"
        darwin_patterns.setText(patterns)
        device_form.addRow(self._create_form_label("Darwin设备模式:"), self._create_form_field_container(darwin_patterns))
        settings_manager.register_widget("serial.device.patterns.darwin", darwin_patterns)

        layout.addWidget(device_group)

        # 继电器配置组
        relay_group, relay_form = self._create_settings_group("继电器配置")

        # 开启指令
        on_packet = QLineEdit()
        on_packet.setStyleSheet(InputStyle.get_line_edit_style())
        on_packet.setPlaceholderText("请输入开启指令")
        on_packet.setText(self.config.get("serial", {}).get("relay", {}).get("packets", {}).get("on", "A0 01 01 A2"))
        relay_form.addRow(self._create_form_label("开启指令:"), self._create_form_field_container(on_packet))
        settings_manager.register_widget("serial.relay.packets.on", on_packet)

        # 关闭指令
        off_packet = QLineEdit()
        off_packet.setStyleSheet(InputStyle.get_line_edit_style())
        off_packet.setPlaceholderText("请输入关闭指令")
        off_packet.setText(self.config.get("serial", {}).get("relay", {}).get("packets", {}).get("off", "A0 01 00 A1"))
        relay_form.addRow(self._create_form_label("关闭指令:"), self._create_form_field_container(off_packet))
        settings_manager.register_widget("serial.relay.packets.off", off_packet)

        # 切换延迟
        switch_delay = NoWheelSpinBox()
        switch_delay.setStyleSheet(InputStyle.get_spin_box_style())
        switch_delay.setRange(1, 10)
        switch_delay.setValue(self.config.get("serial", {}).get("relay", {}).get("switch_delay", 1))
        switch_delay.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        switch_delay.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        relay_form.addRow(self._create_form_label("切换延迟(秒):"), self._create_form_field_container(switch_delay))
        settings_manager.register_widget("serial.relay.switch_delay", switch_delay)

        layout.addWidget(relay_group)
        return content

    def _create_task_settings(self) -> QWidget:
        """创建任务设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 资源配置组
        group, form = self._create_settings_group("资源配置")

        # 视频下载重试次数
        retry_count = NoWheelSpinBox()
        retry_count.setStyleSheet(InputStyle.get_spin_box_style())
        retry_count.setRange(0, 10)
        retry_count.setValue(self.config.get("task", {}).get("resources", {}).get("video", {}).get("download_retry", {}).get("count", 3))
        retry_count.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_count.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("视频下载重试次数:"), self._create_form_field_container(retry_count))
        settings_manager.register_widget("task.resources.video.download_retry.count", retry_count)

        # 视频下载重试间隔
        retry_interval = NoWheelSpinBox()
        retry_interval.setStyleSheet(InputStyle.get_spin_box_style())
        retry_interval.setRange(1, 60)
        retry_interval.setValue(self.config.get("task", {}).get("resources", {}).get("video", {}).get("download_retry", {}).get("interval", 5))
        retry_interval.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
        retry_interval.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
        form.addRow(self._create_form_label("视频下载重试间隔(秒):"), self._create_form_field_container(retry_interval))
        settings_manager.register_widget("task.resources.video.download_retry.interval", retry_interval)

        layout.addWidget(group)
        return content

    def _create_paths_settings(self) -> QWidget:
        """创建路径设置"""
        content = QWidget()
        layout = QVBoxLayout(content)
        layout.setSpacing(24)
        layout.setContentsMargins(0, 0, 0, 0)

        # 路径配置组
        path_group, path_form = self._create_settings_group("路径配置")

        # 基础路径
        base_paths = self.config.get("storage", {}).get("paths", {}).get("base", {})
        for key, value in base_paths.items():
            path_input = QLineEdit(value)
            path_input.setStyleSheet(InputStyle.get_line_edit_style())
            path_input.setPlaceholderText(f"请输入{key}路径")
            path_form.addRow(self._create_form_label(f"{key}路径:"), self._create_form_field_container(path_input))
            settings_manager.register_widget(f"storage.paths.base.{key}", path_input)

        # 文件配置
        files = self.config.get("storage", {}).get("paths", {}).get("files", {})
        for key, value in files.items():
            file_input = QLineEdit(value)
            file_input.setStyleSheet(InputStyle.get_line_edit_style())
            file_input.setPlaceholderText(f"请输入{key}文件名")
            path_form.addRow(self._create_form_label(f"{key}文件:"), self._create_form_field_container(file_input))
            settings_manager.register_widget(f"storage.paths.files.{key}", file_input)

        # 任务路径
        task_paths = self.config.get("storage", {}).get("paths", {}).get("task", {})
        for key, value in task_paths.items():
            task_input = QLineEdit(value)
            task_input.setStyleSheet(InputStyle.get_line_edit_style())
            task_input.setPlaceholderText(f"请输入任务{key}路径")
            path_form.addRow(self._create_form_label(f"任务{key}:"), self._create_form_field_container(task_input))
            settings_manager.register_widget(f"storage.paths.task.{key}", task_input)

        layout.addWidget(path_group)
        return content

    def _save_settings(self):
        """保存设置"""
        try:
            # 保存配置到config.local.json
            if settings_manager.save_settings():
                dialog = MsgBox("保存成功", "设置已保存，应用将重启以应用新的设置", "success", self)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    # 使用QTimer单次触发来执行异步重启
                    # 这样可以避免事件循环冲突问题
                    QTimer.singleShot(0, self._execute_restart)
            else:
                dialog = MsgBox("保存失败", "设置保存失败，请重试", "error", self)
                dialog.exec()
        except Exception as e:
            logger.error(f"保存设置失败: {str(e)}")
            dialog = MsgBox("保存失败", f"设置保存失败: {str(e)}", "error", self)
            dialog.exec()
            
    def _execute_restart(self):
        """执行重启操作"""
        try:
            # 直接重启应用，不需要等待异步操作完成
            QApplication.exit(42)  # 使用特殊的退出码42表示需要重启
        except Exception as e:
            logger.error(f"重启应用失败: {str(e)}")
            self._show_dialog("重启失败", f"应用重启失败: {str(e)}", "error")

    async def _restart_app(self):
        """重启应用程序"""
        try:
            # 停止所有服务，直接在当前事件循环中执行
            try:
                await perf_manager.stop_all_services()
            except Exception as e:
                logger.warning(f"停止服务时发生错误，继续重启: {str(e)}")
            
            # 重启应用
            QApplication.exit(42)  # 使用特殊的退出码42表示需要重启
        except Exception as e:
            logger.error(f"重启应用失败: {str(e)}")
            raise

    def _show_dialog(self, title: str, message: str, dialog_type: str = "info") -> bool:
        """显示对话框并返回用户选择"""
        dialog = MsgBox(title, message, dialog_type, self)
        return dialog.exec() == QDialog.DialogCode.Accepted

    def _reset_settings(self):
        """重置设置"""
        try:
            # 显示确认对话框
            if not self._show_dialog("确认重置", "确定要重置所有设置吗？此操作不可恢复", "warning"):
                return

            # 删除配置文件
            config_path = os.path.join(Path("config"), "config.local.json")
            try:
                if os.path.exists(config_path):
                    os.remove(config_path)
            except Exception as e:
                logger.error(f"删除配置文件失败: {str(e)}")
                self._show_dialog("重置失败", f"删除配置文件失败: {str(e)}", "error")
                return

            # 显示成功对话框
            if self._show_dialog("重置成功", "设置已重置为默认值，应用将重启以应用新的设置", "success"):
                # 使用QTimer单次触发来执行异步重启
                QTimer.singleShot(0, self._execute_restart)

        except Exception as e:
            logger.error(f"重置设置失败: {str(e)}")
            self._show_dialog("重置失败", f"重置设置失败: {str(e)}", "error")

    def _update_nav_selection(self):
        """根据滚动位置更新导航选中状态"""
        try:
            # 获取滚动区域的垂直滚动值
            scroll_value = self.scroll_area.verticalScrollBar().value()
            
            # 获取各个section的位置
            section_positions = {}
            for section_id, widget in self.section_widgets.items():
                # 获取widget相对于scroll_content的位置
                pos = widget.mapTo(self.scroll_area.widget(), QPoint(0, 0))
                section_positions[section_id] = pos.y() - scroll_value

            # 找到最接近顶部的可见section
            visible_section = None
            min_distance = float('inf')
            for section_id, pos in section_positions.items():
                # 如果section在可视区域内或刚好在上方
                if -100 <= pos <= self.scroll_area.height():
                    distance = abs(pos)
                    if distance < min_distance:
                        min_distance = distance
                        visible_section = section_id

            # 更新导航按钮状态
            if visible_section and visible_section != self.current_section:
                self.current_section = visible_section
                for sid, btn in self.nav_buttons.items():
                    btn.setStyleSheet(SettingsStyle.get_nav_button_style(selected=sid == visible_section))

        except Exception as e:
            logger.error(f"更新导航选中状态失败: {str(e)}") 