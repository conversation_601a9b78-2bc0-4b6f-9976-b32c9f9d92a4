from PyQt6.QtGui import QIconEngine, QPainter, QColor, QPixmap, QIcon
from PyQt6.QtCore import Qt, QSize, QRectF
from PyQt6.QtSvg import QSvgRenderer
from PyQt6.QtXml import QDomDocument

class ColoredIconEngine(QIconEngine):
    def __init__(self, filename: str):
        super().__init__()
        self.filename = filename
        with open(filename, 'r') as f:
            self.svg_content = f.read()

    def _get_colored_renderer(self, color: str) -> QSvgRenderer:
        # 替换 SVG 内容中的 currentColor 为指定颜色
        colored_content = self.svg_content.replace('currentColor', color)
        return QSvgRenderer(bytes(colored_content, encoding='utf-8'))

    def paint(self, painter: QPainter, rect, mode: QIcon.Mode, state: QIcon.State):
        # 根据不同状态设置颜色
        if mode in [QIcon.Mode.Selected, QIcon.Mode.Active] or state == QIcon.State.On:
            # 选中、激活或打开状态使用白色
            renderer = self._get_colored_renderer('white')
        elif mode == QIcon.Mode.Disabled:
            # 禁用状态使用灰色
            renderer = self._get_colored_renderer('#C9CDD4')
        else:
            # 默认状态使用蓝色
            renderer = self._get_colored_renderer('#2D8CFF')
        
        # 将 QRect 转换为 QRectF
        bounds = QRectF(rect)
        renderer.render(painter, bounds)

    def pixmap(self, size: QSize, mode: QIcon.Mode, state: QIcon.State) -> QPixmap:
        pixmap = QPixmap(size)
        pixmap.fill(Qt.GlobalColor.transparent)
        painter = QPainter(pixmap)
        self.paint(painter, pixmap.rect(), mode, state)
        painter.end()
        return pixmap

    def clone(self) -> QIconEngine:
        return ColoredIconEngine(self.filename)

def create_colored_icon(filename: str) -> QIcon:
    engine = ColoredIconEngine(filename)
    icon = QIcon(engine)
    # 确保图标能够响应状态变化
    icon.addPixmap(engine.pixmap(QSize(18, 18), QIcon.Mode.Normal, QIcon.State.Off))
    icon.addPixmap(engine.pixmap(QSize(18, 18), QIcon.Mode.Selected, QIcon.State.On))
    icon.addPixmap(engine.pixmap(QSize(18, 18), QIcon.Mode.Active, QIcon.State.On))
    return icon 