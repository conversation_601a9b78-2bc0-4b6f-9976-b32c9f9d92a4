'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-02-11 10:00:00
FilePath: /global_rtc_client/ui/core/settings_manager.py
Description: 设置管理器
'''
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
from utils.common.log_utils import get_logger

logger = get_logger(__name__)

class ConfigManager:
    """配置管理器，负责管理配置文件的读写和更新"""

    def __init__(self):
        self._config: Dict[str, Any] = {}
        self._config_dir = Path("config")
        self._default_config_path = self._config_dir / "config.json"
        self._local_config_path = self._config_dir / "config.local.json"
        self._ensure_local_config()
        self.load_config()

    def _ensure_local_config(self) -> None:
        """确保本地配置文件存在，如果不存在则从默认配置复制"""
        try:
            if not self._local_config_path.exists():
                # 确保目录存在
                self._local_config_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 检查源文件是否存在
                if not self._default_config_path.exists():
                    raise FileNotFoundError(f"默认配置文件不存在: {self._default_config_path}")
                    
                # 复制默认配置到本地
                shutil.copy2(self._default_config_path, self._local_config_path)
                logger.info(f"已从 {self._default_config_path} 创建本地配置文件 {self._local_config_path}")
        except Exception as e:
            logger.error(f"创建本地配置文件失败: {str(e)}")
            raise

    def _sync_config_structure(self, source: Dict[str, Any], target: Dict[str, Any]) -> Dict[str, Any]:
        """同步配置结构，确保目标配置包含源配置的所有键
        
        Args:
            source: 源配置（默认配置）
            target: 目标配置（本地配置）
            
        Returns:
            Dict[str, Any]: 同步后的配置
        """
        result = target.copy()
        
        for key, value in source.items():
            if key not in result:
                # 如果键不存在，直接添加
                result[key] = value
            elif isinstance(value, dict) and isinstance(result[key], dict):
                # 如果都是字典，递归同步
                result[key] = self._sync_config_structure(value, result[key])
            # 如果键存在且类型不同，保留目标配置中的值
        
        # 删除目标配置中多余的键
        keys_to_remove = [key for key in result if key not in source]
        for key in keys_to_remove:
            del result[key]
            
        return result

    def load_config(self) -> None:
        """加载配置文件，优先加载本地配置，并确保配置结构一致"""
        try:
            # 首先加载默认配置
            if not self._default_config_path.exists():
                raise FileNotFoundError("默认配置文件不存在")
                
            with open(self._default_config_path, 'r', encoding='utf-8') as f:
                default_config = json.load(f)
                self._config = default_config
                logger.info("已加载默认配置文件")

            # 如果存在本地配置，则加载并同步结构
            if self._local_config_path.exists():
                with open(self._local_config_path, 'r', encoding='utf-8') as f:
                    local_config = json.load(f)
                    # 同步配置结构
                    synced_config = self._sync_config_structure(default_config, local_config)
                    self._config.update(synced_config)
                    # 保存同步后的配置
                    self.save_config()
                logger.info("已加载并同步本地配置文件")

        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持使用点号访问嵌套配置，如 'api.base_url'
            default: 默认值，当配置项不存在时返回
            
        Returns:
            配置值或默认值
        """
        try:
            # 支持使用点号访问嵌套配置
            keys = key.split('.')
            value = self._config
            for k in keys:
                value = value.get(k, {})
            return value if value != {} else default
        except Exception as e:
            logger.error(f"获取配置失败: {str(e)}")
            return default

    def update(self, key: str, value: Any) -> bool:
        """更新配置值
        
        Args:
            key: 配置键，支持使用点号访问嵌套配置，如 'api.base_url'
            value: 新的配置值
            
        Returns:
            bool: 更新是否成功
        """
        try:
            keys = key.split('.')
            current = self._config
            
            # 遍历到最后一个键之前
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
                
            # 设置最后一个键的值
            current[keys[-1]] = value
            
            # 保存更新后的配置到本地配置文件
            return self.save_config()
            
        except Exception as e:
            logger.error(f"更新配置失败: {str(e)}")
            return False

    def save_config(self) -> bool:
        """保存当前配置到本地配置文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            self._local_config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存到本地配置文件
            with open(self._local_config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            return False

    def reset_config(self) -> bool:
        """重置配置到默认值
        
        Returns:
            bool: 重置是否成功
        """
        try:
            # 从默认配置文件复制到本地配置
            shutil.copy2(self._default_config_path, self._local_config_path)
            # 重新加载配置
            self.load_config()
            logger.info("已重置配置到默认值")
            return True
        except Exception as e:
            logger.error(f"重置配置失败: {str(e)}")
            return False

    @property
    def config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self._config

    @property
    def config_path(self) -> str:
        """获取实际使用的配置文件路径"""
        return str(self._local_config_path if self._local_config_path.exists() else self._default_config_path)

class SettingsManager:
    """设置管理器，负责管理设置页面的配置操作"""

    def __init__(self):
        self._config_manager = ConfigManager()
        self._config = self._config_manager.config
        self._widgets: Dict[str, Any] = {}

    def register_widget(self, key: str, widget: Any) -> None:
        """注册配置项对应的控件
        
        Args:
            key: 配置项键名，支持使用点号访问嵌套配置，如 'api.base_url'
            widget: 对应的控件实例
        """
        self._widgets[key] = widget

    def get_widget(self, key: str) -> Optional[Any]:
        """获取配置项对应的控件
        
        Args:
            key: 配置项键名
            
        Returns:
            对应的控件实例，如果不存在则返回 None
        """
        return self._widgets.get(key)

    def update_widget_value(self, key: str, value: Any) -> None:
        """更新控件的值
        
        Args:
            key: 配置项键名
            value: 新的值
        """
        widget = self.get_widget(key)
        if not widget:
            return

        try:
            # 根据控件类型设置值
            widget_type = widget.__class__.__name__
            if widget_type == 'QLineEdit':
                widget.setText(str(value))
            elif widget_type == 'NoWheelSpinBox':
                widget.setValue(int(value))
            elif widget_type == 'QSpinBox':
                widget.setValue(int(value))
            elif widget_type == 'QComboBox':
                index = widget.findText(str(value))
                if index >= 0:
                    widget.setCurrentIndex(index)
            elif widget_type == 'QCheckBox':
                widget.setChecked(bool(value))
            elif widget_type == 'QLabel':
                widget.setText(str(value))
        except Exception as e:
            logger.error(f"更新控件值失败: {str(e)}")

    def get_widget_value(self, key: str) -> Any:
        """获取控件的当前值
        
        Args:
            key: 配置项键名
            
        Returns:
            控件的当前值
        """
        widget = self.get_widget(key)
        if not widget:
            return None

        try:
            # 根据控件类型获取值
            widget_type = widget.__class__.__name__
            if widget_type == 'QLineEdit':
                return widget.text()
            elif widget_type == 'NoWheelSpinBox':
                return widget.value()
            elif widget_type == 'QSpinBox':
                return widget.value()
            elif widget_type == 'QComboBox':
                return widget.currentText()
            elif widget_type == 'QCheckBox':
                return widget.isChecked()
            elif widget_type == 'QLabel':
                return widget.text()
        except Exception as e:
            logger.error(f"获取控件值失败: {str(e)}")
            return None

    def save_settings(self) -> bool:
        """保存所有设置
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 遍历所有注册的控件，更新配置
            for key, widget in self._widgets.items():
                value = self.get_widget_value(key)
                if value is not None:
                    self._config_manager.update(key, value)
            
            # 保存配置到文件
            return self._config_manager.save_config()
        except Exception as e:
            logger.error(f"保存设置失败: {str(e)}")
            return False

    def reset_settings(self) -> bool:
        """重置所有设置到默认值
        
        Returns:
            bool: 重置是否成功
        """
        try:
            # 重置配置到默认值
            if self._config_manager.reset_config():
                # 更新所有控件的值
                self._config = self._config_manager.config
                for key, widget in self._widgets.items():
                    value = self._config_manager.get(key)
                    self.update_widget_value(key, value)
                return True
            return False
        except Exception as e:
            logger.error(f"重置设置失败: {str(e)}")
            return False

    def load_settings(self) -> None:
        """加载设置到所有控件"""
        try:
            self._config = self._config_manager.config
            for key, widget in self._widgets.items():
                value = self._config_manager.get(key)
                self.update_widget_value(key, value)
        except Exception as e:
            logger.error(f"加载设置失败: {str(e)}")

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持使用点号访问嵌套配置，如 'api.base_url'
            default: 默认值，当配置项不存在时返回
            
        Returns:
            配置值或默认值
        """
        return self._config_manager.get(key, default)

    @property
    def config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self._config_manager.config

# 创建全局设置管理器实例
settings_manager = SettingsManager() 