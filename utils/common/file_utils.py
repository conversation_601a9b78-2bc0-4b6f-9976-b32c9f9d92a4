"""文件操作工具模块"""
import json
import os
import shutil
import sys
from pathlib import Path
from typing import Any, Optional, Dict, Union, List

import yaml

from utils.common.log_utils import get_logger

logger = get_logger(__name__)


def ensure_dir(dir_path: str) -> bool:
    """确保目录存在"""
    try:
        os.makedirs(dir_path, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {str(e)}")
        return False


def read_yaml(yaml_path: Path) -> dict:
    """读取yaml文件"""
    try:
        if not yaml_path.exists():
            return {}
        with open(yaml_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"读取yaml文件失败: {e}")
        return {}


def write_yaml(yaml_path: Path, data: dict) -> None:
    """写入yaml文件"""
    try:
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(data, f, allow_unicode=True)
    except Exception as e:
        logger.error(f"写入yaml文件失败: {e}")


def read_yaml_value(yaml_path: Path, key: str, default: Any = None) -> Any:
    """读取yaml文件的指定字段"""
    yaml_data = read_yaml(yaml_path)
    return yaml_data.get(key, default)


def write_yaml_value(yaml_path: Path, key: str, value: Any) -> None:
    """写入yaml文件的指定字段"""
    yaml_data = read_yaml(yaml_path) or {}
    yaml_data[key] = value
    write_yaml(yaml_path, yaml_data)


def get_tool_path(tool_name: str, default_paths: list = None) -> str:
    """获取工具路径"""
    try:
        if f'{tool_name.upper()}_PATH' in os.environ:
            return os.environ[f'{tool_name.upper()}_PATH']

        tool_path = shutil.which(tool_name)
        if tool_path and os.path.exists(tool_path):
            return tool_path

        if default_paths:
            for path in default_paths:
                if os.path.exists(path):
                    return path

        return tool_name

    except Exception as e:
        logger.error(f"获取{tool_name}路径失败: {e}")
        return tool_name


def get_python_path() -> str:
    """获取Python解释器路径"""
    try:
        return sys.executable
    except Exception as e:
        logger.error(f"获取Python路径失败: {e}")
        return "python"


def get_adb_path() -> str:
    """获取adb工具路径"""
    default_paths = [
        os.path.join(sys.prefix, 'android_device', 'adb', 'tools', 'darwin', 'adb'),
        '/usr/local/bin/adb',
        '/usr/bin/adb',
        '/opt/homebrew/bin/adb'
    ]
    return get_tool_path('adb', default_paths)


def get_bdc_path() -> str:
    """获取bdc工具路径"""
    default_paths = [
        os.path.join(sys.prefix, 'lib', f'python{sys.version_info.major}.{sys.version_info.minor}',
                     'site-packages', 'shoots_ios', 'tools', 'bdc', 'mac', 'bdc'),
        '/usr/local/bin/bdc',
        '/usr/bin/bdc'
    ]
    return get_tool_path('bdc', default_paths)


def read_json(json_path: Union[str, Path]) -> Optional[Dict]:
    """读取JSON文件"""
    try:
        json_path = Path(json_path)
        if not json_path.exists():
            return None

        with open(json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"读取JSON文件失败: {str(e)}")
        return None


def write_json(json_path: Union[str, Path], data: Dict, indent: int = 4) -> bool:
    """写入JSON文件"""
    try:
        json_path = Path(json_path)
        ensure_dir(str(json_path.parent))

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)
        return True
    except Exception as e:
        logger.error(f"写入JSON文件失败: {str(e)}")
        return False


def read_json_value(json_path: Union[str, Path], key: str, default: Any = None) -> Any:
    """读取JSON文件的指定字段"""
    json_data = read_json(json_path)
    return json_data.get(key, default) if json_data else default


def write_json_value(json_path: Union[str, Path], key: str, value: Any) -> bool:
    """写入JSON文件的指定字段"""
    json_data = read_json(json_path) or {}
    json_data[key] = value
    return write_json(json_path, json_data)


def find_files(directory: Union[str, Path], pattern: str) -> List[str]:
    """获取目录下所有匹配的文件路径"""
    try:
        directory = Path(directory)
        if not directory.exists() or not directory.is_dir():
            return []

        matched_files = list(directory.glob(pattern))
        matched_files = [f for f in matched_files if f.is_file()]
        matched_files.sort()
        return [str(f) for f in matched_files]

    except Exception as e:
        logger.error(f"查找文件失败: {str(e)}")
        return []


def find_files_recursively(directory: Union[str, Path], pattern: str) -> List[str]:
    """递归获取目录下所有匹配的文件路径

    Args:
        directory: 要搜索的目录路径
        pattern: 文件匹配模式,如 "*.json"

    Returns:
        List[str]: 匹配文件的路径列表
    """
    try:
        directory = Path(directory)
        if not directory.exists() or not directory.is_dir():
            return []

        # 使用rglob递归搜索所有子目录
        matched_files = list(directory.rglob(pattern))
        matched_files = [f for f in matched_files if f.is_file()]
        matched_files.sort()
        return [str(f) for f in matched_files]

    except Exception as e:
        logger.error(f"递归查找文件失败: {str(e)}")
        return []


def find_first_file_recursively(directory: Union[str, Path], pattern: str) -> Optional[str]:
    """递归获取目录下第一个匹配的文件路径

    Args:
        directory: 要搜索的目录路径
        pattern: 文件匹配模式,如 "*.json"

    Returns:
        Optional[str]: 第一个匹配文件的路径，如果没有找到则返回None
    """
    try:
        directory = Path(directory)
        if not directory.exists() or not directory.is_dir():
            return None

        # 使用rglob递归搜索，找到第一个匹配的文件就返回
        for file_path in directory.rglob(pattern):
            if file_path.is_file():
                return str(file_path)

        return None

    except Exception as e:
        logger.error(f"递归查找第一个文件失败: {str(e)}")
        return None


def write_py_kv(py_path: Union[str, Path], key: str, value: Any) -> bool:
    """
    写入或更新py文件中的变量（键值对）。
    如果变量已存在则更新，否则新增。
    支持字符串、数字、布尔类型。
    """
    try:
        py_path = Path(py_path)
        if not py_path.exists():
            lines = []
        else:
            with open(py_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

        key_line = None
        for idx, line in enumerate(lines):
            if line.strip().startswith(f"{key} ") and '=' in line:
                key_line = idx
                break

        # 格式化value
        if value is None:
            value_str = 'None'
        elif isinstance(value, str):
            value_str = f'"{value}"'
        elif isinstance(value, bool):
            value_str = 'True' if value else 'False'
        else:
            value_str = str(value)

        new_line = f"{key} = {value_str}\n"

        if key_line is not None:
            lines[key_line] = new_line
        else:
            if lines and not lines[-1].endswith('\n'):
                lines[-1] += '\n'
            lines.append(new_line)

        with open(py_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        return True
    except Exception as e:
        logger.error(f"写入py文件变量失败: {str(e)}")
        return False