"""按钮样式定义"""
from typing import Dict, Optional, Union
from dataclasses import dataclass
from ..base.colors import Colors
from ..base.sizes import Sizes
from ..base.fonts import Fonts
from ..base.gradients import Gradients

@dataclass
class ButtonColorScheme:
    """按钮颜色方案"""
    normal_bg: str  # 正常状态背景色
    normal_text: str  # 正常状态文字颜色
    normal_border: str = "transparent"  # 正常状态边框颜色
    hover_bg: str = ""  # 悬停状态背景色
    hover_text: str = ""  # 悬停状态文字颜色
    hover_border: str = ""  # 悬停状态边框颜色
    pressed_bg: str = ""  # 按压状态背景色
    pressed_text: str = ""  # 按压状态文字颜色
    pressed_border: str = ""  # 按压状态边框颜色
    disabled_bg: str = Colors.SURFACE_DARK  # 禁用状态背景色
    disabled_text: str = Colors.TEXT_DISABLED  # 禁用状态文字颜色
    disabled_border: str = "transparent"  # 禁用状态边框颜色
    focus_bg: str = ""  # 焦点状态背景色
    focus_border: str = ""  # 焦点状态边框颜色
    gradient: Optional[str] = None  # 渐变背景（如果设置，将覆盖背景色）

    def __post_init__(self):
        """初始化后处理：填充未设置的值"""
        if not self.hover_bg:
            self.hover_bg = self.normal_bg
        if not self.hover_text:
            self.hover_text = self.normal_text
        if not self.hover_border:
            self.hover_border = self.normal_border
        if not self.pressed_bg:
            self.pressed_bg = self.normal_bg
        if not self.pressed_text:
            self.pressed_text = self.normal_text
        if not self.pressed_border:
            self.pressed_border = self.normal_border
        if not self.focus_bg:
            self.focus_bg = self.normal_bg
        if not self.focus_border:
            self.focus_border = self.normal_border

class ButtonStyle:
    """按钮样式类"""
    
    # 预定义的颜色方案
    class ColorSchemes:
        """预定义的按钮颜色方案"""
        PRIMARY = ButtonColorScheme(
            normal_bg=Colors.PRIMARY,
            normal_text=Colors.TEXT_INVERSE,
            hover_bg=Colors.PRIMARY_HOVER,
            pressed_bg=Colors.PRIMARY_PRESSED,
            focus_border=Colors.PRIMARY
        )
        
        SECONDARY = ButtonColorScheme(
            normal_bg=Colors.PRIMARY_LIGHT,
            normal_text=Colors.PRIMARY,
            normal_border="transparent",
            hover_bg=Colors.BUTTON_HOVER,
            hover_border=Colors.PRIMARY_HOVER,
            pressed_bg=Colors.BUTTON_PRESSED,
            pressed_border=Colors.PRIMARY,
            focus_border=Colors.PRIMARY
        )
        
        ERROR = ButtonColorScheme(
            normal_bg=Colors.ERROR,
            normal_text=Colors.TEXT_INVERSE,
            hover_bg=Colors.ERROR_LIGHT,
            pressed_bg=Colors.ERROR_DARK,
            focus_border=Colors.ERROR
        )
        
        SUCCESS = ButtonColorScheme(
            normal_bg=Colors.SUCCESS,
            normal_text=Colors.TEXT_INVERSE,
            hover_bg=Colors.SUCCESS_LIGHT,
            pressed_bg=Colors.SUCCESS_DARK,
            focus_border=Colors.SUCCESS
        )
        
        GRADIENT_PRIMARY = ButtonColorScheme(
            normal_bg=Colors.PRIMARY,
            normal_text=Colors.TEXT_INVERSE,
            gradient=Gradients.PRIMARY,
            hover_bg=Colors.PRIMARY_HOVER,
            pressed_bg=Colors.PRIMARY_PRESSED,
            focus_border=Colors.PRIMARY
        )

        TRANSPARENT = ButtonColorScheme(
            normal_bg="transparent",
            normal_text=Colors.PRIMARY,
            normal_border="transparent",
            hover_bg=Colors.HOVER,
            pressed_bg=Colors.SELECTED,
            focus_border=Colors.PRIMARY
        )

        TABLE_PRIMARY = ButtonColorScheme(
            normal_bg=Colors.PRIMARY_LIGHT,
            normal_text=Colors.PRIMARY,
            hover_bg=Colors.BUTTON_HOVER,
            hover_border=Colors.PRIMARY_HOVER,
            pressed_bg=Colors.BUTTON_PRESSED,
            pressed_border=Colors.PRIMARY,
            focus_border=Colors.PRIMARY
        )

        TABLE_ERROR = ButtonColorScheme(
            normal_bg=Colors.ERROR_BG,
            normal_text=Colors.ERROR,
            hover_bg=Colors.ERROR_LIGHT,
            hover_border=Colors.ERROR,
            pressed_bg=Colors.ERROR_DARK,
            pressed_border=Colors.ERROR,
            focus_border=Colors.ERROR
        )

        TABLE_SUCCESS = ButtonColorScheme(
            normal_bg=Colors.SUCCESS_BG,
            normal_text=Colors.SUCCESS,
            hover_bg=Colors.SUCCESS_LIGHT,
            hover_border=Colors.SUCCESS,
            pressed_bg=Colors.SUCCESS_DARK,
            pressed_border=Colors.SUCCESS,
            focus_border=Colors.SUCCESS
        )

    @classmethod
    def create_button_style(
        cls,
        color_scheme: ButtonColorScheme,
        size: Dict = Sizes.Button.MEDIUM,
        font_size: str = Fonts.SIZE_NORMAL,
        font_weight: str = Fonts.WEIGHT_MEDIUM,
        custom_style: str = "",
        with_icon: bool = False,
        icon_size: Dict = Sizes.Icon.SMALL
    ) -> str:
        """创建自定义按钮样式
        
        Args:
            color_scheme: 按钮颜色方案
            size: 按钮尺寸配置
            font_size: 字体大小
            font_weight: 字体粗细
            custom_style: 自定义样式（将添加到基本样式之后）
            with_icon: 是否包含图标
            icon_size: 图标尺寸配置（仅当 with_icon 为 True 时有效）
            
        Returns:
            str: 按钮样式字符串
        """
        # 基础样式
        base_style = f"""
            min-width: {size['min_width']}px;
            min-height: {size['min_height']}px;
            padding: {size['padding']};
            border-radius: {size['border_radius']}px;
            font-family: {Fonts.get_system_font_family()};
            font-size: {font_size};
            font-weight: {font_weight};
        """
        
        # 图标样式
        icon_style = f"""
            QPushButton::icon {{
                width: {icon_size['width']}px;
                height: {icon_size['height']}px;
                margin-right: {icon_size['margin_right']}px;
            }}
        """ if with_icon else ""
        
        # 背景样式（支持渐变）
        background = color_scheme.gradient if color_scheme.gradient else color_scheme.normal_bg
        
        return f"""
            QPushButton {{
                background: {background};
                color: {color_scheme.normal_text};
                border: 1px solid {color_scheme.normal_border};
                qproperty-cursor: 'pointing-hand';
                {base_style}
                {custom_style}
            }}
            QPushButton:hover {{
                background: {color_scheme.hover_bg};
                color: {color_scheme.hover_text};
                border: 1px solid {color_scheme.hover_border};
            }}
            QPushButton:pressed {{
                background: {color_scheme.pressed_bg};
                color: {color_scheme.pressed_text};
                border: 1px solid {color_scheme.pressed_border};
            }}
            QPushButton:focus {{
                outline: none;
                background: {color_scheme.focus_bg};
                border: 1px solid {color_scheme.focus_border};
            }}
            QPushButton:disabled {{
                background: {color_scheme.disabled_bg};
                color: {color_scheme.disabled_text};
                border: 1px solid {color_scheme.disabled_border};
                opacity: {Colors.OPACITY_DISABLED};
                qproperty-cursor: 'default';
            }}
            {icon_style}
        """

    @classmethod
    def get_login_button_style(cls) -> str:
        """获取认证按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.PRIMARY,
            size=Sizes.Button.XLARGE,
            font_weight=Fonts.WEIGHT_SEMIBOLD
        )

    @classmethod
    def get_toolbar_button_style(cls) -> str:
        """获取工具栏按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.TRANSPARENT,
            size=Sizes.Button.EXTRA_SMALL,
            custom_style=f"padding: 4px; border-radius: {Sizes.BorderRadius.MEDIUM}px;"
        )

    @classmethod
    def get_avatar_button_style(cls) -> str:
        """获取头像按钮样式"""
        return cls.create_button_style(
            color_scheme=ButtonColorScheme(
                normal_bg="transparent",
                normal_text=Colors.TEXT_PRIMARY,
                normal_border="transparent",
                hover_bg="transparent",
                hover_text=Colors.TEXT_PRIMARY,
                hover_border="transparent",
                pressed_bg="transparent"
            ),
            size=Sizes.Button.MEDIUM,
            font_size=Fonts.SIZE_MEDIUM,
            font_weight=Fonts.WEIGHT_MEDIUM,
            custom_style=f"text-align: center; border-radius: {Sizes.BorderRadius.CIRCLE};"
        )

    @classmethod
    def get_avatar_size_style(cls, size: int) -> str:
        """获取头像大小样式"""
        return cls.create_button_style(
            color_scheme=ButtonColorScheme(
                normal_bg="transparent",
                normal_text=Colors.TEXT_PRIMARY
            ),
            custom_style=f"""
                min-width: {size}px;
                max-width: {size}px;
                min-height: {size}px;
                max-height: {size}px;
            """
        )

    @classmethod
    def get_settings_reset_button_style(cls) -> str:
        """获取设置重置按钮样式"""
        return cls.create_button_style(
            color_scheme=ButtonColorScheme(
                normal_bg=Colors.SURFACE_LIGHT,
                normal_text=Colors.TEXT_SECONDARY,
                normal_border=Colors.BORDER,
                hover_bg=Colors.HOVER,
                hover_border=Colors.BORDER_DARK,
                pressed_bg=Colors.SURFACE_DARK,
                pressed_border=Colors.BORDER_DARK
            ),
            size=Sizes.Button.SMALL,
            font_weight=Fonts.WEIGHT_MEDIUM,
            with_icon=True,
            custom_style="margin-right: 12px;"
        )

    @classmethod
    def get_settings_save_button_style(cls) -> str:
        """获取设置保存按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.PRIMARY,
            size=Sizes.Button.SMALL,
            font_weight=Fonts.WEIGHT_SEMIBOLD,
            with_icon=True
        )

    @classmethod
    def get_serial_config_button_style(cls) -> str:
        """获取串口配置按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.PRIMARY,
            size=Sizes.Button.SMALL,
            font_weight=Fonts.WEIGHT_MEDIUM,
            with_icon=True,
            custom_style="margin: 0 8px;"
        )
    
    @classmethod
    def get_serial_add_button_style(cls) -> str:
        """获取串口配置新增按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.PRIMARY,
            size=Sizes.Button.SMALL,
            font_weight=Fonts.WEIGHT_SEMIBOLD,
            with_icon=True
        )

    @classmethod
    def get_serial_delete_button_style(cls) -> str:
        """获取串口配置删除按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.ERROR,
            size=Sizes.Button.SMALL,
            font_weight=Fonts.WEIGHT_SEMIBOLD,
            with_icon=True
        )
        
    @classmethod
    def get_serial_save_button_style(cls) -> str:
        """获取串口配置保存按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.SUCCESS,
            size=Sizes.Button.SMALL,
            font_weight=Fonts.WEIGHT_SEMIBOLD,
            with_icon=True
        )
    

    @classmethod
    def get_one_key_start_button_style(cls) -> str:
        """获取一键启动按钮样式"""
        return cls.create_button_style(
            color_scheme=cls.ColorSchemes.PRIMARY,
            size=Sizes.Button.LARGE,
            font_weight=Fonts.WEIGHT_SEMIBOLD,
            with_icon=True
        )

    @classmethod
    def get_port_control_button_style(cls, button_type: str = "open") -> str:
        """获取串口控制按钮样式"""
        color_schemes = {
            "open": cls.ColorSchemes.PRIMARY,
            "close": cls.ColorSchemes.ERROR
        }
        
        return cls.create_button_style(
            color_scheme=color_schemes[button_type],
            size=Sizes.Button.TINY,
            font_weight=Fonts.WEIGHT_MEDIUM,
            custom_style="margin: 2px 4px;"
        )