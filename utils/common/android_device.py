"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-11 14:39:44
FilePath: /global_rtc_client/utils/perf/android_device.py
Description: Android设备管理器，包含设备信息收集和控制功能
"""

# 标准库导入
import asyncio
import json
import os
import re
import subprocess
import time
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import List, Optional, Union, Set

# 本地导入
from config.constants import (
    AppInstallMode,
    AppType,
    DeviceConnectType,
    DeviceState,
    PlatformType,
)
from core.auth_manager import auth_manager
from core.settings_manager import settings_manager
from utils.common.file_utils import get_adb_path, get_python_path
from utils.common.log_utils import get_logger

logger = get_logger(__name__)

# 常量定义
DEFAULT_INSTALL_TIMEOUT = 300  # 安装超时时间（秒）
DEFAULT_UNINSTALL_TIMEOUT = 30  # 卸载超时时间（秒）
MIN_REPACK_SIZE = 100 * 1024 * 1024  # 最小重打包文件大小（100MB）
USB_KEYWORDS = frozenset(["adb", "mtp", "rndis", "ptp"])  # USB连接关键字
NETWORK_INTERFACES = ["wlan0", "eth0", "eth1"]  # 网络接口优先级
EXCLUDED_IP_PREFIXES = ("127.", "169.254.")  # 排除的IP地址前缀
# 系统应用前缀，用于判断是否为第三方应用
SYSTEM_PACKAGE_PREFIXES = (
    "android", "com.android", "com.google.android", 
    "com.sec.android", "com.samsung", "com.xiaomi", 
    "com.huawei", "com.vivo", "com.oppo", "com.oneplus",
    "com.miui"
)

class PackageType(Enum):
    """应用类型枚举"""
    ALL = "all"       # 所有应用
    SYSTEM = "system" # 系统应用
    USER = "user"     # 用户应用
    THIRD = "third"   # 第三方应用


@dataclass
class AndroidDeviceInfo:
    """Android设备信息数据类
    
    Attributes:
        name: 设备名称
        udid: 设备唯一标识符
        model: 设备型号
        sys_version: 系统版本
        brand: 设备品牌
        resolution: 屏幕分辨率
        connect_type: 连接类型列表 [1: USB, 2: WiFi]
        ip: IP地址，可选
        sys_type: 系统类型，默认为Android
        serial_port: 串口号，可选
        state: 设备状态，默认为在线
        is_occupied: 是否被占用，默认为0
        user: 使用者，可选
        owner: 所有者，可选
    """
    # 必填字段
    name: str
    udid: str
    model: str
    sys_version: str
    brand: str
    resolution: str
    connect_type: list[int]
    
    # 可选字段
    ip: Optional[str] = None
    serial_port: Optional[str] = None
    user: Optional[str] = None
    owner: Optional[str] = None
    
    # 状态字段
    sys_type: int = PlatformType.ANDROID.value
    state: int = DeviceState.ONLINE.value
    is_occupied: int = 0


class AndroidDevice:
    """Android设备管理器，包含设备信息收集和控制功能"""

    def __init__(self):
        """初始化Android设备管理器"""
        self._init_paths()
        self._init_user()
        
        # 串口配置
        self.serial_baud_rate = settings_manager.get('serial.baud_rate', 9600)
        self.serial_timeout = settings_manager.get('serial.timeout', 0.5)
        self.serial_retry_count = settings_manager.get('serial.retry.count', 3)
        self.serial_retry_interval = settings_manager.get('serial.retry.interval', 5)

        # 重打包配置
        self.repack_timeout = settings_manager.get('app.repack.timeout', 300)
        self.repack_retry_count = settings_manager.get('app.repack.retry.count', 3)
        self.repack_retry_interval = settings_manager.get('app.repack.retry.interval', 5)

        # 安装配置
        self.install_timeout = settings_manager.get('app.install.timeout', 300)
        self.install_retry_count = settings_manager.get('app.install.retry.count', 3)
        self.install_retry_interval = settings_manager.get('app.install.retry.interval', 5)

    def _init_paths(self) -> None:
        """初始化所需的路径"""
        # 获取adb路径
        self.adb_path = settings_manager.get('paths.tools.adb', 'adb') or get_adb_path()

        # 获取Python路径
        self.python_path = get_python_path()
        
        # 加载串口配置路径
        data_dir = settings_manager.get('storage.paths.base.data', 'data')
        serial_file = settings_manager.get('storage.paths.files.serial', 'serial.json')
        self.serial_config_path = os.path.join(data_dir, serial_file)

    def _init_user(self) -> None:
        """初始化用户相关信息"""
        self.operator = ""
        current_user = auth_manager.get_current_user()
        if current_user:
            self.operator = current_user.get('username', '')

    async def get_android_devices(self) -> List[AndroidDeviceInfo]:
        """获取Android设备列表
        
        Returns:
            List[AndroidDeviceInfo]: Android设备信息列表
        """
        try:
            # 获取设备列表
            try:
                process = await asyncio.create_subprocess_exec(
                    self.adb_path, "devices",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                devices_output = stdout.decode()
                
                if process.returncode != 0:
                    logger.error(f"执行 adb devices 命令失败: {stderr.decode()}")
                    return []
                
            except Exception as e:
                logger.error(f"执行 adb devices 命令失败: {str(e)}")
                return []

            if not devices_output:
                return []

            # 解析设备信息
            devices = []
            for line in devices_output.strip().split('\n')[1:]:
                if not line.strip():
                    continue

                parts = line.split('\t')
                if len(parts) != 2:
                    continue

                device_id, state = parts
                if ':' not in device_id and state.strip() == 'device':
                    try:
                        # 使用异步子进程获取设备属性
                        props_process = await asyncio.create_subprocess_exec(
                            self.adb_path, "-s", device_id, "shell", "getprop",
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                        props_stdout, _ = await props_process.communicate()
                        props_output = props_stdout.decode()

                        props = {}
                        for prop_line in props_output.split('\n'):
                            if ':' in prop_line:
                                key, value = prop_line.split(':', 1)
                                props[key.strip('[] ')] = value.strip('[] ')

                        # 异步获取分辨率
                        resolution_process = await asyncio.create_subprocess_exec(
                            self.adb_path, "-s", device_id, "shell", "wm", "size",
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                        resolution_stdout, _ = await resolution_process.communicate()
                        resolution = "unknown"
                        match = re.search(r'Physical size: (\d+x\d+)', resolution_stdout.decode())
                        if match:
                            resolution = match.group(1)

                        # 异步获取IP地址
                        ip = await self._get_device_ip_async(device_id)

                        # 创建设备信息对象
                        device_info = AndroidDeviceInfo(
                            name=props.get('ro.product.model', 'Unknown Device'),
                            udid=device_id,
                            model=props.get('ro.product.model', 'Unknown Model'),
                            sys_version=props.get('ro.build.version.release', 'Unknown Version'),
                            brand=props.get('ro.product.brand', 'Unknown Brand'),
                            resolution=resolution,
                            connect_type=await self.get_connect_type_async(device_id),
                            sys_type=PlatformType.ANDROID.value,
                            state=DeviceState.ONLINE.value,
                            ip=ip,
                            user=self.operator,
                            owner=self.operator
                        )
                        devices.append(device_info)

                    except Exception as e:
                        logger.error(f"获取设备 {device_id} 详细信息失败: {str(e)}")
                        continue

            return devices

        except Exception as e:
            logger.error(f"获取Android设备列表失败: {str(e)}")
            return []

    async def _get_device_ip_async(self, device_id: str) -> Optional[str]:
        """异步获取设备IP地址"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "-s", device_id, "shell", "ip", "addr", "show",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            ip_output = stdout.decode()

            # 按优先级检查不同网络接口
            for interface in NETWORK_INTERFACES:
                pattern = rf"{interface}.*?inet\s+(\d+\.\d+\.\d+\.\d+)"
                match = re.search(pattern, ip_output, re.DOTALL)
                if match:
                    ip = match.group(1)
                    if not ip.startswith(EXCLUDED_IP_PREFIXES):
                        return ip   

            # 如果在指定接口上没找到，尝试获取任何有效的IP地址
            matches = re.finditer(r"inet\s+(\d+\.\d+\.\d+\.\d+)", ip_output)
            for match in matches:
                ip = match.group(1)
                if not ip.startswith(EXCLUDED_IP_PREFIXES):
                    return ip

            logger.warning(f"未找到设备 {device_id} 的有效IP地址")
            return None

        except Exception as e:
            logger.error(f"获取设备 {device_id} IP地址失败: {str(e)}")
            return None

    async def get_connect_type_async(self, device_id: str) -> list[int]:
        """异步获取设备连接方式"""
        connect_types = []
        try:
            # 检查USB连接
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "-s", device_id, "shell", "getprop", "sys.usb.state",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            transport_output = stdout.decode()

            # 如果包含adb、mtp或rndis等关键字,说明是USB连接
            if any(keyword in transport_output.lower() for keyword in USB_KEYWORDS):
                connect_types.append(DeviceConnectType.USB.value)

            # 检查无线连接
            ip = await self._get_device_ip_async(device_id)
            if ip:
                # 检查设备是否已经通过WiFi连接
                process = await asyncio.create_subprocess_exec(
                    self.adb_path, "devices",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                devices_output = stdout.decode()
                
                if devices_output and f"{ip}:5555" in devices_output:
                    connect_types.append(DeviceConnectType.WIFI.value)

            # 如果都没检测到，默认为USB连接
            if not connect_types:
                connect_types.append(DeviceConnectType.USB.value)

            return connect_types

        except Exception as e:
            logger.error(f"获取设备 {device_id} 连接方式失败: {str(e)}")
            return [DeviceConnectType.USB.value]

    def inspect_app(self, apk_path: str) -> Optional[str]:
        """
        检查 APK 文件并返回检查结果。

        Args:
            apk_path: APK 文件路径
        Returns:
            检查结果的 stdout 或 None
        """
        inspect_cmd = [self.python_path, "-m", "shoots_android", "inspect", apk_path]
        try:
            inspect_result = subprocess.run(inspect_cmd, shell=False, capture_output=True, text=True, check=True)
            return inspect_result.stdout
        except subprocess.CalledProcessError as e:
            logger.error(f"[Android] {e.stderr}")
            return None

    def get_package_name_by_app(self, apk_path: str) -> Optional[str]:
        """
        从 APK 文件中获取包名。

        Args:
            apk_path: APK 文件路径
        Returns:
            包名或 None
        """
        if not isinstance(apk_path, str) or not os.path.exists(apk_path):
            logger.error("[Android] 无效的APK路径")
            return None

        try:
            inspect_output = self.inspect_app(apk_path)
            if not inspect_output:
                return None

            package_info = inspect_output.split("Package name:")
            if len(package_info) < 2:
                logger.error("[Android] APK信息中未找到包名")
                return None

            package_name = package_info[1].split("\n")[0].strip()
            return package_name

        except Exception as e:
            logger.error(f"[Android] 获取包名失败: {e}")
            return None

    def get_activity_name_by_app(self, apk_path: str) -> Optional[str]:
        """
        从 APK 文件中获取启动活动名称。

        Args:
            apk_path: APK 文件路径
        Returns:
            启动活动名称或 None
        """
        if not isinstance(apk_path, str) or not os.path.exists(apk_path):
            logger.error("[Android] 无效的APK路径")
            return None

        try:
            inspect_output = self.inspect_app(apk_path)
            if not inspect_output:
                return None

            activity_info = inspect_output.split("Start activity:")
            if len(activity_info) < 2:
                logger.error("[Android] APK信息中未找到启动活动名称")
                return None

            activity_name = activity_info[1].split("\n")[0].strip()
            return activity_name

        except Exception as e:
            logger.error(f"[Android] 获取启动活动名称失败: {e}")
            return None

    def get_app_version_by_app(self, apk_path: str) -> Optional[str]:
        """
        从 APK 文件中获取应用版本号。

        Args:
            apk_path: APK 文件路径
        Returns:
            应用版本号或 None
        """
        if not os.path.exists(apk_path) or not os.path.isfile(apk_path):
            logger.error("[Android] 无效的APK路径")
            return None

        try:
            output = self.inspect_app(apk_path)
            if not output:
                return None

            parts = output.split("Version:")
            if len(parts) < 2:
                logger.warning("[Android] 无法找到版本号")
                return None

            app_version = parts[1].split("\n")[0].strip()
            return app_version

        except Exception as e:
            logger.error(f"[Android] 获取应用版本号失败: {e}")
            return None

    async def repack_app(self, app_path: str, save_dir: str, **kwargs) -> Optional[str]:
        """重打包APK文件
        
        Args:
            app_path: APK文件路径
            save_dir: 保存目录
            **kwargs: 额外参数
            
        Returns:
            str: 重打包后的APK路径，失败返回 None
        """
        try:
            if not os.path.exists(app_path) or not app_path.endswith('.apk'):
                raise ValueError(f"[Android] 无效的APK文件: {app_path}")

            # 构建重打包文件路径
            app_name = os.path.basename(app_path)
            repack_name = f"{os.path.splitext(app_name)[0]}-repack-signed.apk"
            repack_path = os.path.join(save_dir, repack_name)

            # 检查重打包文件是否存在且有效
            if os.path.exists(repack_path) and os.path.getsize(repack_path) > MIN_REPACK_SIZE:
                logger.info(f"[Android] 重打包文件已存在且有效")
                return repack_path

            # 执行重打包命令
            cmd = [
                self.python_path, "-m", "shoots_android", "repack",
                "--debug", app_path
            ]
            logger.info("[Android] 开始执行重打包")

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()
            stdout = stdout.decode() if stdout else ""
            stderr = stderr.decode() if stderr else ""

            if process.returncode == 0 and os.path.exists(repack_path):
                if os.path.getsize(repack_path) > MIN_REPACK_SIZE:
                    logger.info("[Android] 重打包成功")
                    return repack_path
                else:
                    raise Exception(f"[Android] 重打包文件验证失败，文件大小不足")
            else:
                raise Exception(f"[Android] 重打包失败: {stderr}")

        except Exception as e:
            logger.error(f"[Android] 重打包应用失败: {str(e)}")
            return None

    async def install_app(self, device_udid: Union[str, list[str]], app_path: str, **kwargs) -> bool:
        """支持单设备和多设备安装
        
        Args:
            device_udid: 设备ID或设备ID列表
            app_path: 应用路径
            **kwargs: 额外参数
                install_mode: 安装模式
                package_name: 包名
        """
        try:
            if not app_path:
                logger.warning("[Android] APK路径为空，无法安装应用")
                return False

            package_name = kwargs.get('package_name') or self.get_package_name_by_app(app_path)
            if not package_name:
                logger.error("[Android] 无法获取包名")
                return False

            device_udids = [device_udid] if isinstance(device_udid, str) else device_udid
            success_count = 0
            install_mode = kwargs.get("install_mode", AppInstallMode.UNINSTALL_INSTALL.value)

            for udid in device_udids:
                retry_count = 0
                while retry_count < self.install_retry_count:
                    try:
                        if not await self._check_device_connection(udid):
                            logger.error(f"[Android] 设备 {udid} 未连接或未授权")
                            break

                        if install_mode == AppInstallMode.NOT_INSTALL.value:
                            success = True
                        elif install_mode == AppInstallMode.UNINSTALL_INSTALL.value:
                            await self._uninstall_app(udid, package_name)
                            success = await self._execute_single_device_install(udid, app_path)
                        elif install_mode == AppInstallMode.OVERRIDE_INSTALL.value:
                            success = await self._execute_single_device_install(udid, app_path, force_reinstall=True)
                        else:
                            logger.error(f"[Android] 不支持的安装模式: {install_mode}")
                            return False
                        if success:
                            success_count += 1
                            logger.info(f"[Android] 设备 {udid} 安装成功")
                            break

                        logger.error(f"[Android] 安装失败，开始重试 ({retry_count + 1}/{self.install_retry_count})")
                        retry_count += 1
                        if retry_count < self.install_retry_count:
                            await asyncio.sleep(self.install_retry_interval)

                    except Exception as e:
                        logger.error(f"[Android] 安装失败: {str(e)}")
                        retry_count += 1
                        if retry_count < self.install_retry_count:
                            await asyncio.sleep(self.install_retry_interval)

            return success_count == len(device_udids)

        except Exception as e:
            logger.error(f"[Android] 安装应用失败: {str(e)}")
            return False

    async def _execute_single_device_install(self, device_udid: str, app_path: str, force_reinstall: bool = False) -> bool:
        """在单个设备上执行应用安装
        
        Args:
            device_udid: 设备ID
            app_path: 应用路径
            force_reinstall: 是否强制重新安装
        """
        try:
            install_cmd = [self.adb_path, "-s", device_udid, "install"]
            if force_reinstall:
                install_cmd.append("-r")
            install_cmd.append(app_path)

            install_result = await asyncio.create_subprocess_exec(
                *install_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await install_result.communicate()
            stdout = stdout.decode() if stdout else ""
            stderr = stderr.decode() if stderr else ""

            success = "Success" in stdout
            if not success:
                logger.warning(f"[Android] 安装失败: {stderr}")
            return success

        except Exception as e:
            logger.error(f"[Android] 安装应用失败: {str(e)}")
            return False

    async def _check_device_connection(self, device_id: str) -> bool:
        """检查设备连接状态"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "devices",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            devices_output = stdout.decode()

            if not devices_output:
                return False
            
            devices = []
            for line in devices_output.splitlines()[1:]:  # 跳过第一行
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        devices.append(parts[0].strip())

            is_connected = device_id in devices
            if not is_connected:
                logger.warning(f"设备 {device_id} 未连接")
            return is_connected

        except Exception as e:
            logger.error(f"[Android] 检查设备连接状态异常: {str(e)}")
            return False

    async def _uninstall_app(self, device_udid: str, package_name: str) -> bool:
        """卸载应用"""
        try:
            uninstall_cmd = [self.adb_path, "-s", device_udid, "uninstall", package_name]
            uninstall_result = await asyncio.create_subprocess_exec(
                *uninstall_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await uninstall_result.communicate()
            success = "Success" in (stdout.decode() if stdout else "")
            if success:
                logger.info(f"[Android] 卸载应用成功: {package_name}")
            else:
                logger.warning(f"[Android] 卸载应用失败: {package_name}")
            return success

        except Exception as e:
            logger.error(f"[Android] 卸载应用失败: {str(e)}")
            return False

    async def get_installed_packages(self, device_udid: str, package_type: PackageType = PackageType.ALL) -> List[str]:
        """获取已安装的应用列表
        
        Args:
            device_udid: 设备ID
            package_type: 应用类型，可选值：ALL(所有)、SYSTEM(系统)、USER(用户)、THIRD(第三方)
            
        Returns:
            List[str]: 包名列表
        """
        try:
            if not await self._check_device_connection(device_udid):
                logger.error(f"[Android] 设备 {device_udid} 未连接或未授权")
                return []
                
            # 根据类型构建命令
            cmd = [self.adb_path, "-s", device_udid, "shell", "pm", "list", "packages"]
            if package_type == PackageType.SYSTEM:
                cmd.append("-s")  # 系统应用
            elif package_type == PackageType.USER:
                cmd.append("-3")  # 第三方应用
            elif package_type == PackageType.THIRD:
                cmd.append("-3")  # 第三方应用
                
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"[Android] 获取应用列表失败: {stderr.decode() if stderr else ''}")
                return []
                
            # 解析包名列表
            packages_output = stdout.decode() if stdout else ""
            package_names = []
            
            for line in packages_output.splitlines():
                if line.startswith("package:"):
                    package_name = line[8:].strip()  # 去除 "package:" 前缀
                    # 如果是第三方应用类型，需要额外过滤系统应用
                    if package_type == PackageType.THIRD:
                        if not any(package_name.startswith(prefix) for prefix in SYSTEM_PACKAGE_PREFIXES):
                            package_names.append(package_name)
                    else:
                        package_names.append(package_name)
            
            return package_names
            
        except Exception as e:
            logger.error(f"[Android] 获取已安装应用列表失败: {str(e)}")
            return []

    async def uninstall_third_party_apps(self, device_udid: str, whitelist_packages: Set[str] = None) -> bool:
        """卸载除白名单以外的所有第三方应用
        
        Args:
            device_udid: 设备ID
            whitelist_packages: 白名单包名集合，这些应用不会被卸载
            
        Returns:
            bool: 是否卸载成功
        """
        try:
            # 初始化白名单
            if whitelist_packages is None:
                whitelist_packages = set()
                
            # 检查设备连接
            if not await self._check_device_connection(device_udid):
                logger.error(f"[Android] 设备 {device_udid} 未连接或未授权")
                return False
                
            # 获取第三方应用列表
            package_names = await self.get_installed_packages(device_udid, PackageType.THIRD)
            
            # 过滤白名单应用
            package_names = [pkg for pkg in package_names if pkg not in whitelist_packages]
            
            if not package_names:
                logger.info(f"[Android] 设备 {device_udid} 没有需要卸载的第三方应用")
                return True
                
            # 卸载应用
            success_count = 0
            failed_packages = []
            total_count = len(package_names)
            
            logger.info(f"[Android] 开始卸载 {total_count} 个第三方应用")
            
            for package_name in package_names:
                try:
                    if await self._uninstall_app(device_udid, package_name):
                        success_count += 1
                    else:
                        failed_packages.append(package_name)
                except Exception as e:
                    logger.error(f"[Android] 卸载 {package_name} 失败: {str(e)}")
                    failed_packages.append(package_name)
            
            # 输出卸载结果
            if success_count == total_count:
                logger.info(f"[Android] 所有第三方应用卸载成功，总共 {total_count} 个")
                return True
            else:
                logger.warning(f"[Android] 第三方应用卸载完成，成功 {success_count}/{total_count}")
                logger.warning(f"[Android] 卸载失败的应用: {failed_packages}")
                return False
                
        except Exception as e:
            logger.error(f"[Android] 卸载第三方应用失败: {str(e)}")
            return False

    async def push_mapping_files_to_device(self, device_udid: str, jenkins_build_result_url: str,
                                     package_name: str, save_dir: str, app_type: int = AppType.PERF.value) -> bool:
        """推送映射文件到设备
        
        Args:
            device_udid: 设备ID
            jenkins_build_result_url: Jenkins构建结果URL 
            package_name: 包名
            save_dir: 保存目录
            app_type: 应用类型，使用 AppType 枚举值
            
        Returns:
            bool: 是否成功
        """
        try:
            if not jenkins_build_result_url:
                return True

            file_prefix = AppType.get_prefix(app_type)

            # 定义映射文件和目标路径的映射关系
            mapping_files = {
                f'{file_prefix}mapping.txt': f'/data/local/tmp/{package_name}_map.txt',
                f'{file_prefix}class_mapping.txt': f'/data/local/tmp/{package_name}_classmap.txt', 
                f'{file_prefix}resource_mapping.txt': f'/data/local/tmp/{package_name}_resmap.txt'
            }

            # 检查设备连接状态
            if not await self._check_device_connection(device_udid):
                logger.error(f"[Android] 设备 {device_udid} 未连接")
                return False

            # 推送每个映射文件
            for local_file, remote_path in mapping_files.items():
                local_path = os.path.join(save_dir, local_file)
                if not os.path.exists(local_path):
                    logger.warning(f"[Android] 映射文件不存在: {local_path}")
                    continue

                # 使用adb push命令推送文件
                push_cmd = [self.adb_path, "-s", device_udid, "push", local_path, remote_path]
                push_result = await asyncio.create_subprocess_exec(
                    *push_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await push_result.communicate()
                
                if push_result.returncode != 0:
                    logger.error(f"[Android] 推送映射文件失败: {stderr.decode()}")
                    return False

            logger.info(f"[Android] 推送映射文件成功")
            return True

        except Exception as e:
            logger.error(f"[Android] 推送映射文件失败: {str(e)}")
            return False

    async def get_battery_level(self, udid: str) -> int:
        """获取设备电量
        
        Args:
            udid: 设备的唯一标识符
            
        Returns:
            int: 电量百分比，失败返回-1
        """
        try:
            # 修改命令，移除管道符，使用单个命令
            get_battery_level_cmd = [self.adb_path, '-s', udid, 'shell', 'dumpsys', 'battery']
            
            process = await asyncio.create_subprocess_exec(
                *get_battery_level_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 使用正则表达式匹配电量值
                import re
                match = re.search(r'level:\s*(\d+)', stdout.decode())
                if match:
                    power = int(match.group(1))
                    return power
            
            logger.warning(f"[Android] 未能从输出中解析到电量值: {stdout.decode() if stdout else ''}")
            return -1
            
        except Exception as e:
            logger.error(f"[Android] 获取设备 {udid} 电量失败: {str(e)}")
            return -1

    async def set_screen_brightness(self, udid: str, brightness: int) -> bool:
        """设置屏幕亮度"""
        try:
            set_screen_brightness_cmd = [self.adb_path, '-s', udid, 'shell', 'settings', 'put', 'system', 'screen_brightness', str(brightness)]
            process = await asyncio.create_subprocess_exec(
                *set_screen_brightness_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            if process.returncode == 0:
                logger.info(f"[Android] {udid} 设置屏幕亮度成功: {brightness}")
                return True
            else:
                logger.error(f"[Android] {udid} 设置屏幕亮度失败: {stderr.decode()}")
                return False
        except Exception as e:
            logger.error(f"[Android] {udid} 设置屏幕亮度失败: {str(e)}")
            return False
        
    async def close_auto_rotate_screen(self, udid: str) -> bool:
        """关闭指定设备的自动旋转屏幕功能"""
        try:
            close_auto_rotate_screen_cmd = [self.adb_path, '-s', udid, 'shell', 'settings', 'put', 'system', 'accelerometer_rotation', '0']
            process = await asyncio.create_subprocess_exec(
                *close_auto_rotate_screen_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            if process.returncode == 0:
                logger.info(f"[Android] {udid} 关闭自动旋转屏幕成功")
                return True
            else:
                logger.error(f"[Android] {udid} 关闭自动旋转屏幕失败: {stderr.decode()}")
                return False
        except Exception as e:
            logger.error(f"[Android] {udid} 关闭自动旋转屏幕失败: {str(e)}")
            return False

    async def get_screen_state(self, udid: str) -> bool:
        """获取屏幕状态"""
        try:
            get_screen_state_cmd = [self.adb_path, '-s', udid, 'shell', 'dumpsys', 'deviceidle']
            process = await asyncio.create_subprocess_exec(
                *get_screen_state_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            if process.returncode == 0:
                return "mScreenOn=true" in stdout.decode()
            else:
                logger.error(f"[Android] 获取屏幕状态失败: {stderr.decode()}")
                return False
        except Exception as e:
            logger.error(f"[Android] 获取屏幕状态失败: {str(e)}")
            return False

    async def turn_off_screen(self, udid: str) -> bool:
        """熄屏"""
        try:
            if not await self.get_screen_state(udid): 
                return True
            turn_off_screen_cmd = [self.adb_path, '-s', udid, 'shell', 'input', 'keyevent', '26']
            process = await asyncio.create_subprocess_exec(
                *turn_off_screen_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            if process.returncode == 0:
                logger.info(f"[Android] 熄屏成功: device={udid}")
                return True
            else:
                logger.error(f"[Android] 熄屏失败: {stderr.decode()}")
                return False
        except Exception as e:
            logger.error(f"[Android] 熄屏失败: {str(e)}")
            return False
    
    async def turn_on_screen(self, udid: str) -> bool:
        """亮屏"""
        if await self.get_screen_state(udid):
            return True
        turn_on_screen_cmd = [self.adb_path, '-s', udid, 'shell', 'input', 'keyevent', '26']
        try:
            process = await asyncio.create_subprocess_exec(
                *turn_on_screen_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            if process.returncode == 0:
                logger.info(f"[Android] 亮屏成功: device={udid}")
                return True
            else:
                logger.error(f"[Android] 亮屏失败: {stderr.decode()}")
                return False
        except Exception as e:
            logger.error(f"[Android] 亮屏失败: {str(e)}")
            return False

    def set_media_volume(self, udid: str, volume: int) -> bool:
        """设置媒体音量大小，音量值(0-15)"""
        set_media_volume_cmd = [self.adb_path, '-s', udid, 'shell', 'media', 'volume', '--set', str(volume), '--stream', '3']
        try:
            subprocess.run(set_media_volume_cmd, shell=False, capture_output=True, text=True, check=True)
            logger.info(f"[Android] 设置媒体音量成功: device={udid}, volume={volume}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"[Android] 设置媒体音量失败: {e.stderr}")
            return False

    def kill_adb_service(self) -> bool:
        """终止adb服务进程"""
        try:
            subprocess.run([self.adb_path, 'kill-server'], shell=False, capture_output=True, text=True, check=False)
            logger.info("[Android] 终止服务进程成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"[Android] 终止服务进程失败: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"[Android] 终止服务进程异常: {str(e)}")
            return False
        
    def start_adb_service(self) -> bool:
        """启动adb服务进程"""
        try:
            subprocess.run([self.adb_path, 'start-server'], shell=False, capture_output=True, text=True, check=False)
            logger.info("[Android] 启动服务进程成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"[Android] 启动服务进程失败: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"[Android] 启动服务进程异常: {str(e)}")
            return False


# 创建单例实例
android_device = AndroidDevice()

if __name__ == '__main__':
    apk_path = "/Users/<USER>/CodeHub/global-business/global_rtc_client/.tasks/283/393/results/基准包/视频直播/1752229389/case.log"
    jenkins_build_result_url = "https://code.byted.org/byte_rtc/rtc_engine_android/-/jobs/1752229389"
