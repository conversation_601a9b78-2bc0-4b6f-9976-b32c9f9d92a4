'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-11 13:17:52
FilePath: /global_rtc_client/ui/styles/containers/frame.py
Description: 
'''
"""框架样式定义"""
from ..base.colors import Colors
from PyQt6.QtGui import QColor

class FrameStyle:
    @classmethod
    def get_card_style(cls) -> str:
        """获取卡片样式"""
        return f"""
            QFrame {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
            }}
        """

    @classmethod
    def get_screen_container_style(cls) -> str:
        """获取视频源选择容器样式"""
        return f"""
            QFrame {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                margin-bottom: 12px;
            }}
        """

    @classmethod
    def get_upload_frame_style(cls) -> str:
        """获取上传区域框架样式"""
        return f"""
            QFrame {{
                background-color: white;
                border: 2px dashed {Colors.BORDER};
                border-radius: 12px;
                margin-bottom: 16px;
            }}
            QFrame:hover {{
                border-color: {Colors.PRIMARY};
                background-color: #F8F9FF;
            }}
        """

    @classmethod
    def get_button_container_style(cls) -> str:
        """获取按钮容器样式"""
        return f"""
            QWidget {{
                background-color: white;
                border-top: 1px solid {Colors.BORDER};
            }}
        """

    @classmethod
    def get_perf_window_card_style(cls) -> str:
        """获取性能窗口卡片样式"""
        return f"""
            QFrame {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 16px;
            }}
            QFrame:hover {{
                border-color: {Colors.PRIMARY_LIGHT};
            }}
        """

    @classmethod
    def get_separator_style(cls) -> str:
        """获取分隔线样式"""
        return f"""
            background: {Colors.BORDER};
            max-height: 1px;
            margin: 8px 0;
        """

    @classmethod
    def get_shadow_effect_style(cls, blur_radius: int = 20) -> dict:
        """获取阴影效果样式"""
        return {
            'blur_radius': blur_radius,
            'x_offset': 0,
            'y_offset': 4,
            'color': QColor(0, 0, 0, 40)
        } 

    @classmethod
    def get_input_group_style(cls) -> str:
        """获取输入组样式"""
        return f"""
            QFrame {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: 8px;
                padding: 16px;
            }}
            QFrame:hover {{
                border-color: {Colors.PRIMARY_LIGHT};
            }}
        """ 

    @classmethod
    def get_perf_section_style(cls) -> str:
        return """
            QFrame {
                background-color: #FFFFFF;
                border: none;
                border-radius: 8px;
            }
        """

    @classmethod
    def get_auth_form_style(cls) -> str:
        """获取认证表单容器样式"""
        return f"""
            QFrame {{
                background: white;
                border: 1px solid {Colors.BORDER};
                border-radius: 12px;
            }}
            QFrame:hover {{
                border-color: {Colors.PRIMARY_LIGHT};
                background: {Colors.SURFACE_LIGHT};
            }}
        """

    @staticmethod
    def get_side_menu_style() -> str:
        return f"""
            QWidget {{
                background-color: {Colors.BACKGROUND};
                border-right: 1px solid {Colors.BORDER};
                min-width: 80px;
                max-width: 80px;
            }}
        """
    
    @staticmethod
    def get_menu_button_style(selected: bool = False) -> str:
        bg_color = Colors.PRIMARY_LIGHT if selected else "transparent"
        text_color = Colors.PRIMARY if selected else Colors.TEXT_PRIMARY
        return f"""
            QPushButton {{
                background-color: {bg_color};
                border: none;
                padding: 6px 6px;
                text-align: left;
                color: {text_color};
                font-size: 13px;
                border-radius: 4px;
                margin: 1px 2px;
                font-weight: {600 if selected else 400};
            }}
            QPushButton:hover {{
                background-color: {Colors.PRIMARY}15;
                color: {Colors.PRIMARY};
            }}
            QPushButton:pressed {{
                background-color: {Colors.PRIMARY}25;
                padding: 7px 5px 5px 7px;
            }}
        """