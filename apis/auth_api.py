"""
认证相关的OpenAPI实现
"""
from typing import Optional, Dict, List

import aiohttp
import asyncio
from dataclasses import dataclass

from core.settings_manager import settings_manager
from utils.common.log_utils import get_logger

logger = get_logger(__name__)


class AuthError(Exception):
    """认证错误"""
    pass


class AuthAPI:
    """认证API实现"""

    def __init__(self):
        """初始化"""
        try:
            # 从settings获取配置
            self.api_key = settings_manager.get('api.auth.api_key')
            self.jwt_url = settings_manager.get('api.auth.jwt_url')
            self.user_url = settings_manager.get('api.auth.user_url')
            self.timeout = settings_manager.get('api.default_timeout', 30)
            self.retry_count = settings_manager.get('api.retry_count', 3)
            self.retry_interval = settings_manager.get('api.retry_interval', 5)
            self.session: Optional[aiohttp.ClientSession] = None
            self._closing = False

        except Exception as e:
            logger.error(f"AuthAPI 初始化失败: {str(e)}")
            raise

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    def __del__(self):
        """析构函数"""
        if self.session and not self._closing:
            asyncio.create_task(self.close())

    async def _ensure_session(self) -> None:
        """确保会话存在"""
        if not self.session and not self._closing:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)

    async def close(self):
        """关闭会话"""
        if self.session and not self._closing:
            self._closing = True
            await self.session.close()
            self.session = None
            self._closing = False

    async def get_jwt_token(self) -> str:
        """
        获取JWT令牌
        
        Returns:
            str: JWT令牌
            
        Raises:
            AuthError: 获取失败时抛出
        """
        retry_count = 0

        while retry_count < self.retry_count:
            try:
                headers = {
                    "authorization": f"Bearer {self.api_key}"
                }
                await self._ensure_session()
                async with self.session.get(
                        self.jwt_url,
                        headers=headers,
                        ssl=False
                ) as response:
                    if response.status != 200:
                        raise AuthError(f"获取JWT令牌失败: HTTP {response.status}")

                    jwt_token = response.headers.get('x-jwt-token')
                    if not jwt_token:
                        raise AuthError("JWT令牌不存在")

                    return jwt_token

            except Exception as e:
                retry_count += 1
                if retry_count >= self.retry_count:
                    logger.error(f"获取JWT令牌失败: {str(e)}")
                    raise AuthError(f"获取JWT令牌失败: {str(e)}")
                logger.warning(f"获取JWT令牌失败，正在重试 ({retry_count}/{self.retry_count})")
                await asyncio.sleep(self.retry_interval)

    async def get_user_list(self, search_text: str) -> List[str]:
        """
        获取用户列表
        
        Args:
            search_text: 搜索文本
            
        Returns:
            List[str]: 用户名列表
            
        Raises:
            AuthError: 搜索失败时抛出
        """
        retry_count = 0
        jwt_token = await self.get_jwt_token()

        while retry_count < self.retry_count:
            try:
                headers = {
                    'X-Jwt-Token': jwt_token
                }
                params = {
                    'page': 1,
                    'search': search_text
                }

                await self._ensure_session()
                async with self.session.get(
                        self.user_url,
                        params=params,
                        headers=headers,
                        ssl=False
                ) as response:
                    if response.status != 200:
                        raise AuthError(f"获取用户列表失败: HTTP {response.status}")

                    data = await response.json()

                    if not data or 'data' not in data or 'items' not in data['data']:
                        logger.warning("响应数据为空或格式错误")
                        return []

                    # 处理返回的数据
                    users = []
                    for user in data['data']['items']:
                        if isinstance(user, dict) and user.get('username'):
                            users.append(user['username'])

                    return users

            except Exception as e:
                retry_count += 1
                if retry_count >= self.retry_count:
                    logger.error(f"获取用户列表失败: {str(e)}")
                    raise AuthError(f"获取用户列表失败: {str(e)}")
                logger.warning(f"获取用户列表失败，正在重试 ({retry_count}/{self.retry_count})")
                await asyncio.sleep(self.retry_interval)

    async def get_user_info(self, username: str, jwt_token: str) -> Optional[Dict]:
        """
        获取用户信息
        
        Args:
            username: 用户名
            jwt_token: JWT令牌
            
        Returns:
            Dict: 用户信息字典，包含 username、department 等字段
        """
        try:
            headers = {
                'X-Jwt-Token': jwt_token
            }
            params = {
                'usernames': username
            }

            await self._ensure_session()
            async with self.session.get(
                    self.user_url,
                    params=params,
                    headers=headers,
                    ssl=False
            ) as response:
                if response.status != 200:
                    logger.error(f"获取用户信息失败: HTTP {response.status}")
                    return None

                data = await response.json()
                if not data or 'error_code' not in data or data['error_code'] != 0:
                    logger.error("获取用户信息响应格式错误")
                    return None

                if not data.get('data', {}).get('items'):
                    logger.warning(f"未找到用户信息: {username}")
                    return None

                user_data = data['data']['items'][0]

                # 处理用户信息
                result = {
                    'username': user_data.get('username'),
                    'name': user_data.get('name'),
                    'email': user_data.get('email'),
                    'department': user_data.get('department', {}),
                    'avatar_url': user_data.get('avatar_url'),
                    'en_name': user_data.get('en_name')
                }
                return result

        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return None

    async def get_user_avatar(self, avatar_url: str, jwt_token: str) -> bytes:
        """
        获取用户头像
        
        Args:
            avatar_url: 头像URL
            jwt_token: JWT令牌
            
        Returns:
            bytes: 头像数据
            
        Raises:
            AuthError: 获取失败时抛出
        """
        retry_count = 0

        while retry_count < self.retry_count:
            try:
                headers = {
                    'X-Jwt-Token': jwt_token
                }

                await self._ensure_session()
                async with self.session.get(
                        avatar_url,
                        headers=headers,
                        ssl=False
                ) as response:
                    if response.status != 200:
                        raise AuthError(f"获取用户头像失败: HTTP {response.status}")

                    data = await response.read()
                    return data

            except Exception as e:
                retry_count += 1
                if retry_count >= self.retry_count:
                    logger.error(f"获取用户头像失败: {str(e)}")
                    raise AuthError(f"获取用户头像失败: {str(e)}")
                logger.warning(f"获取用户头像失败，正在重试 ({retry_count}/{self.retry_count})")

auth_api = AuthAPI()