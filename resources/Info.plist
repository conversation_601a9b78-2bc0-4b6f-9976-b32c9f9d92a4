<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- 应用基本信息 -->
    <key>CFBundleName</key>
    <string>GB</string>
    
    <key>CFBundleDisplayName</key>
    <string>GB</string>
    
    <key>CFBundleIdentifier</key>
    <string>com.global-business.gb</string>
    
    <key>CFBundleVersion</key>
    <string>2.0.0</string>
    
    <key>CFBundleShortVersionString</key>
    <string>2.0.0</string>
    
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    
    <key>CFBundleSignature</key>
    <string>GBPT</string>
    
    <!-- 应用图标 -->
    <key>CFBundleIconFile</key>
    <string>app_icon</string>
    
    <!-- 应用描述 -->
    <key>CFBundleGetInfoString</key>
    <string>Global Business Client v2.0.0</string>
    
    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2025 Global Business Team. All rights reserved.</string>
    
    <!-- 应用类别 -->
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.developer-tools</string>
    
    <!-- 高分辨率支持 -->
    <key>NSHighResolutionCapable</key>
    <true/>
    
    <!-- 支持的架构 -->
    <key>LSArchitecturePriority</key>
    <array>
        <string>arm64</string>
        <string>x86_64</string>
    </array>
    
    <!-- 最低系统版本 -->
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    
    <!-- 应用执行信息 -->
    <key>CFBundleExecutable</key>
    <string>GB</string>
    
    <!-- 文档类型支持 -->
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeName</key>
            <string>Performance Test Data</string>
            <key>CFBundleTypeExtensions</key>
            <array>
                <string>json</string>
                <string>log</string>
            </array>
            <key>CFBundleTypeRole</key>
            <string>Viewer</string>
        </dict>
    </array>
    
    <!-- URL Schemes -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>GB Protocol</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>gb</string>
            </array>
        </dict>
    </array>
    
    <!-- 应用传输安全 -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
    
    <!-- 权限请求 -->
    <key>NSCameraUsageDescription</key>
    <string>GB needs camera access for device testing.</string>
    
    <key>NSMicrophoneUsageDescription</key>
    <string>GB needs microphone access for audio testing.</string>
    
    <key>NSNetworkVolumesUsageDescription</key>
    <string>GB needs network access for performance testing.</string>
</dict>
</plist>
